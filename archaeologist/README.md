# Contract Archaeologist 🏺

> **"Most people chase narratives. You will chase forgotten money."**

## 🧠 The Mindset

**You are not a builder.**
**You are not a trader.**
**You are a forager in the ruins of the chain.**

The Contract Archaeologist operates with a **cold, quiet, and deeply dangerous** mindset. While others chase hype and narratives, you methodically extract value from the digital ruins that others have abandoned.

**Dust. Silence. Extraction.**

---

Contract Archaeologist is a production-ready automated system that discovers and extracts funds from forgotten, abandoned, or misconfigured smart contracts across multiple blockchains. It embodies the true archaeologist mindset: **relentless, silent, and surgical** in its approach to finding forgotten money.

## 🏺 Archaeologist Mode Features

- 🔍 **Enhanced Discovery**: 50x increased contract scanning (1000+ vs 20 contracts)
- 💰 **Balance-Aware Targeting**: Prioritizes contracts with ETH/token balances
- 🎯 **Strategic Parameter Fuzzing**: Function-specific parameter strategies to unlock hidden funds
- 🔓 **Bytecode Decompilation**: Analyzes unverified contracts others ignore
- ⚡ **Proper Profitability**: Fixed gas calculations with realistic 2x profit thresholds
- 🌐 **Unified API**: Etherscan API v2 support for 50+ chains with single key
- 👻 **Ghost Mode**: Silent operations with archaeologist-specific logging
- 🧬 **Relic Analysis**: Deep dissection of abandoned contract structures

## 🎯 The Hunt

**You live in the past to eat in the present.**

Target the ruins others abandoned:
- 💀 **Dead DAOs**: Forgotten `rageQuit()` and `exit()` functions
- 🏺 **Legacy Vaults**: Abandoned `withdraw()` and `claim()` mechanisms
- 💰 **Unclaimed Airdrops**: Open claim gates with no expiration
- 🔓 **Unverified Contracts**: Hidden value in bytecode others can't read
- ⚰️ **Deprecated Protocols**: `finalize()` and `selfDestruct()` with payouts
- 🌫️ **Forgotten LPs**: Stale liquidity with claimable fees
- 🕳️ **Misconfigured Access**: Broken ownership and authorization

**Every contract is a relic to dissect.**

## 🏗️ Excavation Pipeline

**The archaeologist follows a systematic excavation protocol:**

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   DISCOVER  │───▶│   ANALYZE   │───▶│   SIMULATE  │───▶│   ASSESS    │───▶│   EXTRACT   │
│             │    │             │    │             │    │             │    │             │
│ • 1000+ scan│    │ • Target    │    │ • Strategic │    │ • 2x profit │    │ • Silent    │
│ • Bytecode  │    │   functions │    │   fuzzing   │    │   threshold │    │   execution │
│ • Balance   │    │ • Relic     │    │ • Ghost     │    │ • Risk      │    │ • Flashbots │
│   checking  │    │   analysis  │    │   simulation│    │   assessment│    │   protection│
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

**Cold. Methodical. Profitable.**

### 🔧 Excavation Tools

#### 1. **Relic Database** (`src/database.ts`)
- Silent tracking of all discovered contracts
- Marks targets for future exploitation
- Documents extraction history and success rates

#### 2. **Discovery Engine** (`src/fetcher.ts`) 🏺
- **ARCHAEOLOGIST MODE**: 50x enhanced discovery
- Scans 1000+ contracts vs 20 (traditional)
- Balance-aware targeting (prioritizes contracts with ETH)
- Bytecode decompilation for unverified contracts
- Unified Etherscan API v2 for 50+ chains

#### 3. **Relic Analyzer** (`src/filter.ts`)
- Identifies target function signatures in ruins
- Categorizes by exploitation potential
- Prioritizes based on abandonment indicators

#### 4. **Ghost Simulator** (`src/simulator.ts`) 👻
- **Strategic parameter fuzzing** for hidden exploits
- Function-specific parameter strategies
- Silent simulation with no trace
- Tests edge cases others miss

#### 5. **Profit Calculator** (`src/evaluator.ts`) ⚡
- **FIXED**: Proper gas cost calculation (was completely wrong)
- 2x profit threshold for execution
- Risk assessment for ghost operations

#### 6. **Silent Executor** (`src/executor.ts`)
- Ghost mode transaction execution
- Flashbots integration for stealth
- No celebration, just extraction

#### 7. **Excavation Controller** (`src/scanner.ts`)
- Orchestrates systematic excavation
- Continuous surveillance mode
- Cold, methodical operation

## 🏺 Begin Your Archaeological Expedition

### Prerequisites for the Hunt
- Node.js 18+ (your excavation tools)
- Yarn package manager (archaeologists use yarn)
- **Single Etherscan API key** (works for 50+ chains via API v2)
- RPC endpoints (your eyes into the ruins)
- Private key (your extraction mechanism)

### Installation

```bash
# Clone the archaeological site
git clone <repository-url>
cd archaeologist

# Install excavation tools
yarn install

# Prepare your ghost configuration
cp .env.example .env

# Configure for silent operation
nano .env
```

### Ghost Configuration

Configure your `.env` for silent operations:

```env
# ARCHAEOLOGIST ESSENTIALS
ETHERSCAN_API_KEY=your_single_api_key_for_all_chains
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_key
PRIVATE_KEY=your_extraction_key

# GHOST MODE SETTINGS
FLASHBOTS_RPC_URL=https://rpc.flashbots.net
SCAN_BATCH_SIZE=1000  # ARCHAEOLOGIST MODE: Increased from 100
SIMULATION_TIMEOUT=10000  # More time for deep analysis
MAX_GAS_PRICE=20000000000  # 20 gwei baseline

# SURVEILLANCE SETTINGS
LOG_LEVEL=info  # Set to 'debug' for detailed relic analysis
```

**Note**: With Etherscan API v2, one key works for all 50+ supported chains.

### Excavation Sites (Supported Chains)

| Chain | Chain ID | Relic Density | Status |
|-------|----------|---------------|--------|
| Ethereum | 1 | 🏺🏺🏺🏺🏺 | ✅ Primary hunting ground |
| Arbitrum | 42161 | 🏺🏺🏺 | ✅ Rich L2 ruins |
| Base | 8453 | 🏺🏺 | ✅ Fresh excavation site |
| Optimism | 10 | 🏺🏺🏺 | ✅ Established ruins |
| Polygon | 137 | 🏺🏺 | ✅ Diverse artifacts |

**50+ additional chains supported via Etherscan API v2**

## 🏺 Archaeological Operations

### Excavation Commands

```bash
# SYSTEMATIC EXCAVATION: Scan all chains for forgotten money
yarn dev scan

# DEEP ANALYSIS: Examine specific relic
yarn dev contract ****************************************** 1

# CONTINUOUS SURVEILLANCE: Hunt every 30 minutes
yarn dev continuous 30

# GHOST MODE: Silent monitoring
yarn dev monitor

# EXTRACTION: Execute profitable discoveries
yarn dev execute

# INTELLIGENCE: View excavation statistics
yarn dev stats

# RELIC VERIFICATION: Test all function signatures
node verify-signatures.js
```

### Archaeologist Mindset Commands

```bash
# Start your expedition
yarn dev scan

# The system will display:
# 🏺 CONTRACT ARCHAEOLOGIST
# You are not a builder.
# You are not a trader.
# You are a forager in the ruins of the chain.
#
# Most people chase narratives.
# You chase forgotten money.
#
# Dust. Silence. Extraction.
```

### 🌐 Web Dashboard

The web dashboard provides a beautiful interface for monitoring your Contract Archaeologist:

```bash
# Start the dashboard
npm run dashboard

# Access at http://localhost:3000
```

**Dashboard Features:**
- 📊 Real-time statistics and metrics
- 🔄 Start/stop scanning and monitoring
- 📈 Chain status and block tracking
- 🎯 Recent exploits and pending opportunities
- 🔍 Manual contract scanning
- 📝 Live system logs
- 🚀 One-click exploit execution

### 🧪 Testing

```bash
# Run test suite
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### 🔍 Advanced Fuzzing

The system includes 15+ fuzzing strategies for deep vulnerability discovery:

**Fuzzing Strategies:**
- **Boundary Value Testing**: Test edge cases and limits
- **Overflow/Underflow**: Integer overflow detection
- **Reentrancy**: Reentrancy attack vectors
- **Access Control**: Authorization bypass attempts
- **Token Amount Testing**: Various token amounts and dust
- **Timestamp Manipulation**: Time-based vulnerabilities
- **Array Testing**: Array boundary and length attacks
- **Signature Testing**: Signature verification bypasses
- **Price Oracle Testing**: Price manipulation vectors
- **Rounding Testing**: Precision loss vulnerabilities

### 🔄 Real-time Monitoring

Block-by-block monitoring for immediate exploit detection:

```bash
# Start real-time monitoring
npm run monitor

# Monitor catches new contracts as they're deployed
# Automatically scans and executes profitable exploits
```

### Development Mode

```bash
# Run TypeScript directly
npm run dev

# Build for production
npm run build

# Run built version
npm start
```

## 🎯 Target Relics (Function Signatures)

**CRITICAL**: All signatures have been verified and corrected for precision targeting.

| Function | 4-byte Signature | Priority | Relic Type |
|----------|-----------------|----------|------------|
| `claim()` | `0x4e71d92d` | 1 | 💰 Airdrops, loyalty drops |
| `withdraw()` | `0x3ccfd60b` | 1 | 🏺 Legacy vaults (no params) |
| `withdraw(uint256)` | `0x2e1a7d4d` | 1 | 🏺 Legacy vaults (with amount) |
| `exit()` | `0xe9fad8ee` | 1 | ⚰️ DAOs, LP exits |
| `rageQuit(uint256)` | `0x65f969f6` | 2 | 💀 DAO rage exits |
| `collect()` | `0xe5225381` | 2 | 💎 DEX fees |
| `finalize()` | `0x4bb278f3` | 2 | 🔓 Unlock mechanisms |
| `redeem()` | `0xbe040fb0` | 2 | 🎫 Tokenized positions |
| `claimTokens()` | `0x48c54b9d` | 1 | 🪙 Token claiming |
| `emergencyWithdraw()` | `0xdb2e21bc` | 1 | 🚨 Emergency mechanisms |
| `close()` | `0x43d726d6` | 3 | 🚪 Position closures |
| `selfDestruct()` | `0x9cb8a26a` | 3 | 💥 Legacy contracts with payout |

**All signatures mathematically verified. No false positives.**

## 🔍 Intelligence Features

### Keyword Prioritization
Contracts containing these keywords get priority boost:
- `airdrop`, `vault`, `rageQuit`, `merkle`
- `dao`, `yield`, `farming`, `staking`
- `lp`, `liquidity`, `claim`, `withdraw`

### Contract Categorization
- **DAOs**: Contracts with governance and ragequit functions
- **Vaults**: Yield farming and staking contracts
- **Airdrops**: Distribution and claim contracts

### Simulation Strategies
- Multiple parameter combinations
- Fallback to zero values and addresses
- Timeout protection (5 seconds default)
- Gas estimation for profitability

## 📊 Database Schema

```sql
-- Contract storage
CREATE TABLE contracts (
    address TEXT PRIMARY KEY,
    name TEXT,
    abi TEXT,
    source_code TEXT,
    chain_id INTEGER,
    block_number INTEGER,
    timestamp INTEGER
);

-- Simulation results
CREATE TABLE simulations (
    contract_address TEXT,
    function_name TEXT,
    success BOOLEAN,
    return_data TEXT,
    gas_estimate INTEGER,
    potential_value TEXT
);

-- Exploitable contracts
CREATE TABLE exploitable_contracts (
    address TEXT,
    function_name TEXT,
    estimated_value TEXT,
    priority INTEGER,
    executed BOOLEAN DEFAULT FALSE
);

-- Execution results
CREATE TABLE executions (
    contract_address TEXT,
    tx_hash TEXT,
    success BOOLEAN,
    gas_used INTEGER,
    value TEXT
);
```

## � Ghost Mode Security

### Archaeologist Tactics
- **Silent Operations**: No traces, no celebration
- **Fresh Wallet Rotation**: Ghost addresses for extraction
- **Flashbots Integration**: MEV protection for stealth
- **Private Mempool**: No public transaction exposure
- **Delayed Extraction**: Strategic timing for maximum stealth

### Excavation Safety
- **2x Profit Threshold**: Only profitable extractions
- **Gas Price Monitoring**: 20 gwei baseline with limits
- **Pre-execution Simulation**: Test before commit
- **Graceful Failure**: Silent error handling
- **Balance Verification**: Confirm value before extraction

**You don't brag. You don't share. You extract and vanish.**

## 🔧 Advanced Usage

### Custom Function Targeting
```typescript
// Add custom target functions
const customFunction: TargetFunction = {
  name: 'customClaim',
  signature: 'customClaim(address)',
  fourByteSignature: '0x12345678',
  description: 'Custom claim function',
  priority: 1
};
```

### Multi-Chain Scanning
```typescript
// Scan specific chain
await scanner.scanChain(CONFIG.chains[0]); // Ethereum

// Scan all configured chains
await scanner.scanAllChains();
```

### Custom Simulation
```typescript
// Simulate with custom parameters
const result = await contractSimulator.simulateWithCustomParams(
  contract,
  'claim',
  [userAddress]
);
```

## 📈 Performance Optimization

### Rate Limiting
- Etherscan API: 200ms between requests
- RPC calls: Batched processing
- Transaction submission: 1-second delays

### Batch Processing
- Process contracts in batches of 10
- Parallel simulation where possible
- Database bulk operations

### Memory Management
- Stream processing for large datasets
- Efficient data structures
- Garbage collection optimization

## �️ Surveillance & Intelligence

### Archaeologist Logging
- **EXTRACT**: Silent value extraction events
- **MARK**: Targets marked for future exploitation
- **RELIC**: Analysis of dead contract structures
- **GHOST**: Silent operational updates
- **DUST**: Small but guaranteed value collection

### Intelligence Metrics
- Relics discovered per excavation site
- Extraction success rates by chain
- Total forgotten money recovered
- Ghost operation efficiency
- Balance-aware targeting accuracy

**Every operation is documented. Every extraction is tracked. You are building a war chest in silence.**

## 🔮 Future Enhancements

### Planned Features
1. **Smart Contract Fuzzer**: Auto-test edge cases
2. **Auto-Deployer**: Resell discovered exploits
3. **Frontend Dashboard**: Tracking and leaderboard
4. **Token Balance Checking**: ERC20 token detection
5. **Merkle Proof Validation**: Airdrop claim verification

### Scaling Opportunities
- Kubernetes deployment
- Message queue integration
- Distributed scanning
- Real-time block monitoring

## 📄 License

This project is for educational purposes only. Use responsibly and in accordance with applicable laws and regulations.

## ⚠️ Disclaimer

This software is provided "as is" for educational and research purposes. The authors are not responsible for any misuse or damages. Always ensure you have proper authorization before interacting with smart contracts.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create pull request

## 📞 Support

For questions or support:
- Open an issue on GitHub
- Review the documentation
- Check the logs for debugging information

---

## 🏺 The Archaeologist's Creed

*"Most people chase narratives. You chase forgotten money."*

*"You are not a builder. You are not a trader. You are a forager in the ruins of the chain."*

*"Dust. Silence. Extraction."*

**The mindset of the Contract Archaeologist is cold, quiet, and deeply dangerous.**

---

*Ready to hunt for forgotten money? The ruins await.* 🏴‍☠️
