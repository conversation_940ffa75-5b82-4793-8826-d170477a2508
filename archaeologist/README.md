# Contract Archaeologist 🏛️

> "Most people scan Twitter. I scan opcodes."  
> "Most people chase narratives. You will chase forgotten money."

Contract Archaeologist is a production-ready automated system that discovers and extracts funds from forgotten, abandoned, or misconfigured smart contracts across multiple blockchains. It systematically scans for contracts with claimable functions like `claim()`, `withdraw()`, `exit()`, and `rageQuit()`, then attempts to extract available value using advanced fuzzing techniques and real-time monitoring.

## ✨ New Features

- 🎯 **Advanced Fuzzing System**: 15+ fuzzing strategies for deep vulnerability discovery
- 🔄 **Real-time Block Monitoring**: Block-by-block contract discovery
- 📊 **Web Dashboard**: Beautiful real-time monitoring interface
- 🧪 **Comprehensive Test Suite**: Jest-based testing with high coverage
- 🔍 **Token Balance Detection**: ERC20 token scanning and analysis
- 📈 **Incremental Scanning**: Efficient block tracking to avoid rescanning
- 🎨 **Production-Ready**: Proper error handling, logging, and monitoring

## 🎯 Core Goal

Find and extract funds from:
- Unclaimed tokens or ETH (`claim()`, `withdraw()`, `exit()`)
- Airdrops with open claim gates
- Dead DAOs/vaults with `rageQuit()`, `finalize()`, or `selfDestruct()`
- Stale LP contracts with value remaining
- Deprecated yield strategies
- Pre-fund contracts with value before launch

## 🔧 Architecture

The system follows a modular pipeline architecture:

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Fetcher   │───▶│   Filter    │───▶│  Simulator  │───▶│  Evaluator  │───▶│  Executor   │
│             │    │             │    │             │    │             │    │             │
│ Get verified│    │ Filter by   │    │ Test via    │    │ Assess      │    │ Submit real │
│ contracts   │    │ target      │    │ eth_call    │    │ profitability│    │ transactions│
│ from        │    │ function    │    │ simulation  │    │ & flag      │    │ via         │
│ Etherscan   │    │ signatures  │    │             │    │ exploitable │    │ Flashbots   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### Core Components

#### 1. **Database Layer** (`src/database.ts`)
- SQLite database for persistence and avoiding reruns
- Tracks contracts, simulations, exploits, and executions
- Provides statistics and scan status tracking

#### 2. **Contract Fetcher** (`src/fetcher.ts`)
- Fetches recently verified contracts from Etherscan API
- Supports multiple methods: recent contracts, block ranges, top contracts
- Rate-limited to respect API quotas

#### 3. **Contract Filter** (`src/filter.ts`)
- Filters contracts by target function signatures
- Prioritizes based on keywords (airdrop, vault, DAO, etc.)
- Categorizes contracts (DAOs, vaults, airdrops)

#### 4. **Contract Simulator** (`src/simulator.ts`)
- Simulates function calls via `eth_call`
- Tests multiple parameter combinations
- Extracts potential value from return data

#### 5. **Evaluator** (`src/evaluator.ts`)
- Assesses profitability vs gas costs
- Flags exploitable contracts
- Calculates priority scores

#### 6. **Executor** (`src/executor.ts`)
- Executes profitable exploits
- Supports Flashbots for MEV protection
- Handles transaction batching and nonce management

#### 7. **Main Scanner** (`src/scanner.ts`)
- Orchestrates the entire pipeline
- Supports single scans and continuous monitoring
- Provides CLI interface

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- TypeScript
- Etherscan API key
- RPC endpoints (Infura, Alchemy, etc.)
- Private key for transaction execution

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd contract-archaeologist

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### Configuration

Edit `.env` file with your settings:

```env
# Required
ETHERSCAN_API_KEY=your_etherscan_api_key
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_key
PRIVATE_KEY=your_private_key

# Optional
FLASHBOTS_RPC_URL=https://rpc.flashbots.net
SCAN_BATCH_SIZE=100
SIMULATION_TIMEOUT=5000
MAX_GAS_PRICE=20000000000
```

### Supported Chains

| Chain | Chain ID | Status |
|-------|----------|--------|
| Ethereum | 1 | ✅ |
| Arbitrum | 42161 | ✅ |
| Base | 8453 | ✅ |
| Optimism | 10 | ✅ |
| Polygon | 137 | ✅ |

## 📖 Usage

### Basic Commands

```bash
# Run a single scan of all chains
npm run scan

# Run incremental scan (only new blocks)
npm run scan:incremental

# Start continuous scanning (60-minute intervals)
npm run scan:continuous

# Start real-time block monitoring
npm run monitor

# Launch web dashboard
npm run dashboard

# Scan specific contract
npm run scan -- contract ****************************************** 1

# View statistics
npm run stats

# Execute pending exploits
npm run execute

# Fuzz specific contract
npm run scan -- fuzz ****************************************** 1 100
```

### 🌐 Web Dashboard

The web dashboard provides a beautiful interface for monitoring your Contract Archaeologist:

```bash
# Start the dashboard
npm run dashboard

# Access at http://localhost:3000
```

**Dashboard Features:**
- 📊 Real-time statistics and metrics
- 🔄 Start/stop scanning and monitoring
- 📈 Chain status and block tracking
- 🎯 Recent exploits and pending opportunities
- 🔍 Manual contract scanning
- 📝 Live system logs
- 🚀 One-click exploit execution

### 🧪 Testing

```bash
# Run test suite
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### 🔍 Advanced Fuzzing

The system includes 15+ fuzzing strategies for deep vulnerability discovery:

**Fuzzing Strategies:**
- **Boundary Value Testing**: Test edge cases and limits
- **Overflow/Underflow**: Integer overflow detection
- **Reentrancy**: Reentrancy attack vectors
- **Access Control**: Authorization bypass attempts
- **Token Amount Testing**: Various token amounts and dust
- **Timestamp Manipulation**: Time-based vulnerabilities
- **Array Testing**: Array boundary and length attacks
- **Signature Testing**: Signature verification bypasses
- **Price Oracle Testing**: Price manipulation vectors
- **Rounding Testing**: Precision loss vulnerabilities

### 🔄 Real-time Monitoring

Block-by-block monitoring for immediate exploit detection:

```bash
# Start real-time monitoring
npm run monitor

# Monitor catches new contracts as they're deployed
# Automatically scans and executes profitable exploits
```

### Development Mode

```bash
# Run TypeScript directly
npm run dev

# Build for production
npm run build

# Run built version
npm start
```

## 🎯 Target Functions

The system targets these high-value functions:

| Function | 4-byte Signature | Priority | Description |
|----------|-----------------|----------|-------------|
| `claim()` | `0x4e71d92d` | 1 | Airdrops, loyalty drops |
| `withdraw()` | `0x2e1a7d4d` | 1 | Legacy vaults |
| `exit()` | `0x3f4ba83a` | 1 | DAOs, LP exits |
| `rageQuit()` | `0x7c7f4f8e` | 2 | DAO exits |
| `collect()` | `0x9a1fc3a7` | 2 | DEX fees |
| `finalize()` | `0x4bb278f3` | 2 | Unlock mechanisms |
| `redeem()` | `0xbe040fb0` | 2 | Tokenized positions |
| `emergencyWithdraw()` | `0x5312ea8e` | 1 | Emergency mechanisms |

## 🔍 Intelligence Features

### Keyword Prioritization
Contracts containing these keywords get priority boost:
- `airdrop`, `vault`, `rageQuit`, `merkle`
- `dao`, `yield`, `farming`, `staking`
- `lp`, `liquidity`, `claim`, `withdraw`

### Contract Categorization
- **DAOs**: Contracts with governance and ragequit functions
- **Vaults**: Yield farming and staking contracts
- **Airdrops**: Distribution and claim contracts

### Simulation Strategies
- Multiple parameter combinations
- Fallback to zero values and addresses
- Timeout protection (5 seconds default)
- Gas estimation for profitability

## 📊 Database Schema

```sql
-- Contract storage
CREATE TABLE contracts (
    address TEXT PRIMARY KEY,
    name TEXT,
    abi TEXT,
    source_code TEXT,
    chain_id INTEGER,
    block_number INTEGER,
    timestamp INTEGER
);

-- Simulation results
CREATE TABLE simulations (
    contract_address TEXT,
    function_name TEXT,
    success BOOLEAN,
    return_data TEXT,
    gas_estimate INTEGER,
    potential_value TEXT
);

-- Exploitable contracts
CREATE TABLE exploitable_contracts (
    address TEXT,
    function_name TEXT,
    estimated_value TEXT,
    priority INTEGER,
    executed BOOLEAN DEFAULT FALSE
);

-- Execution results
CREATE TABLE executions (
    contract_address TEXT,
    tx_hash TEXT,
    success BOOLEAN,
    gas_used INTEGER,
    value TEXT
);
```

## 🛡️ Security Features

### Ghost Tactics
- Fresh wallet support
- Flashbots integration for MEV protection
- Private mempool submission
- Delayed token selling

### Safety Measures
- Gas price limits
- Profitability checks
- Transaction simulation before execution
- Graceful error handling

## 🔧 Advanced Usage

### Custom Function Targeting
```typescript
// Add custom target functions
const customFunction: TargetFunction = {
  name: 'customClaim',
  signature: 'customClaim(address)',
  fourByteSignature: '0x12345678',
  description: 'Custom claim function',
  priority: 1
};
```

### Multi-Chain Scanning
```typescript
// Scan specific chain
await scanner.scanChain(CONFIG.chains[0]); // Ethereum

// Scan all configured chains
await scanner.scanAllChains();
```

### Custom Simulation
```typescript
// Simulate with custom parameters
const result = await contractSimulator.simulateWithCustomParams(
  contract,
  'claim',
  [userAddress]
);
```

## 📈 Performance Optimization

### Rate Limiting
- Etherscan API: 200ms between requests
- RPC calls: Batched processing
- Transaction submission: 1-second delays

### Batch Processing
- Process contracts in batches of 10
- Parallel simulation where possible
- Database bulk operations

### Memory Management
- Stream processing for large datasets
- Efficient data structures
- Garbage collection optimization

## 🚨 Monitoring & Logging

### Log Levels
- **ERROR**: Critical failures
- **WARN**: Non-critical issues
- **INFO**: General information
- **DEBUG**: Detailed debugging

### Metrics Tracking
- Contracts scanned per chain
- Simulation success rates
- Exploit success rates
- Total value extracted

## 🔮 Future Enhancements

### Planned Features
1. **Smart Contract Fuzzer**: Auto-test edge cases
2. **Auto-Deployer**: Resell discovered exploits
3. **Frontend Dashboard**: Tracking and leaderboard
4. **Token Balance Checking**: ERC20 token detection
5. **Merkle Proof Validation**: Airdrop claim verification

### Scaling Opportunities
- Kubernetes deployment
- Message queue integration
- Distributed scanning
- Real-time block monitoring

## 📄 License

This project is for educational purposes only. Use responsibly and in accordance with applicable laws and regulations.

## ⚠️ Disclaimer

This software is provided "as is" for educational and research purposes. The authors are not responsible for any misuse or damages. Always ensure you have proper authorization before interacting with smart contracts.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create pull request

## 📞 Support

For questions or support:
- Open an issue on GitHub
- Review the documentation
- Check the logs for debugging information

---

*"Stay invisible. Extract value. Repeat."*
