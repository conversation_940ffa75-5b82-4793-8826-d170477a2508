import { ChainConfig, TargetFunction } from './types';
export interface DecompiledFunction {
    selector: string;
    signature?: string;
    name?: string;
    inputs?: string[];
    confidence: number;
}
export interface DecompilationResult {
    address: string;
    chainId: number;
    functions: DecompiledFunction[];
    hasTargetFunctions: boolean;
    matchedTargetFunctions: TargetFunction[];
    bytecodeHash: string;
}
export declare class BytecodeDecompiler {
    private providers;
    private functionSignatureDatabase;
    constructor(chains: ChainConfig[]);
    private initializeProviders;
    private initializeFunctionDatabase;
    decompileContract(address: string, chainId: number): Promise<DecompilationResult | null>;
    private extractFunctionSelectors;
    private isValidSelector;
    private extractFunctionName;
    private extractInputTypes;
    private matchTargetFunctions;
    batchDecompile(addresses: string[], chainId: number): Promise<DecompilationResult[]>;
    private detectFunctionPatterns;
}
export declare const bytecodeDecompiler: BytecodeDecompiler;
//# sourceMappingURL=decompiler.d.ts.map