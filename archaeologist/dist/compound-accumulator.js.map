{"version": 3, "file": "compound-accumulator.js", "sourceRoot": "", "sources": ["../src/compound-accumulator.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AAChC,qCAAkC;AAwBlC,MAAa,mBAAmB;IAM9B,YAAY,EAAY;QAJP,mBAAc,GAAG,eAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,gCAAgC;QAC7E,uBAAkB,GAAG,eAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,kCAAkC;QAClF,wBAAmB,GAAG,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,6BAA6B;QAG5F,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,eAAuB,EACvB,YAAoB,EACpB,cAAsB,EACtB,OAAe,EACf,MAAc;QAEd,MAAM,SAAS,GAAG,cAAc,GAAG,OAAO,CAAC;QAE3C,IAAI,SAAS,IAAI,EAAE,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,gCAAgC,eAAe,IAAI,YAAY,EAAE,CAAC,CAAC;YAChF,OAAO;QACT,CAAC;QAED,oBAAoB;QACpB,MAAM,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;YAC3B,eAAe;YACf,YAAY;YACZ,cAAc;YACd,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,eAAM,CAAC,OAAO,CAAC,mBAAmB,eAAM,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,eAAe,EAAE,CAAC,CAAC;QAE/F,yCAAyC;QACzC,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACxB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC;QAEnD,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAC/E,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1D,MAAM,gBAAgB,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;QAC7D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACpD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAExD,OAAO;YACL,gBAAgB;YAChB,gBAAgB;YAChB,gBAAgB;YAChB,aAAa;YACb,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B;QACvC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAEhD,IAAI,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtD,eAAM,CAAC,OAAO,CAAC,2BAA2B,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACtG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAwB;QACvD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACtB,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YAC9D,eAAM,CAAC,OAAO,CAAC,sBAAsB,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;YACrE,eAAM,CAAC,OAAO,CAAC,gBAAgB,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC1F,eAAM,CAAC,OAAO,CAAC,iBAAiB,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAElF,IAAI,CAAC;gBACH,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBACjD,eAAM,CAAC,OAAO,CAAC,oBAAoB,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;YACrE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,gBAAgB,CAAC;YACxE,eAAM,CAAC,KAAK,CAAC,wBAAwB,eAAM,CAAC,WAAW,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,uEAAuE;QACvE,oDAAoD;QACpD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAE1E,+CAA+C;QAC/C,OAAO,WAAW,GAAG,EAAE,GAAG,GAAG,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC5B,wCAAwC;QACxC,OAAO,CAAC,CAAC,CAAC,cAAc;IAC1B,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,gBAAwB;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7C,iDAAiD;QACjD,OAAO,OAAO;aACX,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,IAAI,MAAM,CAAC,cAAc,CAAC;aAC3D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,OAAO;YACL;gBACE,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,sCAAsC;gBACnD,cAAc,EAAE,eAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBACzC,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,eAAM,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;oBAC9D,iDAAiD;gBACnD,CAAC;aACF;YACD;gBACE,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,kCAAkC;gBAC/C,cAAc,EAAE,eAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBACzC,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,eAAM,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;oBACpE,iDAAiD;gBACnD,CAAC;aACF;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,8BAA8B;gBAC3C,cAAc,EAAE,eAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBACzC,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,eAAM,CAAC,OAAO,CAAC,sDAAsD,CAAC,CAAC;oBACvE,4CAA4C;gBAC9C,CAAC;aACF;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,0CAA0C;gBACvD,cAAc,EAAE,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBACxC,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,eAAM,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;oBAClE,qDAAqD;gBACvD,CAAC;aACF;YACD;gBACE,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,iCAAiC;gBAC9C,cAAc,EAAE,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBACxC,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,eAAM,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;oBAC5D,8CAA8C;gBAChD,CAAC;aACF;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,yCAAyC;gBACtD,cAAc,EAAE,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBACxC,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,eAAM,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;oBAC5D,+CAA+C;gBACjD,CAAC;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,MAA0B;QAC1D,MAAM,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC9B,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,MAAM,EAAE,MAAM,CAAC,cAAc;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB;QAC3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAEhD,eAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACjD,eAAM,CAAC,OAAO,CAAC,4BAA4B,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7F,eAAM,CAAC,OAAO,CAAC,4BAA4B,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7F,eAAM,CAAC,OAAO,CAAC,4BAA4B,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7F,eAAM,CAAC,OAAO,CAAC,yBAAyB,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;QAE/D,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,gBAAgB,CAAC;YACxE,IAAI,MAAM,IAAI,EAAE,EAAE,CAAC;gBACjB,eAAM,CAAC,OAAO,CAAC,yBAAyB,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,OAAO,CAAC,eAAe,KAAK,CAAC,UAAU,CAAC,WAAW,WAAW,eAAM,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC/G,CAAC;QACH,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC;QACnD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,CAAC;QAErC,8BAA8B;QAC9B,MAAM,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QACpG,MAAM,QAAQ,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAChD,MAAM,OAAO,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;QAE1E,MAAM,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAClF,IAAI,QAAQ,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE7B,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAC1E,MAAM,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC;QAEpD,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AArQD,kDAqQC"}