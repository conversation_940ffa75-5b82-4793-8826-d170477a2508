"use strict";
/**
 * 🏺 SURGICAL PRECISION: Enhanced Bytecode Analysis System
 *
 * This module provides multi-source bytecode analysis for unverified contracts.
 * Integrates proven open-source decompilers and analysis tools for maximum accuracy.
 *
 * Sources:
 * 1. He<PERSON><PERSON>l-rs (Primary decompiler)
 * 2. Dedaub API (Commercial grade)
 * 3. EtherVM (Backup)
 * 4. Internal bytecode pattern matching
 *
 * NO GUESSES. SURGICAL PRECISION.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.bytecodeAnalyzer = void 0;
const axios_1 = __importDefault(require("axios"));
const ethers_1 = require("ethers");
const child_process_1 = require("child_process");
const util_1 = require("util");
const logger_1 = require("./logger");
const config_1 = require("./config");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class BytecodeAnalyzer {
    constructor() {
        this.cache = new Map();
        this.DEDAUB_API = 'https://api.dedaub.com/decompile';
        this.ETHERVM_API = 'https://ethervm.io/decompile';
    }
    /**
     * SURGICAL PRECISION: Analyze bytecode with multiple decompilers
     */
    async analyzeBytecode(address, chainId, bytecode) {
        const cacheKey = `${address}:${chainId}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        logger_1.logger.relic(`🔍 Analyzing bytecode for ${address} on chain ${chainId}`);
        // Get bytecode if not provided
        if (!bytecode) {
            bytecode = await this.fetchBytecode(address, chainId);
        }
        const analysis = {
            address,
            chainId,
            bytecode,
            functions: [],
            decompilationSources: [],
            confidence: 0,
            isVerified: false,
            analysis: {
                hasTargetFunctions: false,
                targetFunctions: [],
                riskLevel: 'LOW',
                extractionPotential: 0
            }
        };
        // Multi-source analysis
        const analysisResults = await Promise.allSettled([
            this.analyzeWithHeimdall(bytecode),
            this.analyzeWithDedaub(bytecode),
            this.analyzeWithInternalPatterns(bytecode)
        ]);
        // Consolidate results
        const functions = new Map();
        analysisResults.forEach((result, index) => {
            const sources = ['Heimdall-rs', 'Dedaub', 'Internal'];
            if (result.status === 'fulfilled' && result.value) {
                analysis.decompilationSources.push(sources[index]);
                result.value.forEach((func) => {
                    const existing = functions.get(func.fourByteSignature);
                    if (!existing || func.confidence > existing.confidence) {
                        functions.set(func.fourByteSignature, func);
                    }
                });
            }
        });
        analysis.functions = Array.from(functions.values());
        analysis.confidence = this.calculateConfidence(analysis);
        // Check for target functions
        this.analyzeTargetFunctions(analysis);
        // Calculate extraction potential
        this.calculateExtractionPotential(analysis);
        logger_1.logger.relic(`📊 Analysis complete: ${analysis.functions.length} functions detected (${analysis.confidence}% confidence)`);
        if (analysis.analysis.hasTargetFunctions) {
            logger_1.logger.mark(`🎯 TARGET FUNCTIONS DETECTED: ${analysis.analysis.targetFunctions.join(', ')}`);
        }
        this.cache.set(cacheKey, analysis);
        return analysis;
    }
    /**
     * Fetch bytecode from chain
     */
    async fetchBytecode(address, chainId) {
        const chain = config_1.CONFIG.chains.find(c => c.chainId === chainId);
        if (!chain?.rpcUrl) {
            throw new Error(`No RPC URL configured for chain ${chainId}`);
        }
        const provider = new ethers_1.ethers.JsonRpcProvider(chain.rpcUrl);
        const bytecode = await provider.getCode(address);
        if (bytecode === '0x') {
            throw new Error(`No bytecode found at address ${address}`);
        }
        return bytecode;
    }
    /**
     * Analyze with Heimdall-rs (if available)
     */
    async analyzeWithHeimdall(bytecode) {
        try {
            // Check if heimdall is installed
            await execAsync('which heimdall');
            // Create temporary file for bytecode
            const tempFile = `/tmp/bytecode_${Date.now()}.hex`;
            require('fs').writeFileSync(tempFile, bytecode);
            // Run heimdall decompilation
            const { stdout } = await execAsync(`heimdall decompile ${tempFile} --include-sol --include-yul`, {
                timeout: 30000
            });
            // Parse heimdall output for function signatures
            const functions = this.parseHeimdallOutput(stdout);
            // Cleanup
            require('fs').unlinkSync(tempFile);
            logger_1.logger.relic(`🔨 Heimdall-rs detected ${functions.length} functions`);
            return functions;
        }
        catch (error) {
            logger_1.logger.debug(`Heimdall-rs analysis failed: ${error}`);
            return [];
        }
    }
    /**
     * Analyze with Dedaub API (if available)
     */
    async analyzeWithDedaub(bytecode) {
        try {
            const response = await axios_1.default.post(this.DEDAUB_API, {
                bytecode: bytecode,
                include_functions: true
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (response.data && response.data.functions) {
                const functions = this.parseDedaubOutput(response.data.functions);
                logger_1.logger.relic(`🏛️ Dedaub detected ${functions.length} functions`);
                return functions;
            }
            return [];
        }
        catch (error) {
            logger_1.logger.debug(`Dedaub analysis failed: ${error}`);
            return [];
        }
    }
    /**
     * Internal pattern matching for target functions
     */
    async analyzeWithInternalPatterns(bytecode) {
        const functions = [];
        // Search for target function signatures in bytecode
        config_1.CONFIG.targetFunctions.forEach(targetFunc => {
            const signature = targetFunc.fourByteSignature.slice(2); // Remove 0x
            if (bytecode.toLowerCase().includes(signature.toLowerCase())) {
                functions.push({
                    signature: targetFunc.signature,
                    fourByteSignature: targetFunc.fourByteSignature,
                    confidence: 85, // High confidence for direct signature match
                    source: 'Internal Pattern Matching'
                });
            }
        });
        if (functions.length > 0) {
            logger_1.logger.extract(`🎯 Internal pattern matching found ${functions.length} target functions`);
        }
        return functions;
    }
    /**
     * Parse Heimdall-rs output
     */
    parseHeimdallOutput(output) {
        const functions = [];
        // Parse function signatures from Heimdall output
        const functionRegex = /function\s+(\w+)\s*\([^)]*\)/g;
        let match;
        while ((match = functionRegex.exec(output)) !== null) {
            const functionName = match[1];
            const signature = match[0].replace('function ', '');
            try {
                const fourByteSignature = ethers_1.ethers.id(signature).slice(0, 10);
                functions.push({
                    signature,
                    fourByteSignature,
                    confidence: 90,
                    source: 'Heimdall-rs'
                });
            }
            catch (error) {
                // Skip invalid signatures
            }
        }
        return functions;
    }
    /**
     * Parse Dedaub output
     */
    parseDedaubOutput(dedaubFunctions) {
        const functions = [];
        dedaubFunctions.forEach(func => {
            if (func.signature && func.selector) {
                functions.push({
                    signature: func.signature,
                    fourByteSignature: func.selector,
                    confidence: 95, // Dedaub is highly accurate
                    source: 'Dedaub',
                    parameters: func.parameters,
                    isPayable: func.payable,
                    isView: func.view
                });
            }
        });
        return functions;
    }
    /**
     * Calculate overall confidence
     */
    calculateConfidence(analysis) {
        if (analysis.functions.length === 0)
            return 0;
        const avgConfidence = analysis.functions.reduce((sum, func) => sum + func.confidence, 0) / analysis.functions.length;
        const sourceBonus = analysis.decompilationSources.length * 10; // Bonus for multiple sources
        return Math.min(100, Math.round(avgConfidence + sourceBonus));
    }
    /**
     * Analyze for target functions
     */
    analyzeTargetFunctions(analysis) {
        const targetSignatures = config_1.CONFIG.targetFunctions.map(f => f.fourByteSignature.toLowerCase());
        analysis.analysis.targetFunctions = analysis.functions
            .filter(func => targetSignatures.includes(func.fourByteSignature.toLowerCase()))
            .map(func => func.signature);
        analysis.analysis.hasTargetFunctions = analysis.analysis.targetFunctions.length > 0;
        // Set risk level based on target functions
        if (analysis.analysis.targetFunctions.length >= 3) {
            analysis.analysis.riskLevel = 'HIGH';
        }
        else if (analysis.analysis.targetFunctions.length >= 1) {
            analysis.analysis.riskLevel = 'MEDIUM';
        }
        else {
            analysis.analysis.riskLevel = 'LOW';
        }
    }
    /**
     * Calculate extraction potential
     */
    calculateExtractionPotential(analysis) {
        let potential = 0;
        // Base potential from target functions
        potential += analysis.analysis.targetFunctions.length * 25;
        // Bonus for high-priority functions
        const highPriorityFunctions = ['claim()', 'withdraw()', 'exit()', 'emergencyWithdraw()'];
        const hasHighPriority = analysis.analysis.targetFunctions.some(func => highPriorityFunctions.some(hpf => func.includes(hpf.split('(')[0])));
        if (hasHighPriority)
            potential += 20;
        // Bonus for multiple decompilation sources
        potential += analysis.decompilationSources.length * 5;
        // Confidence bonus
        potential += Math.round(analysis.confidence * 0.1);
        analysis.analysis.extractionPotential = Math.min(100, potential);
    }
    /**
     * Get cached analysis
     */
    getCachedAnalysis(address, chainId) {
        return this.cache.get(`${address}:${chainId}`) || null;
    }
    /**
     * Clear analysis cache
     */
    clearCache() {
        this.cache.clear();
        logger_1.logger.ghost('Bytecode analysis cache cleared');
    }
}
exports.bytecodeAnalyzer = new BytecodeAnalyzer();
//# sourceMappingURL=bytecode-analyzer.js.map