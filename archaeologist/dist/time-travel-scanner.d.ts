/**
 * 🏺 ARCHAE<PERSON>OGICAL TIME-TRAVEL SCANNER
 *
 * This module allows the archaeologist to travel back in time and scan
 * specific historical periods of the blockchain for forgotten money.
 *
 * HUNT THE ANCIENT RUINS. EXTRACT FORGOTTEN WEALTH.
 */
declare const ARCHA<PERSON><PERSON>OGICAL_PERIODS: {
    2015: {
        name: string;
        description: string;
    };
    2016: {
        name: string;
        description: string;
    };
    2017: {
        name: string;
        description: string;
    };
    2018: {
        name: string;
        description: string;
    };
    2019: {
        name: string;
        description: string;
    };
    2020: {
        name: string;
        description: string;
    };
    2021: {
        name: string;
        description: string;
    };
    2022: {
        name: string;
        description: string;
    };
    2023: {
        name: string;
        description: string;
    };
    2024: {
        name: string;
        description: string;
    };
};
interface TimeTravelConfig {
    chainId: number;
    startBlock?: number;
    endBlock?: number;
    year?: number;
    batchSize?: number;
    maxBlocks?: number;
}
interface HistoricalScanResult {
    chainId: number;
    startBlock: number;
    endBlock: number;
    period: string;
    contractsFound: number;
    targetsFound: number;
    exploitableContracts: number;
    totalValue: string;
    scanDuration: number;
}
declare class TimeTravelScanner {
    private providers;
    constructor();
    private initializeProviders;
    /**
     * 🏺 MAIN TIME TRAVEL SCANNING METHOD
     */
    scanHistoricalPeriod(config: TimeTravelConfig): Promise<HistoricalScanResult>;
    /**
     * 🏺 Calculate block range from config
     */
    private calculateBlockRange;
    /**
     * 🏺 Scan historical blocks for contracts
     */
    private scanHistoricalBlocks;
    /**
     * 🏺 Filter contracts for target functions
     */
    private filterTargetContracts;
    /**
     * 🏺 Calculate total value from exploitable contracts
     */
    private calculateTotalValue;
    /**
     * 🏺 Get chain name for display
     */
    private getChainName;
    /**
     * 🏺 Log scan results
     */
    private logScanResults;
    /**
     * 🏺 Get available archaeological periods
     */
    getAvailablePeriods(): typeof ARCHAEOLOGICAL_PERIODS;
    /**
     * 🏺 Estimate block number for a given year
     */
    estimateBlockForYear(year: number, chainId?: number): number | null;
}
export declare const timeTravelScanner: TimeTravelScanner;
export { TimeTravelConfig, HistoricalScanResult, ARCHAEOLOGICAL_PERIODS };
//# sourceMappingURL=time-travel-scanner.d.ts.map