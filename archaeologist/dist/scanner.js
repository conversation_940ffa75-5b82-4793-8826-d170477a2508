"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scanner = exports.Scanner = void 0;
const fetcher_1 = require("./fetcher");
const filter_1 = require("./filter");
const simulator_1 = require("./simulator");
const evaluator_1 = require("./evaluator");
const executor_1 = require("./executor");
const database_1 = require("./database");
const logger_1 = require("./logger");
const config_1 = require("./config");
const metrics_1 = require("./metrics");
const decompiler_1 = require("./decompiler");
const ethers_1 = require("ethers");
const ora_1 = __importDefault(require("ora"));
// 🏺 SURGICAL PRECISION COMPONENTS
const signature_verifier_1 = require("./signature-verifier");
const economic_engine_1 = require("./economic-engine");
const self_monitor_1 = require("./self-monitor");
class Scanner {
    constructor() {
        this.isRunning = false;
    }
    /**
     * 🏺 SURGICAL PRECISION: Comprehensive system verification
     */
    async performSurgicalVerification() {
        logger_1.logger.ghost('🔍 Initiating surgical precision verification...');
        const errors = [];
        const warnings = [];
        try {
            // 1. Verify all function signatures
            logger_1.logger.relic('Verifying function signatures...');
            const signatureVerification = await signature_verifier_1.signatureVerifier.verifyAllTargetFunctions(config_1.CONFIG.targetFunctions);
            if (signatureVerification.summary.failed > 0) {
                errors.push(`${signatureVerification.summary.failed} function signatures failed verification`);
                signatureVerification.failed.forEach(failed => {
                    errors.push(`  ${failed.signature}: ${failed.conflicts.join(', ')}`);
                });
            }
            else {
                logger_1.logger.extract(`✅ All ${signatureVerification.summary.total} function signatures verified`);
            }
            // 2. Perform comprehensive health check
            logger_1.logger.relic('Performing system health check...');
            const healthCheck = await self_monitor_1.selfMonitor.performHealthCheck();
            if (healthCheck.overall === 'CRITICAL' || healthCheck.overall === 'FAILED') {
                errors.push(`System health check failed: ${healthCheck.overall}`);
                healthCheck.issues.forEach(issue => {
                    if (issue.severity === 'CRITICAL') {
                        errors.push(`  ${issue.component}: ${issue.description}`);
                    }
                });
            }
            else if (healthCheck.overall === 'WARNING') {
                warnings.push('System health check shows warnings');
                healthCheck.issues.forEach(issue => {
                    if (issue.severity === 'MEDIUM' || issue.severity === 'HIGH') {
                        warnings.push(`  ${issue.component}: ${issue.description}`);
                    }
                });
            }
            else {
                logger_1.logger.extract(`✅ System health check passed: ${healthCheck.overall}`);
            }
            // 3. Test economic engine
            logger_1.logger.relic('Testing economic calculation engine...');
            try {
                const testEconomics = await economic_engine_1.economicEngine.analyzeEconomics(1, // Ethereum
                ethers_1.ethers.parseEther('1'), // 1 ETH
                100000n, // 100k gas
                []);
                if (testEconomics.profitability.netProfit >= 0n) {
                    logger_1.logger.extract('✅ Economic engine operational');
                }
                else {
                    warnings.push('Economic engine test returned negative profit');
                }
            }
            catch (error) {
                errors.push(`Economic engine test failed: ${error}`);
            }
            // 4. Validate configuration
            const configValidation = this.validateConfiguration();
            errors.push(...configValidation.errors);
            warnings.push(...configValidation.warnings);
            const valid = errors.length === 0;
            if (valid) {
                logger_1.logger.extract('🎯 SURGICAL PRECISION ACHIEVED - System ready for operations');
            }
            else {
                logger_1.logger.mark(`🚨 SURGICAL PRECISION FAILED - ${errors.length} critical errors detected`);
            }
            return { valid, errors, warnings };
        }
        catch (error) {
            errors.push(`Surgical verification failed: ${error}`);
            return { valid: false, errors, warnings };
        }
    }
    // Validate production configuration
    validateConfiguration() {
        const errors = [];
        const warnings = [];
        // Check required environment variables
        if (!process.env.ETHERSCAN_API_KEY) {
            errors.push('ETHERSCAN_API_KEY is required for Ethereum mainnet');
        }
        // Check if at least one chain has proper configuration
        const validChains = config_1.CONFIG.chains.filter(chain => chain.rpcUrl && chain.etherscanApiKey);
        if (validChains.length === 0) {
            errors.push('At least one chain must have both RPC_URL and API_KEY configured');
            errors.push('Example: ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_KEY');
        }
        // Warn about chains that will be skipped
        const invalidChains = config_1.CONFIG.chains.filter(chain => !chain.rpcUrl || !chain.etherscanApiKey);
        if (invalidChains.length > 0) {
            warnings.push(`The following chains will be skipped due to missing configuration:`);
            invalidChains.forEach(chain => {
                const missing = [];
                if (!chain.rpcUrl)
                    missing.push('RPC_URL');
                if (!chain.etherscanApiKey)
                    missing.push('API_KEY');
                warnings.push(`  - ${chain.name}: Missing ${missing.join(', ')}`);
            });
        }
        // Check private key for execution
        if (!process.env.PRIVATE_KEY) {
            errors.push('PRIVATE_KEY is required for transaction execution');
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }
    async scanChain(chain) {
        const scanId = `scan_${chain.chainId}_${Date.now()}`;
        metrics_1.performanceMonitor.startTiming(scanId, 'scan', { chain: chain.name });
        metrics_1.performanceMonitor.incrementCounter('total_scans');
        const spinner = (0, ora_1.default)(`Scanning ${chain.name} (${chain.chainId})...`).start();
        try {
            // Step 1: Fetch recent verified contracts
            spinner.text = `Fetching verified contracts from ${chain.name}...`;
            const fetchId = `fetch_${chain.chainId}_${Date.now()}`;
            metrics_1.performanceMonitor.startTiming(fetchId, 'fetch');
            const verifiedContracts = await fetcher_1.contractFetcher.fetchRecentVerifiedContracts(chain, 1, config_1.CONFIG.scanBatchSize);
            metrics_1.performanceMonitor.endTiming(fetchId, true);
            // Step 1b: Fetch unverified contracts with bytecode decompilation
            spinner.text = `Fetching unverified contracts from ${chain.name}...`;
            const unverifiedContracts = await fetcher_1.contractFetcher.fetchUnverifiedContracts(chain, Math.min(5, Math.floor(config_1.CONFIG.scanBatchSize / 2))) || [];
            // Combine verified and unverified contracts
            const contracts = [...verifiedContracts, ...(unverifiedContracts || [])];
            if (contracts.length === 0) {
                spinner.warn(`No contracts found on ${chain.name}`);
                metrics_1.performanceMonitor.incrementCounter('failed_scans');
                metrics_1.performanceMonitor.endTiming(scanId, false);
                return;
            }
            logger_1.logger.info(`Found ${verifiedContracts.length} verified and ${unverifiedContracts.length} unverified contracts on ${chain.name}`);
            // Save contracts to database
            for (const contract of contracts) {
                await database_1.db.saveContract(contract);
            }
            // Step 2: Filter contracts with target functions
            spinner.text = `Filtering contracts on ${chain.name}...`;
            const filterId = `filter_${chain.chainId}_${Date.now()}`;
            metrics_1.performanceMonitor.startTiming(filterId, 'filter');
            const filteredResults = filter_1.contractFilter.filterContracts(contracts);
            metrics_1.performanceMonitor.endTiming(filterId, true);
            if (filteredResults.length === 0) {
                spinner.warn(`No contracts with target functions found on ${chain.name}`);
                metrics_1.performanceMonitor.incrementCounter('failed_scans');
                metrics_1.performanceMonitor.endTiming(scanId, false);
                return;
            }
            logger_1.logger.info(`Found ${filteredResults.length} contracts with target functions on ${chain.name}`);
            // Step 3: Simulate target functions
            spinner.text = `Simulating functions on ${chain.name}...`;
            const simId = `sim_${chain.chainId}_${Date.now()}`;
            metrics_1.performanceMonitor.startTiming(simId, 'simulation');
            metrics_1.performanceMonitor.incrementCounter('total_simulations', filteredResults.length);
            const simulationResults = await simulator_1.contractSimulator.batchSimulate(filteredResults);
            const successfulSims = simulationResults.filter(r => r.success).length;
            metrics_1.performanceMonitor.incrementCounter('successful_simulations', successfulSims);
            metrics_1.performanceMonitor.endTiming(simId, true);
            // Step 4: Evaluate simulation results
            spinner.text = `Evaluating results on ${chain.name}...`;
            const evalId = `eval_${chain.chainId}_${Date.now()}`;
            metrics_1.performanceMonitor.startTiming(evalId, 'evaluation');
            const exploitableContracts = await evaluator_1.evaluator.evaluateSimulations(simulationResults);
            metrics_1.performanceMonitor.endTiming(evalId, true);
            // Step 5: Execute exploits (if any found)
            if (exploitableContracts.length > 0) {
                logger_1.logger.info(`🎯 Found ${exploitableContracts.length} potentially exploitable contracts on ${chain.name}!`);
                spinner.text = `Executing ${exploitableContracts.length} exploits on ${chain.name}...`;
                const execId = `exec_${chain.chainId}_${Date.now()}`;
                metrics_1.performanceMonitor.startTiming(execId, 'execution');
                metrics_1.performanceMonitor.incrementCounter('total_executions', exploitableContracts.length);
                const executionResults = await executor_1.executor.batchExecuteExploits(exploitableContracts);
                const successfulExecutions = executionResults.filter(r => r.success);
                metrics_1.performanceMonitor.incrementCounter('successful_executions', successfulExecutions.length);
                metrics_1.performanceMonitor.endTiming(execId, successfulExecutions.length > 0);
                if (successfulExecutions.length > 0) {
                    logger_1.logger.info(`💰 Successfully executed ${successfulExecutions.length} exploits on ${chain.name}!`);
                }
                spinner.succeed(`Scan completed on ${chain.name}: ${successfulExecutions.length} successful exploits`);
            }
            else {
                spinner.succeed(`Scan completed on ${chain.name}: No exploitable contracts found`);
            }
            // Get current block number for tracking
            const provider = simulator_1.contractSimulator.getProvider(chain.chainId);
            let currentBlock = 0;
            if (provider) {
                try {
                    currentBlock = await provider.getBlockNumber();
                }
                catch (error) {
                    logger_1.logger.debug(`Failed to get current block for ${chain.name}:`, error);
                }
            }
            // Update scan status with proper block tracking
            metrics_1.performanceMonitor.recordDatabaseOperation();
            await database_1.db.updateScanStatus({
                chainId: chain.chainId,
                lastProcessedBlock: currentBlock,
                lastScanTime: new Date(),
                contractsScanned: contracts.length,
                exploitsFound: exploitableContracts.length,
                totalValue: exploitableContracts.reduce((sum, e) => sum + parseFloat(e.estimatedValue || '0'), 0).toString()
            });
            metrics_1.performanceMonitor.incrementCounter('successful_scans');
            metrics_1.performanceMonitor.incrementCounter('chains_scanned');
            metrics_1.performanceMonitor.endTiming(scanId, true);
        }
        catch (error) {
            metrics_1.performanceMonitor.incrementCounter('failed_scans');
            metrics_1.performanceMonitor.endTiming(scanId, false);
            spinner.fail(`Scan failed on ${chain.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            logger_1.logger.error(`Scan error on ${chain.name}:`, error);
        }
    }
    // Advanced scanning with block tracking to avoid rescanning
    async scanChainFromLastBlock(chain) {
        const spinner = (0, ora_1.default)(`Scanning ${chain.name} from last processed block...`).start();
        try {
            // Get last scan status
            const scanStatus = await database_1.db.getScanStatus(chain.chainId);
            // Get current block number
            const provider = simulator_1.contractSimulator.getProvider(chain.chainId);
            if (!provider) {
                throw new Error(`No provider configured for ${chain.name}`);
            }
            const currentBlock = await provider.getBlockNumber();
            const lastProcessedBlock = scanStatus?.lastProcessedBlock || currentBlock - 1000;
            if (currentBlock <= lastProcessedBlock) {
                spinner.info(`${chain.name} is up to date (current: ${currentBlock}, last processed: ${lastProcessedBlock})`);
                return;
            }
            spinner.text = `Scanning ${chain.name} from block ${lastProcessedBlock + 1} to ${currentBlock}...`;
            // Fetch contracts from the unprocessed block range
            const contracts = await fetcher_1.contractFetcher.fetchContractsByBlockRange(chain, lastProcessedBlock + 1, currentBlock);
            if (contracts.length === 0) {
                spinner.info(`No new contracts found in blocks ${lastProcessedBlock + 1} to ${currentBlock}`);
                // Update scan status even if no contracts found
                await database_1.db.updateScanStatus({
                    chainId: chain.chainId,
                    lastProcessedBlock: currentBlock,
                    lastScanTime: new Date(),
                    contractsScanned: 0,
                    exploitsFound: 0,
                    totalValue: '0'
                });
                return;
            }
            // Save contracts to database
            for (const contract of contracts) {
                await database_1.db.saveContract(contract);
            }
            // Process contracts as usual
            const filteredResults = filter_1.contractFilter.filterContracts(contracts);
            if (filteredResults.length > 0) {
                spinner.text = `Processing ${filteredResults.length} contracts with target functions...`;
                const simulationResults = await simulator_1.contractSimulator.batchSimulate(filteredResults);
                const exploitableContracts = await evaluator_1.evaluator.evaluateSimulations(simulationResults);
                if (exploitableContracts.length > 0) {
                    spinner.text = `Executing ${exploitableContracts.length} exploits...`;
                    const executionResults = await executor_1.executor.batchExecuteExploits(exploitableContracts);
                    const successfulExecutions = executionResults.filter(r => r.success);
                    spinner.succeed(`Scan completed: ${successfulExecutions.length} successful exploits from ${contracts.length} new contracts`);
                }
                else {
                    spinner.succeed(`Scan completed: No exploitable contracts found from ${contracts.length} new contracts`);
                }
                // Update scan status
                await database_1.db.updateScanStatus({
                    chainId: chain.chainId,
                    lastProcessedBlock: currentBlock,
                    lastScanTime: new Date(),
                    contractsScanned: contracts.length,
                    exploitsFound: exploitableContracts.length,
                    totalValue: exploitableContracts.reduce((sum, e) => sum + parseFloat(e.estimatedValue || '0'), 0).toString()
                });
            }
            else {
                spinner.succeed(`Scan completed: No contracts with target functions found from ${contracts.length} new contracts`);
                // Update scan status
                await database_1.db.updateScanStatus({
                    chainId: chain.chainId,
                    lastProcessedBlock: currentBlock,
                    lastScanTime: new Date(),
                    contractsScanned: contracts.length,
                    exploitsFound: 0,
                    totalValue: '0'
                });
            }
        }
        catch (error) {
            spinner.fail(`Scan failed on ${chain.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            logger_1.logger.error(`Scan error on ${chain.name}:`, error);
        }
    }
    async scanAllChains() {
        if (this.isRunning) {
            logger_1.logger.warn('Scanner is already running');
            return;
        }
        // 🏺 SURGICAL PRECISION: Comprehensive system verification
        const verification = await this.performSurgicalVerification();
        if (!verification.valid) {
            logger_1.logger.mark('🚨 SURGICAL PRECISION FAILED - System not ready for operations');
            verification.errors.forEach(error => logger_1.logger.mark(`  ❌ ${error}`));
            logger_1.logger.info('');
            logger_1.logger.info('📋 Required environment variables:');
            logger_1.logger.info('  - ETHERSCAN_API_KEY=your_etherscan_api_key');
            logger_1.logger.info('  - ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_key');
            logger_1.logger.info('  - PRIVATE_KEY=your_private_key_for_execution');
            logger_1.logger.info('');
            logger_1.logger.info('💡 For other chains, you may need separate API keys:');
            logger_1.logger.info('  - ARBISCAN_API_KEY for Arbitrum');
            logger_1.logger.info('  - BASESCAN_API_KEY for Base');
            logger_1.logger.info('  - OPTIMISM_API_KEY for Optimism');
            logger_1.logger.info('  - POLYGONSCAN_API_KEY for Polygon');
            return;
        }
        // Show warnings
        if (verification.warnings.length > 0) {
            logger_1.logger.mark('⚠️ System warnings detected:');
            verification.warnings.forEach(warning => logger_1.logger.mark(`  ⚠️ ${warning}`));
            logger_1.logger.info('');
        }
        this.isRunning = true;
        logger_1.logger.extract('🎯 SURGICAL PRECISION ACHIEVED - Initiating excavation protocol...');
        // Get valid chains
        const validChains = config_1.CONFIG.chains.filter(chain => chain.rpcUrl && chain.etherscanApiKey);
        logger_1.logger.ghost(`Scanning ${validChains.length} chains for abandoned relics...`);
        try {
            for (const chain of validChains) {
                await this.scanChain(chain);
                // Ghost mode: silent operation between chains
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        catch (error) {
            logger_1.logger.error('Excavation protocol interrupted:', error);
        }
        finally {
            this.isRunning = false;
            logger_1.logger.ghost('Excavation protocol complete. Returning to surveillance mode...');
        }
    }
    async scanAllChainsIncremental() {
        if (this.isRunning) {
            logger_1.logger.warn('Scanner is already running');
            return;
        }
        this.isRunning = true;
        logger_1.logger.info('Starting incremental multi-chain scan...');
        // Validate configuration
        const validChains = config_1.CONFIG.chains.filter(chain => chain.rpcUrl && chain.etherscanApiKey);
        if (validChains.length === 0) {
            logger_1.logger.error('❌ No valid chain configurations found for incremental scan.');
            this.isRunning = false;
            return;
        }
        try {
            for (const chain of validChains) {
                await this.scanChainFromLastBlock(chain);
                // Small delay between chains to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        catch (error) {
            logger_1.logger.error('Incremental multi-chain scan error:', error);
        }
        finally {
            this.isRunning = false;
            logger_1.logger.info('Incremental multi-chain scan completed');
        }
    }
    async continuousScanning(intervalMinutes = 60) {
        logger_1.logger.info(`Starting continuous scanning with ${intervalMinutes} minute intervals`);
        while (true) {
            await this.scanAllChains();
            logger_1.logger.info(`Waiting ${intervalMinutes} minutes before next scan...`);
            await new Promise(resolve => setTimeout(resolve, intervalMinutes * 60 * 1000));
        }
    }
    async scanSpecificContract(address, chainId) {
        const chain = config_1.CONFIG.chains.find(c => c.chainId === chainId);
        if (!chain) {
            logger_1.logger.error(`Chain ${chainId} not configured`);
            return;
        }
        const spinner = (0, ora_1.default)(`🏺 ARCHAEOLOGIST: Deep scanning ${address} on ${chain.name}...`).start();
        try {
            let contract = null;
            // Step 1: Try to fetch as verified contract
            spinner.text = `Checking if ${address} is verified...`;
            contract = await fetcher_1.contractFetcher.fetchContractDetails(chain, address);
            if (!contract) {
                // Step 2: If not verified, try bytecode decompilation
                spinner.text = `🔓 Contract not verified, attempting bytecode analysis...`;
                logger_1.logger.info(`Contract ${address} not verified, attempting bytecode decompilation`);
                const decompilationResult = await decompiler_1.bytecodeDecompiler.decompileContract(address, chainId);
                if (decompilationResult && decompilationResult.hasTargetFunctions) {
                    // Convert decompilation result to ContractInfo with proper ABI
                    const abi = decompilationResult.matchedTargetFunctions.map((targetFunc) => {
                        // Find the decompiled function that matches this target
                        const decompiledFunc = decompilationResult.functions.find(f => f.selector === targetFunc.fourByteSignature);
                        return {
                            name: targetFunc.name, // Use target function name for consistency
                            type: 'function',
                            inputs: decompiledFunc?.inputs ? decompiledFunc.inputs.map((type) => ({ type })) : [],
                            outputs: [],
                            selector: targetFunc.fourByteSignature
                        };
                    });
                    contract = {
                        address: address,
                        name: `Unverified_${address.slice(0, 8)}`,
                        abi: abi,
                        sourceCode: `// Decompiled contract - ${decompilationResult.functions.length} functions found`,
                        compiler: 'decompiled',
                        txHash: '0x0',
                        blockNumber: 0,
                        timestamp: Date.now(),
                        chainId: chainId
                    };
                    logger_1.logger.info(`🎯 Bytecode analysis found ${decompilationResult.matchedTargetFunctions.length} target functions in ${address}`);
                }
            }
            if (!contract) {
                spinner.fail(`Contract ${address} not found, not verified, and bytecode analysis failed`);
                return;
            }
            // Step 3: Filter contract for target functions
            spinner.text = `Analyzing target functions...`;
            const filterResults = filter_1.contractFilter.filterContracts([contract]);
            if (filterResults.length === 0) {
                spinner.fail(`Contract ${address} has no target functions`);
                return;
            }
            logger_1.logger.info(`Found ${filterResults[0].matchedFunctions.length} target functions: ${filterResults[0].matchedFunctions.map(f => f.name).join(', ')}`);
            // Step 4: Simulate functions
            spinner.text = `🎯 Simulating target functions...`;
            const simulationResults = await simulator_1.contractSimulator.simulateContract(filterResults[0]);
            // Step 5: Evaluate results
            const exploitableContracts = await evaluator_1.evaluator.evaluateSimulations(simulationResults);
            if (exploitableContracts.length > 0) {
                spinner.succeed(`💰 Contract ${address} is exploitable with ${exploitableContracts.length} potential exploits`);
                // Show details
                for (const exploit of exploitableContracts) {
                    logger_1.logger.exploit(`${exploit.functionName}: ${exploit.estimatedValue} potential value`);
                }
            }
            else {
                spinner.warn(`Contract ${address} has no profitable exploits (simulations may have failed or gas costs too high)`);
                // Show simulation details for debugging
                logger_1.logger.info(`Simulation results: ${simulationResults.length} total, ${simulationResults.filter(r => r.success).length} successful`);
                for (const result of simulationResults) {
                    logger_1.logger.debug(`${result.functionName}: success=${result.success}, value=${result.potentialValue}, error=${result.error}`);
                }
            }
        }
        catch (error) {
            spinner.fail(`Error scanning contract ${address}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getStats() {
        const stats = await database_1.db.getStats();
        const unexecutedExploits = await database_1.db.getUnexecutedExploits();
        logger_1.logger.info('=== CONTRACT ARCHAEOLOGIST STATS ===');
        logger_1.logger.info(`Total contracts scanned: ${stats.totalContracts}`);
        logger_1.logger.info(`Successful simulations: ${stats.successfulSimulations}`);
        logger_1.logger.info(`Exploitable contracts found: ${stats.exploitableContracts}`);
        logger_1.logger.info(`Successful executions: ${stats.successfulExecutions}`);
        logger_1.logger.info(`Total value extracted: ${stats.totalValueExtracted} ETH`);
        logger_1.logger.info(`Pending exploits: ${unexecutedExploits.length}`);
        if (unexecutedExploits.length > 0) {
            logger_1.logger.info('=== PENDING EXPLOITS ===');
            for (const exploit of unexecutedExploits.slice(0, 10)) {
                logger_1.logger.info(`${exploit.address} - ${exploit.functionName} (${exploit.estimatedValue} value)`);
            }
        }
    }
    async executePendingExploits() {
        const pendingExploits = await database_1.db.getUnexecutedExploits();
        if (pendingExploits.length === 0) {
            logger_1.logger.info('No pending exploits to execute');
            return;
        }
        logger_1.logger.info(`Executing ${pendingExploits.length} pending exploits...`);
        // Execute in batches to avoid overwhelming the network
        const batchSize = 5;
        for (let i = 0; i < pendingExploits.length; i += batchSize) {
            const batch = pendingExploits.slice(i, i + batchSize);
            const results = await executor_1.executor.batchExecuteExploits(batch);
            const successful = results.filter(r => r.success);
            logger_1.logger.info(`Batch ${Math.floor(i / batchSize) + 1}: ${successful.length}/${batch.length} successful`);
        }
    }
    async rescanFailedContracts() {
        logger_1.logger.info('Rescanning contracts with failed simulations...');
        // This would require additional database queries to find failed simulations
        // For now, we'll just log that this feature needs implementation
        logger_1.logger.warn('Rescan feature not yet implemented');
    }
    stop() {
        this.isRunning = false;
        logger_1.logger.info('Scanner stopped');
    }
    // Public getter for dashboard access
    getIsRunning() {
        return this.isRunning;
    }
}
exports.Scanner = Scanner;
exports.scanner = new Scanner();
// CLI interface
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0];
    switch (command) {
        case 'scan':
            exports.scanner.scanAllChains();
            break;
        case 'incremental':
            exports.scanner.scanAllChainsIncremental();
            break;
        case 'continuous':
            const interval = parseInt(args[1]) || 60;
            exports.scanner.continuousScanning(interval);
            break;
        case 'contract':
            const address = args[1];
            const chainId = parseInt(args[2]) || 1;
            if (address) {
                exports.scanner.scanSpecificContract(address, chainId);
            }
            else {
                logger_1.logger.error('Please provide contract address');
            }
            break;
        case 'stats':
            exports.scanner.getStats();
            break;
        case 'execute':
            exports.scanner.executePendingExploits();
            break;
        case 'dashboard':
            logger_1.logger.info('Starting dashboard server...');
            require('./dashboard/server');
            break;
        case 'monitor':
            const { blockMonitor } = require('./monitor');
            blockMonitor.startMonitoring();
            break;
        case 'metrics':
            logger_1.logger.info('Displaying performance metrics...');
            metrics_1.performanceMonitor.displayMetrics();
            break;
        case 'fuzz':
            const fuzzAddress = args[1];
            const fuzzChainId = parseInt(args[2]) || 1;
            const maxIterations = parseInt(args[3]) || 100;
            if (fuzzAddress) {
                logger_1.logger.info(`Starting fuzzing for contract ${fuzzAddress} on chain ${fuzzChainId} with ${maxIterations} iterations`);
                // This would need proper implementation
                logger_1.logger.warn('Fuzzing command not yet fully implemented');
            }
            else {
                logger_1.logger.error('Please provide contract address for fuzzing');
            }
            break;
        default:
            logger_1.logger.info('Usage: ts-node scanner.ts [scan|incremental|continuous|contract|stats|execute|dashboard|monitor|metrics|fuzz]');
            logger_1.logger.info('Commands:');
            logger_1.logger.info('  scan        - Full scan of all chains');
            logger_1.logger.info('  incremental - Incremental scan from last processed block');
            logger_1.logger.info('  continuous  - Continuous scanning with interval');
            logger_1.logger.info('  contract    - Scan specific contract');
            logger_1.logger.info('  stats       - Show statistics');
            logger_1.logger.info('  execute     - Execute pending exploits');
            logger_1.logger.info('  dashboard   - Start web dashboard');
            logger_1.logger.info('  monitor     - Start real-time block monitoring');
            logger_1.logger.info('  metrics     - Show performance metrics');
            logger_1.logger.info('  fuzz        - Fuzz specific contract');
            break;
    }
}
//# sourceMappingURL=scanner.js.map