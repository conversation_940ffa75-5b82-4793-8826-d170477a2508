import { SimulationResult, ExploitableContract } from './types';
export declare class Evaluator {
    evaluateSimulations(simulations: SimulationResult[]): Promise<ExploitableContract[]>;
    private isProfitable;
    /**
     * 🏺 SURGICAL PRECISION: Advanced profitability assessment using economic engine
     */
    private isProfitableSurgical;
    /**
     * 🏺 SURGICAL PRECISION: Calculate priority based on multiple factors
     */
    private calculateSurgicalPriority;
    evaluateBatch(simulationResults: SimulationResult[]): ExploitableContract[];
    private createExploit;
}
export declare const evaluator: Evaluator;
//# sourceMappingURL=evaluator.d.ts.map