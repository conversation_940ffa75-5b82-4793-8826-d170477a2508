/**
 * 🏺 SURGICAL PRECISION: Simulation Precision Engine
 *
 * This module eliminates ALL guesswork from function simulation.
 * Uses deterministic parameter generation and comprehensive edge case testing.
 *
 * Features:
 * 1. Function-specific parameter strategies
 * 2. Deterministic parameter generation (no random values)
 * 3. Edge case testing (0, max values, common patterns)
 * 4. Multi-scenario simulation
 * 5. Result validation and verification
 *
 * NO GUESSES. SURGICAL PRECISION.
 */
import { ethers } from 'ethers';
export interface SimulationStrategy {
    functionName: string;
    signature: string;
    parameterSets: ParameterSet[];
    description: string;
}
export interface ParameterSet {
    name: string;
    parameters: any[];
    description: string;
    priority: number;
}
export interface PrecisionSimulationResult {
    functionName: string;
    signature: string;
    successful: boolean;
    results: SimulationAttempt[];
    bestResult?: SimulationAttempt;
    confidence: number;
    extractionPotential: number;
}
export interface SimulationAttempt {
    parameterSet: ParameterSet;
    success: boolean;
    returnData?: string;
    gasUsed?: bigint;
    error?: string;
    extractedValue?: bigint;
    balanceChange?: bigint;
    confidence: number;
}
declare class PrecisionSimulator {
    private strategies;
    constructor();
    /**
     * SURGICAL PRECISION: Initialize deterministic parameter strategies
     */
    private initializeStrategies;
    /**
     * SURGICAL PRECISION: Simulate function with deterministic parameters
     */
    simulateFunction(provider: ethers.Provider, contractAddress: string, functionSignature: string, abi: any[], fromAddress: string): Promise<PrecisionSimulationResult>;
    /**
     * Calculate confidence for individual attempt
     */
    private calculateAttemptConfidence;
    /**
     * Calculate overall confidence
     */
    private calculateOverallConfidence;
    /**
     * Calculate extraction potential
     */
    private calculateExtractionPotential;
    /**
     * 🏺 DUST COLLECTION: Enhanced value extraction with balance monitoring
     */
    private extractValueFromResult;
    /**
     * Check if error message is informative
     */
    private isInformativeError;
    /**
     * Check if error is due to function not found
     */
    private isFunctionNotFoundError;
    /**
     * Check if error is due to access control
     */
    private isAccessControlError;
    /**
     * 🏺 DUST COLLECTION: Check if error indicates already claimed
     */
    private isAlreadyClaimedError;
    /**
     * 🏺 DUST COLLECTION: Check if error is timing-related
     */
    private isTimingError;
    /**
     * 🏺 DUST COLLECTION: Categorize error for high-value contracts
     */
    private categorizeError;
    /**
     * Find alternative function signatures in the contract
     */
    private findAlternativeFunctions;
    /**
     * Get strategy for function
     */
    getStrategy(functionSignature: string): SimulationStrategy | undefined;
    /**
     * Get all available strategies
     */
    getAllStrategies(): SimulationStrategy[];
}
export declare const precisionSimulator: PrecisionSimulator;
export {};
//# sourceMappingURL=precision-simulator.d.ts.map