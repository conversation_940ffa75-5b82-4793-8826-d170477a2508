{"version": 3, "file": "signature-verifier.js", "sourceRoot": "", "sources": ["../src/signature-verifier.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;GAWG;;;AAEH,mCAAgC;AAChC,qCAAkC;AAkBlC,MAAM,iBAAiB;IAAvB;QACU,UAAK,GAAG,IAAI,GAAG,EAAiC,CAAC;IAqJ3D,CAAC;IApJC,gFAAgF;IAEhF;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,gBAAyB;QAChE,MAAM,QAAQ,GAAG,GAAG,SAAS,IAAI,gBAAgB,IAAI,EAAE,EAAE,CAAC;QAE1D,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QACnC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,wBAAwB,SAAS,EAAE,CAAC,CAAC;QAElD,MAAM,YAAY,GAA0B;YAC1C,SAAS;YACT,iBAAiB,EAAE,EAAE;YACrB,UAAU,EAAE,KAAK;YACjB,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,CAAC;SACd,CAAC;QAEF,MAAM,OAAO,GAAyB,EAAE,CAAC;QAEzC,qDAAqD;QACrD,IAAI,CAAC;YACH,MAAM,kBAAkB,GAAG,eAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC7D,YAAY,CAAC,iBAAiB,GAAG,kBAAkB,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,0BAA0B;gBAChC,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,kBAAkB;aAC9B,CAAC,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,8BAA8B,SAAS,MAAM,kBAAkB,EAAE,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC;YAC5E,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,0BAA0B;gBAChC,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;QACL,CAAC;QAED,qEAAqE;QACrE,8DAA8D;QAC9D,eAAM,CAAC,KAAK,CAAC,sEAAsE,CAAC,CAAC;QAErF,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,4BAA4B;YAClC,QAAQ,EAAE,IAAI,EAAE,0CAA0C;YAC1D,SAAS,EAAE,YAAY,CAAC,iBAAiB,CAAC,uBAAuB;SAClE,CAAC,CAAC;QAEH,sDAAsD;QACtD,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,OAAO,GAAG,gBAAgB,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAChG,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,gBAAgB;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,gBAAgB,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,qEAAqE;QACrE,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACxD,YAAY,CAAC,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACxD,YAAY,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,sDAAsD;QACrF,YAAY,CAAC,UAAU,GAAG,YAAY,CAAC,iBAAiB,KAAK,EAAE,CAAC,CAAC,kCAAkC;QAEnG,cAAc;QACd,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;YAC5B,eAAM,CAAC,OAAO,CAAC,iCAAiC,SAAS,MAAM,YAAY,CAAC,iBAAiB,oBAAoB,CAAC,CAAC;QACrH,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,uCAAuC,SAAS,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACvC,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,4DAA4D;IAC5D,wDAAwD;IAExD;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,eAAsB;QAUnD,eAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;QAE/E,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAC7D,CACF,CAAC;QAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAClD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAC9B,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CACnE,CAAC;QAEF,MAAM,OAAO,GAAG;YACd,KAAK,EAAE,OAAO,CAAC,MAAM;YACrB,QAAQ,EAAE,QAAQ,CAAC,MAAM;YACzB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,UAAU,EAAE,aAAa;SAC1B,CAAC;QAEF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,eAAM,CAAC,OAAO,CAAC,8BAA8B,QAAQ,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,qCAAqC,CAAC,CAAC;QACvH,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,wCAAwC,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,aAAa,CAAC,CAAC;YAClG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBACjB,eAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,oCAAoC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,SAAiB;QACrC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;QACpF,OAAO,MAAM,IAAI,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,eAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAC7C,CAAC;CACF;AAEY,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC"}