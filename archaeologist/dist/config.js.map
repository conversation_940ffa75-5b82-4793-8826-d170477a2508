{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": ";;;;;;AACA,oDAA4B;AAE5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEH,QAAA,gBAAgB,GAAqB;IAChD;QACE,IAAI,EAAE,OAAO;QACb,SAAS,EAAE,SAAS;QACpB,iBAAiB,EAAE,YAAY;QAC/B,WAAW,EAAE,yBAAyB;QACtC,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,YAAY;QACvB,iBAAiB,EAAE,YAAY,EAAG,iDAAiD;QACnF,WAAW,EAAE,eAAe;QAC5B,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,QAAQ;QACnB,iBAAiB,EAAE,YAAY,EAAG,2BAA2B;QAC7D,WAAW,EAAE,gBAAgB;QAC7B,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,mBAAmB;QAC9B,iBAAiB,EAAE,YAAY,EAAG,2BAA2B;QAC7D,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,WAAW;QACtB,iBAAiB,EAAE,YAAY,EAAG,2BAA2B;QAC7D,WAAW,EAAE,UAAU;QACvB,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,YAAY;QACvB,iBAAiB,EAAE,YAAY;QAC/B,WAAW,EAAE,yBAAyB;QACtC,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE,UAAU;QACrB,iBAAiB,EAAE,YAAY;QAC/B,WAAW,EAAE,qBAAqB;QAClC,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,OAAO;QACb,SAAS,EAAE,SAAS;QACpB,iBAAiB,EAAE,YAAY;QAC/B,WAAW,EAAE,iCAAiC;QAC9C,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,cAAc;QACpB,SAAS,EAAE,gBAAgB;QAC3B,iBAAiB,EAAE,YAAY;QAC/B,WAAW,EAAE,8BAA8B;QAC3C,QAAQ,EAAE,CAAC;KACZ;IACD,+BAA+B;IAC/B;QACE,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,mBAAmB;QAC9B,iBAAiB,EAAE,YAAY;QAC/B,WAAW,EAAE,2BAA2B;QACxC,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,aAAa;QACnB,SAAS,EAAE,eAAe;QAC1B,iBAAiB,EAAE,YAAY,EAAG,2BAA2B;QAC7D,WAAW,EAAE,gBAAgB;QAC7B,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,IAAI,EAAE,mBAAmB;QACzB,SAAS,EAAE,qBAAqB;QAChC,iBAAiB,EAAE,YAAY,EAAG,2BAA2B;QAC7D,WAAW,EAAE,uBAAuB;QACpC,QAAQ,EAAE,CAAC;KACZ;CACF,CAAC;AAEW,QAAA,aAAa,GAAkB;IAC1C;QACE,OAAO,EAAE,CAAC;QACV,IAAI,EAAE,UAAU;QAChB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;QAC1C,eAAe,EAAE,iCAAiC;QAClD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;KACrD;IACD;QACE,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,UAAU;QAChB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;QAC1C,eAAe,EAAE,iCAAiC;QAClD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;KACrD;IACD;QACE,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE;QACtC,eAAe,EAAE,iCAAiC;QAClD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;KACrD;IACD;QACE,OAAO,EAAE,EAAE;QACX,IAAI,EAAE,UAAU;QAChB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;QAC1C,eAAe,EAAE,iCAAiC;QAClD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;KACrD;IACD;QACE,OAAO,EAAE,GAAG;QACZ,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE;QACzC,eAAe,EAAE,iCAAiC;QAClD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;KACrD;CACF,CAAC;AAEW,QAAA,MAAM,GAAW;IAC5B,MAAM,EAAE,qBAAa;IACrB,eAAe,EAAE,wBAAgB;IACjC,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,KAAK,CAAC;IAC7D,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,MAAM,CAAC;IACrE,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,aAAa,CAAC;IAC/D,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;IACzC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,yBAAyB;IACxD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,MAAM;IACzC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB;CAC/C,CAAC;AAEW,QAAA,sBAAsB,GAAG;IACpC,SAAS;IACT,OAAO;IACP,UAAU;IACV,QAAQ;IACR,OAAO;IACP,UAAU;IACV,MAAM;IACN,KAAK;IACL,OAAO;IACP,SAAS;IACT,SAAS;IACT,IAAI;IACJ,WAAW;CACZ,CAAC;AAEF,6CAA6C;AAC7C,6EAA6E;AAC7E,2DAA2D;AAC9C,QAAA,oBAAoB,GAAG,GAAG,CAAC,CAAC,+DAA+D;AAC3F,QAAA,oBAAoB,GAAG,CAAC,CAAC,CAAC,oCAAoC"}