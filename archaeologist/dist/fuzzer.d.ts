import { ContractInfo, SimulationResult } from './types';
export interface FuzzingStrategy {
    name: string;
    description: string;
    generateParameters(abiFunction: any): any[][];
    priority: number;
}
export interface FuzzingResult {
    strategy: string;
    parameters: any[];
    result: SimulationResult;
    interestingBehavior: boolean;
    potentialExploit: boolean;
    riskScore: number;
}
export declare class ContractFuzzer {
    private strategies;
    private providers;
    constructor();
    private initializeProviders;
    private initializeStrategies;
    fuzzContract(contract: ContractInfo, targetFunctions: string[], maxIterations?: number): Promise<FuzzingResult[]>;
    private fuzzFunction;
    private simulateWithParameters;
    private formatReturnData;
    private extractPotentialValue;
    private isInterestingBehavior;
    private isPotentialExploit;
    private isFalsePositive;
    private calculateRiskScore;
    private analyzeResults;
    private validatePotentialExploit;
    private validateOverflowResult;
    private validateReentrancyResult;
    private saveFuzzingResults;
    batchFuzz(contracts: ContractInfo[], maxIterationsPerContract?: number): Promise<Map<string, FuzzingResult[]>>;
}
export declare const contractFuzzer: ContractFuzzer;
//# sourceMappingURL=fuzzer.d.ts.map