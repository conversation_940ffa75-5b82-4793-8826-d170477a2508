import { ContractInfo, ChainConfig } from './types';
export declare class ContractFetcher {
    private requestTimes;
    private readonly maxRequestsPerSecond;
    private rateLimit;
    private processAddressesBatch;
    fetchRecentVerifiedContracts(chain: ChainConfig, page?: number, offset?: number): Promise<ContractInfo[]>;
    private getRecentContractAddresses;
    fetchContractDetails(chain: ChainConfig, address: string): Promise<ContractInfo | null>;
    private getContractCreationInfo;
    fetchContractsByBlockRange(chain: ChainConfig, fromBlock: number, toBlock: number): Promise<ContractInfo[]>;
    fetchTopContracts(chain: ChainConfig, limit?: number): Promise<ContractInfo[]>;
    private fetchProtocolContracts;
    private fetchContractsFromBlockRanges;
    private fetchContractsFromRecentBlocks;
    fetchUnverifiedContracts(chain: ChainConfig, limit?: number): Promise<ContractInfo[]>;
    private getActiveContractAddressesFromEvents;
    private getRecentContractCreations;
    private filterUnverifiedContracts;
    private createContractInfoFromDecompilation;
    private checkContractValue;
    private prioritizeContractsByValue;
}
export declare const contractFetcher: ContractFetcher;
//# sourceMappingURL=fetcher.d.ts.map