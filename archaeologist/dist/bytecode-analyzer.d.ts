/**
 * 🏺 SURGICAL PRECISION: Enhanced Bytecode Analysis System
 *
 * This module provides multi-source bytecode analysis for unverified contracts.
 * Integrates proven open-source decompilers and analysis tools for maximum accuracy.
 *
 * Sources:
 * 1. Heimdall-rs (Primary decompiler)
 * 2. Dedaub API (Commercial grade)
 * 3. EtherVM (Backup)
 * 4. Internal bytecode pattern matching
 *
 * NO GUESSES. SURGICAL PRECISION.
 */
export interface BytecodeAnalysis {
    address: string;
    chainId: number;
    bytecode: string;
    functions: DetectedFunction[];
    decompilationSources: string[];
    confidence: number;
    isVerified: boolean;
    analysis: {
        hasTargetFunctions: boolean;
        targetFunctions: string[];
        riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
        extractionPotential: number;
    };
}
export interface DetectedFunction {
    signature: string;
    fourByteSignature: string;
    confidence: number;
    source: string;
    parameters?: string[];
    isPayable?: boolean;
    isView?: boolean;
}
declare class BytecodeAnalyzer {
    private cache;
    private readonly DEDAUB_API;
    private readonly ETHERVM_API;
    /**
     * SURGICAL PRECISION: Analyze bytecode with multiple decompilers
     */
    analyzeBytecode(address: string, chainId: number, bytecode?: string): Promise<BytecodeAnalysis>;
    /**
     * Fetch bytecode from chain
     */
    private fetchBytecode;
    /**
     * Analyze with Heimdall-rs (if available)
     */
    private analyzeWithHeimdall;
    /**
     * Analyze with Dedaub API (if available)
     */
    private analyzeWithDedaub;
    /**
     * Internal pattern matching for target functions
     */
    private analyzeWithInternalPatterns;
    /**
     * Parse Heimdall-rs output
     */
    private parseHeimdallOutput;
    /**
     * Parse Dedaub output
     */
    private parseDedaubOutput;
    /**
     * Calculate overall confidence
     */
    private calculateConfidence;
    /**
     * Analyze for target functions
     */
    private analyzeTargetFunctions;
    /**
     * Calculate extraction potential
     */
    private calculateExtractionPotential;
    /**
     * Get cached analysis
     */
    getCachedAnalysis(address: string, chainId: number): BytecodeAnalysis | null;
    /**
     * Clear analysis cache
     */
    clearCache(): void;
}
export declare const bytecodeAnalyzer: BytecodeAnalyzer;
export {};
//# sourceMappingURL=bytecode-analyzer.d.ts.map