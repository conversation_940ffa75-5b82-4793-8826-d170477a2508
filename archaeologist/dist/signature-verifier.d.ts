/**
 * 🏺 SURGICAL PRECISION: Function Signature Verification System
 *
 * This module ensures 100% accuracy of function signatures using ONLY
 * mathematical verification. No external sources. No guesses. No false positives.
 *
 * Sources:
 * 1. Mathematical verification (ethers.js) - GROUND TRUTH
 * 2. Expected value comparison (if provided)
 *
 * External APIs like 4byte.directory have been proven unreliable and removed.
 */
export interface SignatureVerification {
    signature: string;
    fourByteSignature: string;
    isVerified: boolean;
    sources: string[];
    conflicts: string[];
    confidence: number;
}
export interface VerificationSource {
    name: string;
    url?: string;
    verified: boolean;
    signature?: string;
}
declare class SignatureVerifier {
    private cache;
    /**
     * SURGICAL PRECISION: Verify function signature with multiple sources
     */
    verifySignature(signature: string, expectedFourByte?: string): Promise<SignatureVerification>;
    /**
     * SURGICAL PRECISION: Verify all target functions in configuration
     */
    verifyAllTargetFunctions(targetFunctions: any[]): Promise<{
        verified: SignatureVerification[];
        failed: SignatureVerification[];
        summary: {
            total: number;
            verified: number;
            failed: number;
            confidence: number;
        };
    }>;
    /**
     * Get verification status for a specific signature
     */
    getVerificationStatus(signature: string): SignatureVerification | null;
    /**
     * Clear verification cache
     */
    clearCache(): void;
}
export declare const signatureVerifier: SignatureVerifier;
export {};
//# sourceMappingURL=signature-verifier.d.ts.map