import { ChainConfig } from './types';
export declare class Scanner {
    private isRunning;
    /**
     * 🏺 SURGICAL PRECISION: Comprehensive system verification
     */
    private performSurgicalVerification;
    private validateConfiguration;
    scanChain(chain: ChainConfig): Promise<void>;
    scanChainFromLastBlock(chain: ChainConfig): Promise<void>;
    scanAllChains(): Promise<void>;
    scanAllChainsIncremental(): Promise<void>;
    continuousScanning(intervalMinutes?: number): Promise<void>;
    scanSpecificContract(address: string, chainId: number): Promise<void>;
    getStats(): Promise<void>;
    executePendingExploits(): Promise<void>;
    rescanFailedContracts(): Promise<void>;
    stop(): void;
    getIsRunning(): boolean;
}
export declare const scanner: Scanner;
//# sourceMappingURL=scanner.d.ts.map