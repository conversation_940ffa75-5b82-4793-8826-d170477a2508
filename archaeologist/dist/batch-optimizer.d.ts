/**
 * 🏺 DUST COLLECTION: Batch Optimization System
 * Combines multiple small extractions into single transactions for gas efficiency
 */
export interface BatchableExtraction {
    contractAddress: string;
    functionName: string;
    parameters: any[];
    estimatedValue: bigint;
    gasEstimate: number;
    chainId: number;
}
export interface BatchResult {
    totalValue: bigint;
    totalGas: number;
    netProfit: bigint;
    extractions: BatchableExtraction[];
    multicallData: string;
}
export declare class BatchOptimizer {
    private readonly MULTICALL_ADDRESS;
    private readonly MIN_BATCH_SIZE;
    private readonly MAX_BATCH_SIZE;
    private readonly GAS_OVERHEAD_PER_CALL;
    /**
     * 🏺 DUST COLLECTION: Optimize extractions by batching compatible ones
     */
    optimizeExtractions(extractions: BatchableExtraction[]): Promise<BatchResult[]>;
    /**
     * 🏺 DUST COLLECTION: Group extractions by blockchain
     */
    private groupByChain;
    /**
     * 🏺 DUST COLLECTION: Create optimal batches using greedy algorithm
     */
    private createOptimalBatches;
    /**
     * 🏺 DUST COLLECTION: Create a single optimized batch
     */
    private createSingleBatch;
    /**
     * 🏺 DUST COLLECTION: Create batch of specific size
     */
    private createBatchOfSize;
    /**
     * 🏺 DUST COLLECTION: Generate multicall transaction data
     */
    private generateMulticallData;
    /**
     * 🏺 DUST COLLECTION: Get parameter types for function encoding
     */
    private getParameterTypes;
    /**
     * 🏺 DUST COLLECTION: Check if single extraction is profitable
     */
    private isSingleExtractionProfitable;
    /**
     * 🏺 DUST COLLECTION: Create batch for single extraction
     */
    private createSingleExtractionBatch;
    /**
     * 🏺 DUST COLLECTION: Log batch optimization results
     */
    logBatchResults(batches: BatchResult[]): void;
}
//# sourceMappingURL=batch-optimizer.d.ts.map