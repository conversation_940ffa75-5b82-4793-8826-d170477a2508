import { ContractInfo, TargetFunction } from './types';
export interface FilterResult {
    contract: ContractInfo;
    matchedFunctions: TargetFunction[];
    priorityScore: number;
    hasHighPriorityKeywords: boolean;
}
export declare class ContractFilter {
    private targetFunctions;
    constructor(targetFunctions?: TargetFunction[]);
    filterContracts(contracts: ContractInfo[]): FilterResult[];
    private analyzeContract;
    private matchesTargetFunction;
    private generateFunctionSignature;
    private generateFourByteSignature;
    private hasHighPriorityKeywords;
    filterByChain(results: FilterResult[], chainId: number): FilterResult[];
    filterByPriority(results: FilterResult[], maxPriority: number): FilterResult[];
    filterByKeywords(results: FilterResult[], keywords: string[]): FilterResult[];
    filterByFunctionName(results: FilterResult[], functionName: string): FilterResult[];
    filterByFunctionCombination(results: FilterResult[], requiredFunctions: string[]): FilterResult[];
    filterDAOContracts(results: FilterResult[]): FilterResult[];
    filterVaultContracts(results: FilterResult[]): FilterResult[];
    filterAirdropContracts(results: FilterResult[]): FilterResult[];
    getFilterStats(results: FilterResult[]): any;
}
export declare const contractFilter: ContractFilter;
//# sourceMappingURL=filter.d.ts.map