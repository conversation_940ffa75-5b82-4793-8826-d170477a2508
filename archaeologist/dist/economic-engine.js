"use strict";
/**
 * 🏺 SURGICAL PRECISION: Economic Calculation Engine
 *
 * This module provides accurate economic calculations with real-time data feeds.
 * Eliminates guesswork in profitability assessment and risk calculation.
 *
 * Features:
 * 1. Real-time gas price feeds from multiple sources
 * 2. Accurate profit calculations with 2x safety margin
 * 3. Multi-chain gas price tracking
 * 4. Risk assessment with confidence intervals
 * 5. MEV protection cost calculation
 *
 * NO GUESSES. SURGICAL PRECISION.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.economicEngine = void 0;
const axios_1 = __importDefault(require("axios"));
const ethers_1 = require("ethers");
const logger_1 = require("./logger");
const config_1 = require("./config");
class EconomicEngine {
    constructor() {
        this.gasPriceCache = new Map();
        this.CACHE_DURATION = 30000; // 30 seconds
        this.PROFIT_THRESHOLD = process.env.PROFIT_THRESHOLD ?
            parseFloat(process.env.PROFIT_THRESHOLD) : 2; // Default 2x minimum profit
        this.FLASHBOTS_TIP_PERCENTAGE = 0.1; // 10% tip for MEV protection
    }
    /**
     * SURGICAL PRECISION: Comprehensive economic analysis
     */
    async analyzeEconomics(chainId, estimatedValue, estimatedGas, riskFactors = []) {
        logger_1.logger.relic(`💰 Economic analysis for chain ${chainId}`);
        // Get real-time gas prices
        const gasPriceData = await this.getGasPrices(chainId);
        // Calculate costs
        const gasCost = gasPriceData.gasPrice * estimatedGas;
        const flashbotsTip = gasCost * BigInt(Math.round(this.FLASHBOTS_TIP_PERCENTAGE * 100)) / 100n;
        const totalCost = gasCost + flashbotsTip;
        // Calculate profitability
        const netProfit = estimatedValue > totalCost ? estimatedValue - totalCost : 0n;
        const profitMargin = estimatedValue > 0n ?
            Number((netProfit * 10000n) / estimatedValue) / 100 : 0;
        const meetsThreshold = netProfit >= totalCost * BigInt(this.PROFIT_THRESHOLD - 1);
        // Risk assessment
        const riskAssessment = this.assessRisk(estimatedValue, totalCost, netProfit, riskFactors, chainId);
        const analysis = {
            chainId,
            gasPrice: {
                current: gasPriceData.gasPrice,
                fast: gasPriceData.fast,
                standard: gasPriceData.standard,
                safe: gasPriceData.safe,
                source: gasPriceData.source,
                timestamp: gasPriceData.timestamp
            },
            costs: {
                estimatedGas,
                gasCost,
                flashbotsTip,
                totalCost
            },
            profitability: {
                estimatedValue,
                netProfit,
                profitMargin,
                meetsThreshold,
                riskAdjustedProfit: this.calculateRiskAdjustedProfit(netProfit, riskAssessment.level)
            },
            risk: riskAssessment
        };
        this.logEconomicAnalysis(analysis);
        return analysis;
    }
    /**
     * Get real-time gas prices from multiple sources
     */
    async getGasPrices(chainId) {
        const cached = this.gasPriceCache.get(chainId);
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
            return cached;
        }
        logger_1.logger.relic(`⛽ Fetching real-time gas prices for chain ${chainId}`);
        // Try multiple sources for gas prices
        const gasPriceData = await this.fetchGasPricesMultiSource(chainId);
        this.gasPriceCache.set(chainId, gasPriceData);
        return gasPriceData;
    }
    /**
     * Fetch gas prices from multiple sources for accuracy
     */
    async fetchGasPricesMultiSource(chainId) {
        const sources = [
            () => this.fetchFromEthGasStation(chainId),
            () => this.fetchFromRPC(chainId),
            () => this.fetchFromOwlracle(chainId)
        ];
        let lastError = null;
        for (const source of sources) {
            try {
                const data = await source();
                if (data) {
                    logger_1.logger.relic(`⛽ Gas prices fetched from ${data.source}`);
                    return data;
                }
            }
            catch (error) {
                lastError = error;
                logger_1.logger.debug(`Gas price source failed: ${error}`);
            }
        }
        // Fallback to default values
        logger_1.logger.mark(`⚠️ All gas price sources failed, using fallback values`);
        return this.getFallbackGasPrices(chainId);
    }
    /**
     * Fetch from ETH Gas Station (Ethereum mainnet)
     */
    async fetchFromEthGasStation(chainId) {
        if (chainId !== 1)
            return null; // Only for Ethereum mainnet
        try {
            const response = await axios_1.default.get('https://ethgasstation.info/api/ethgasAPI.json', {
                timeout: 5000
            });
            const data = response.data;
            return {
                chainId,
                gasPrice: ethers_1.ethers.parseUnits((data.fast / 10).toString(), 'gwei'),
                fast: ethers_1.ethers.parseUnits((data.fastest / 10).toString(), 'gwei'),
                standard: ethers_1.ethers.parseUnits((data.average / 10).toString(), 'gwei'),
                safe: ethers_1.ethers.parseUnits((data.safeLow / 10).toString(), 'gwei'),
                source: 'ETH Gas Station',
                timestamp: Date.now()
            };
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Fetch from RPC provider
     */
    async fetchFromRPC(chainId) {
        try {
            const chain = config_1.CONFIG.chains.find(c => c.chainId === chainId);
            if (!chain?.rpcUrl)
                return null;
            const provider = new ethers_1.ethers.JsonRpcProvider(chain.rpcUrl);
            const feeData = await provider.getFeeData();
            if (!feeData.gasPrice)
                return null;
            // Estimate fast/standard/safe based on current gas price
            const basePrice = feeData.gasPrice;
            return {
                chainId,
                gasPrice: basePrice,
                fast: basePrice * 120n / 100n, // 20% higher
                standard: basePrice,
                safe: basePrice * 80n / 100n, // 20% lower
                source: 'RPC Provider',
                timestamp: Date.now()
            };
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Fetch from Owlracle (multi-chain)
     */
    async fetchFromOwlracle(chainId) {
        try {
            const networkMap = {
                1: 'eth',
                137: 'poly',
                42161: 'arb',
                10: 'op',
                8453: 'base'
            };
            const network = networkMap[chainId];
            if (!network)
                return null;
            const response = await axios_1.default.get(`https://api.owlracle.info/v4/${network}/gas`, {
                timeout: 5000
            });
            const data = response.data;
            return {
                chainId,
                gasPrice: ethers_1.ethers.parseUnits(data.speeds[1].gasPrice.toString(), 'gwei'), // Standard
                fast: ethers_1.ethers.parseUnits(data.speeds[0].gasPrice.toString(), 'gwei'),
                standard: ethers_1.ethers.parseUnits(data.speeds[1].gasPrice.toString(), 'gwei'),
                safe: ethers_1.ethers.parseUnits(data.speeds[2].gasPrice.toString(), 'gwei'),
                source: 'Owlracle',
                timestamp: Date.now()
            };
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Fallback gas prices when all sources fail
     */
    getFallbackGasPrices(chainId) {
        const fallbackPrices = {
            1: ethers_1.ethers.parseUnits('20', 'gwei'), // Ethereum
            137: ethers_1.ethers.parseUnits('30', 'gwei'), // Polygon
            42161: ethers_1.ethers.parseUnits('0.1', 'gwei'), // Arbitrum
            10: ethers_1.ethers.parseUnits('0.001', 'gwei'), // Optimism
            8453: ethers_1.ethers.parseUnits('0.001', 'gwei') // Base
        };
        const basePrice = fallbackPrices[chainId] || ethers_1.ethers.parseUnits('20', 'gwei');
        return {
            chainId,
            gasPrice: basePrice,
            fast: basePrice * 120n / 100n,
            standard: basePrice,
            safe: basePrice * 80n / 100n,
            source: 'Fallback',
            timestamp: Date.now()
        };
    }
    /**
     * Assess risk factors
     */
    assessRisk(estimatedValue, totalCost, netProfit, riskFactors, chainId) {
        let riskScore = 0;
        const factors = [];
        // Value-based risk
        if (estimatedValue < totalCost) {
            riskScore += 40;
            factors.push('Estimated value below cost');
        }
        // Profit margin risk
        const profitMargin = estimatedValue > 0n ?
            Number((netProfit * 10000n) / estimatedValue) / 100 : 0;
        if (profitMargin < 100) { // Less than 2x profit
            riskScore += 30;
            factors.push('Low profit margin');
        }
        // Chain-specific risk
        if (chainId !== 1) { // Non-Ethereum chains
            riskScore += 10;
            factors.push('Non-Ethereum chain');
        }
        // External risk factors
        riskScore += riskFactors.length * 15;
        factors.push(...riskFactors);
        // Determine risk level
        let level;
        if (riskScore >= 80)
            level = 'EXTREME';
        else if (riskScore >= 60)
            level = 'HIGH';
        else if (riskScore >= 30)
            level = 'MEDIUM';
        else
            level = 'LOW';
        return {
            level,
            factors,
            confidence: Math.max(0, 100 - riskScore),
            maxLoss: totalCost
        };
    }
    /**
     * Calculate risk-adjusted profit
     */
    calculateRiskAdjustedProfit(netProfit, riskLevel) {
        const riskMultipliers = {
            'LOW': 0.95,
            'MEDIUM': 0.8,
            'HIGH': 0.6,
            'EXTREME': 0.3
        };
        const multiplier = riskMultipliers[riskLevel] || 0.5;
        return netProfit * BigInt(Math.round(multiplier * 100)) / 100n;
    }
    /**
     * Log economic analysis
     */
    logEconomicAnalysis(analysis) {
        const { profitability, costs, risk } = analysis;
        logger_1.logger.relic(`💰 Economic Analysis:`);
        logger_1.logger.relic(`   Gas Price: ${ethers_1.ethers.formatUnits(analysis.gasPrice.current, 'gwei')} gwei (${analysis.gasPrice.source})`);
        logger_1.logger.relic(`   Total Cost: ${ethers_1.ethers.formatEther(costs.totalCost)} ETH`);
        logger_1.logger.relic(`   Estimated Value: ${ethers_1.ethers.formatEther(profitability.estimatedValue)} ETH`);
        logger_1.logger.relic(`   Net Profit: ${ethers_1.ethers.formatEther(profitability.netProfit)} ETH`);
        logger_1.logger.relic(`   Profit Margin: ${profitability.profitMargin.toFixed(2)}%`);
        logger_1.logger.relic(`   Risk Level: ${risk.level} (${risk.confidence}% confidence)`);
        if (profitability.meetsThreshold) {
            logger_1.logger.extract(`✅ MEETS 2X PROFIT THRESHOLD - EXTRACTION VIABLE`);
        }
        else {
            logger_1.logger.mark(`❌ Below 2x profit threshold - extraction not recommended`);
        }
    }
    /**
     * Clear gas price cache
     */
    clearCache() {
        this.gasPriceCache.clear();
        logger_1.logger.ghost('Economic engine cache cleared');
    }
}
exports.economicEngine = new EconomicEngine();
//# sourceMappingURL=economic-engine.js.map