"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.contractFilter = exports.ContractFilter = void 0;
const ethers_1 = require("ethers");
const config_1 = require("./config");
const logger_1 = require("./logger");
class ContractFilter {
    constructor(targetFunctions = config_1.TARGET_FUNCTIONS) {
        this.targetFunctions = targetFunctions;
    }
    filterContracts(contracts) {
        const results = [];
        for (const contract of contracts) {
            const filterResult = this.analyzeContract(contract);
            if (filterResult.matchedFunctions.length > 0) {
                results.push(filterResult);
            }
        }
        // Sort by priority score (lower is better)
        results.sort((a, b) => {
            if (a.priorityScore !== b.priorityScore) {
                return a.priorityScore - b.priorityScore;
            }
            // If same priority, prefer contracts with high priority keywords
            if (a.hasHighPriorityKeywords !== b.hasHighPriorityKeywords) {
                return a.hasHighPriorityKeywords ? -1 : 1;
            }
            // Finally, prefer contracts with more matched functions
            return b.matchedFunctions.length - a.matchedFunctions.length;
        });
        logger_1.logger.info(`Filtered ${results.length} contracts with target functions from ${contracts.length} total contracts`);
        return results;
    }
    analyzeContract(contract) {
        const matchedFunctions = [];
        let priorityScore = 10; // Default high priority score
        let hasHighPriorityKeywords = false;
        // Check for target functions in ABI
        for (const abiItem of contract.abi) {
            if (abiItem.type === 'function') {
                const functionSignature = this.generateFunctionSignature(abiItem);
                const fourByteSignature = this.generateFourByteSignature(functionSignature);
                // Check if this function matches any target function
                for (const targetFunction of this.targetFunctions) {
                    if (this.matchesTargetFunction(abiItem, targetFunction, fourByteSignature)) {
                        matchedFunctions.push(targetFunction);
                        // Update priority score to the lowest (best) among matched functions
                        priorityScore = Math.min(priorityScore, targetFunction.priority);
                    }
                }
            }
        }
        // Check for high priority keywords in contract name and source code
        hasHighPriorityKeywords = this.hasHighPriorityKeywords(contract);
        // Boost priority for contracts with high priority keywords
        if (hasHighPriorityKeywords && priorityScore > 1) {
            priorityScore = Math.max(1, priorityScore - 1);
        }
        return {
            contract,
            matchedFunctions,
            priorityScore,
            hasHighPriorityKeywords
        };
    }
    matchesTargetFunction(abiItem, targetFunction, fourByteSignature) {
        // Check by function name
        if (abiItem.name === targetFunction.name) {
            return true;
        }
        // Check by 4-byte signature
        if (fourByteSignature === targetFunction.fourByteSignature) {
            return true;
        }
        // Check by full signature
        const fullSignature = this.generateFunctionSignature(abiItem);
        if (fullSignature === targetFunction.signature) {
            return true;
        }
        return false;
    }
    generateFunctionSignature(abiItem) {
        const inputs = abiItem.inputs || [];
        const paramTypes = inputs.map((input) => input.type).join(',');
        return `${abiItem.name}(${paramTypes})`;
    }
    generateFourByteSignature(signature) {
        // Use ethers.js to generate proper Keccak-256 hash
        const hash = ethers_1.ethers.keccak256(ethers_1.ethers.toUtf8Bytes(signature));
        return hash.substring(0, 10); // First 4 bytes (8 hex chars) plus '0x'
    }
    hasHighPriorityKeywords(contract) {
        const searchText = `${contract.name} ${contract.sourceCode}`.toLowerCase();
        return config_1.HIGH_PRIORITY_KEYWORDS.some(keyword => searchText.includes(keyword.toLowerCase()));
    }
    // Additional filtering methods for specific use cases
    filterByChain(results, chainId) {
        return results.filter(result => result.contract.chainId === chainId);
    }
    filterByPriority(results, maxPriority) {
        return results.filter(result => result.priorityScore <= maxPriority);
    }
    filterByKeywords(results, keywords) {
        return results.filter(result => {
            const searchText = `${result.contract.name} ${result.contract.sourceCode}`.toLowerCase();
            return keywords.some(keyword => searchText.includes(keyword.toLowerCase()));
        });
    }
    filterByFunctionName(results, functionName) {
        return results.filter(result => result.matchedFunctions.some(func => func.name === functionName));
    }
    // Get contracts that have specific function combinations
    filterByFunctionCombination(results, requiredFunctions) {
        return results.filter(result => {
            const matchedFunctionNames = result.matchedFunctions.map(func => func.name);
            return requiredFunctions.every(required => matchedFunctionNames.includes(required));
        });
    }
    // Find contracts that might be DAOs
    filterDAOContracts(results) {
        const daoKeywords = ['dao', 'governance', 'voting', 'ragequit', 'proposal'];
        const daoFunctions = ['rageQuit', 'exit', 'withdraw'];
        return results.filter(result => {
            const hasDAOKeywords = this.filterByKeywords([result], daoKeywords).length > 0;
            const hasDAOFunctions = result.matchedFunctions.some(func => daoFunctions.includes(func.name));
            return hasDAOKeywords || hasDAOFunctions;
        });
    }
    // Find contracts that might be vaults/farms
    filterVaultContracts(results) {
        const vaultKeywords = ['vault', 'farm', 'staking', 'yield', 'liquidity'];
        const vaultFunctions = ['withdraw', 'exit', 'emergencyWithdraw', 'collect'];
        return results.filter(result => {
            const hasVaultKeywords = this.filterByKeywords([result], vaultKeywords).length > 0;
            const hasVaultFunctions = result.matchedFunctions.some(func => vaultFunctions.includes(func.name));
            return hasVaultKeywords || hasVaultFunctions;
        });
    }
    // Find contracts that might be airdrops
    filterAirdropContracts(results) {
        const airdropKeywords = ['airdrop', 'claim', 'merkle', 'distribution'];
        const airdropFunctions = ['claim', 'claimTokens'];
        return results.filter(result => {
            const hasAirdropKeywords = this.filterByKeywords([result], airdropKeywords).length > 0;
            const hasAirdropFunctions = result.matchedFunctions.some(func => airdropFunctions.includes(func.name));
            return hasAirdropKeywords || hasAirdropFunctions;
        });
    }
    // Get statistics about filtered contracts
    getFilterStats(results) {
        const stats = {
            total: results.length,
            byPriority: {},
            byChain: {},
            byFunction: {},
            withHighPriorityKeywords: 0
        };
        for (const result of results) {
            // Priority stats
            const priority = result.priorityScore;
            stats.byPriority[priority] = (stats.byPriority[priority] || 0) + 1;
            // Chain stats
            const chainId = result.contract.chainId;
            stats.byChain[chainId] = (stats.byChain[chainId] || 0) + 1;
            // Function stats
            for (const func of result.matchedFunctions) {
                stats.byFunction[func.name] = (stats.byFunction[func.name] || 0) + 1;
            }
            // High priority keywords
            if (result.hasHighPriorityKeywords) {
                stats.withHighPriorityKeywords++;
            }
        }
        return stats;
    }
}
exports.ContractFilter = ContractFilter;
exports.contractFilter = new ContractFilter();
//# sourceMappingURL=filter.js.map