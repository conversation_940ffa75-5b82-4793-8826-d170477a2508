"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bytecodeDecompiler = exports.BytecodeDecompiler = void 0;
const ethers_1 = require("ethers");
const config_1 = require("./config");
const logger_1 = require("./logger");
class BytecodeDecompiler {
    constructor(chains) {
        this.providers = new Map();
        this.functionSignatureDatabase = new Map();
        this.initializeProviders(chains);
        this.initializeFunctionDatabase();
    }
    initializeProviders(chains) {
        for (const chain of chains) {
            if (chain.rpcUrl) {
                const provider = new ethers_1.ethers.JsonRpcProvider(chain.rpcUrl);
                this.providers.set(chain.chainId, provider);
                logger_1.logger.debug(`Initialized RPC provider for ${chain.name} (${chain.chainId}): ${chain.rpcUrl}`);
            }
            else {
                logger_1.logger.warn(`No RPC URL configured for ${chain.name} (${chain.chainId})`);
            }
        }
        logger_1.logger.info(`Initialized ${this.providers.size} RPC providers for bytecode decompilation`);
    }
    initializeFunctionDatabase() {
        // Initialize with known target function signatures
        for (const targetFunc of config_1.TARGET_FUNCTIONS) {
            const selector = targetFunc.fourByteSignature;
            if (!this.functionSignatureDatabase.has(selector)) {
                this.functionSignatureDatabase.set(selector, []);
            }
            this.functionSignatureDatabase.get(selector).push(targetFunc.signature);
        }
        // ARCHAEOLOGIST MODE: Expanded function signature database for better detection
        const commonSignatures = [
            // Standard ERC20
            { selector: '0xa9059cbb', signatures: ['transfer(address,uint256)'] },
            { selector: '0x23b872dd', signatures: ['transferFrom(address,address,uint256)'] },
            { selector: '0x095ea7b3', signatures: ['approve(address,uint256)'] },
            { selector: '0x70a08231', signatures: ['balanceOf(address)'] },
            { selector: '0x18160ddd', signatures: ['totalSupply()'] },
            { selector: '0x06fdde03', signatures: ['name()'] },
            { selector: '0x95d89b41', signatures: ['symbol()'] },
            { selector: '0x313ce567', signatures: ['decimals()'] },
            // Ownership & Control
            { selector: '0x8da5cb5b', signatures: ['owner()'] },
            { selector: '0xf2fde38b', signatures: ['transferOwnership(address)'] },
            { selector: '0x715018a6', signatures: ['renounceOwnership()'] },
            { selector: '0x5c975abb', signatures: ['paused()'] },
            { selector: '0x8456cb59', signatures: ['pause()'] },
            { selector: '0x3f4ba83a', signatures: ['unpause()'] },
            // ARCHAEOLOGIST TARGETS: High-value function patterns
            { selector: '0x38ed1739', signatures: ['swapExactTokensForTokens(uint256,uint256,address[],address,uint256)'] },
            { selector: '0x7ff36ab5', signatures: ['swapExactETHForTokens(uint256,address[],address,uint256)'] },
            { selector: '0x18cbafe5', signatures: ['swapExactTokensForETH(uint256,uint256,address[],address,uint256)'] },
            { selector: '0x02751cec', signatures: ['removeLiquidity(address,address,uint256,uint256,uint256,address,uint256)'] },
            { selector: '0xbaa2abde', signatures: ['removeLiquidityETH(address,uint256,uint256,uint256,address,uint256)'] },
            { selector: '0x441a3e70', signatures: ['emergencyWithdraw(uint256)'] },
            { selector: '0x17caf6f1', signatures: ['harvest()'] },
            { selector: '0x630b5ba1', signatures: ['getReward()'] },
            { selector: '0x3d18b912', signatures: ['claimReward()'] },
            { selector: '0xe9fad8ee', signatures: ['claimRewards()'] },
            { selector: '0x379607f5', signatures: ['claimTokens(address)'] },
            { selector: '0x84e9bd7e', signatures: ['emergencyExit()'] },
            { selector: '0x853828b6', signatures: ['unstake(uint256)'] },
            { selector: '0x2e17de78', signatures: ['unstakeAll()'] },
            { selector: '0x1959a002', signatures: ['claimAll()'] },
            { selector: '0x4641257d', signatures: ['harvest(uint256)'] },
        ];
        for (const { selector, signatures } of commonSignatures) {
            if (!this.functionSignatureDatabase.has(selector)) {
                this.functionSignatureDatabase.set(selector, []);
            }
            this.functionSignatureDatabase.get(selector).push(...signatures);
        }
        logger_1.logger.info(`🏺 ARCHAEOLOGIST: Loaded ${this.functionSignatureDatabase.size} function signatures for bytecode analysis`);
    }
    async decompileContract(address, chainId) {
        const provider = this.providers.get(chainId);
        if (!provider) {
            logger_1.logger.error(`No provider for chain ${chainId}`);
            return null;
        }
        try {
            // Get contract bytecode
            const bytecode = await provider.getCode(address);
            if (bytecode === '0x' || bytecode.length <= 2) {
                logger_1.logger.debug(`No bytecode found for ${address}`);
                return null;
            }
            logger_1.logger.info(`Decompiling contract ${address} on chain ${chainId}`);
            // Extract function selectors from bytecode
            const functions = this.extractFunctionSelectors(bytecode);
            // Match against target functions
            const matchedTargetFunctions = this.matchTargetFunctions(functions);
            const result = {
                address,
                chainId,
                functions,
                hasTargetFunctions: matchedTargetFunctions.length > 0,
                matchedTargetFunctions,
                bytecodeHash: ethers_1.ethers.keccak256(bytecode)
            };
            logger_1.logger.info(`Decompiled ${address}: found ${functions.length} functions, ${matchedTargetFunctions.length} target matches`);
            return result;
        }
        catch (error) {
            logger_1.logger.error(`Failed to decompile ${address}:`, error);
            return null;
        }
    }
    extractFunctionSelectors(bytecode) {
        const functions = [];
        const selectors = new Set();
        // Remove 0x prefix
        const code = bytecode.slice(2);
        // Look for PUSH4 instructions followed by function selectors
        // PUSH4 is 0x63, followed by 4 bytes
        for (let i = 0; i < code.length - 8; i += 2) {
            const opcode = code.slice(i, i + 2);
            if (opcode === '63') { // PUSH4
                const selector = '0x' + code.slice(i + 2, i + 10);
                if (this.isValidSelector(selector)) {
                    selectors.add(selector);
                }
            }
        }
        // Look for direct selector comparisons (common pattern)
        // EQ instruction (0x14) often follows selector loading
        for (let i = 0; i < code.length - 16; i += 2) {
            const instruction = code.slice(i, i + 16);
            // Pattern: PUSH4 selector DUP1 PUSH4 selector EQ
            if (instruction.startsWith('63') && instruction.includes('14')) {
                const selector = '0x' + instruction.slice(2, 10);
                if (this.isValidSelector(selector)) {
                    selectors.add(selector);
                }
            }
        }
        // Convert selectors to function objects
        for (const selector of selectors) {
            const signatures = this.functionSignatureDatabase.get(selector) || [];
            const confidence = signatures.length > 0 ? 0.9 : 0.3;
            functions.push({
                selector,
                signature: signatures[0], // Use first known signature
                name: signatures[0] ? this.extractFunctionName(signatures[0]) : undefined,
                inputs: signatures[0] ? this.extractInputTypes(signatures[0]) : undefined,
                confidence
            });
        }
        return functions;
    }
    isValidSelector(selector) {
        // Basic validation for function selectors
        if (selector.length !== 10)
            return false;
        if (!selector.startsWith('0x'))
            return false;
        // Check if it's a valid hex string
        const hex = selector.slice(2);
        return /^[0-9a-fA-F]{8}$/.test(hex);
    }
    extractFunctionName(signature) {
        const match = signature.match(/^([a-zA-Z_][a-zA-Z0-9_]*)\(/);
        return match ? match[1] : 'unknown';
    }
    extractInputTypes(signature) {
        const match = signature.match(/\(([^)]*)\)/);
        if (!match || !match[1])
            return [];
        return match[1].split(',').map(type => type.trim()).filter(type => type.length > 0);
    }
    matchTargetFunctions(functions) {
        const matches = [];
        for (const func of functions) {
            for (const targetFunc of config_1.TARGET_FUNCTIONS) {
                if (func.selector === targetFunc.fourByteSignature) {
                    matches.push(targetFunc);
                    break;
                }
            }
        }
        return matches;
    }
    async batchDecompile(addresses, chainId) {
        const results = [];
        for (const address of addresses) {
            try {
                const result = await this.decompileContract(address, chainId);
                if (result) {
                    results.push(result);
                }
                // Small delay to avoid overwhelming the RPC
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            catch (error) {
                logger_1.logger.error(`Failed to decompile ${address}:`, error);
            }
        }
        return results;
    }
    // Enhanced function signature detection using common patterns
    detectFunctionPatterns(bytecode) {
        const functions = [];
        const code = bytecode.slice(2);
        // Look for common function dispatch patterns
        const patterns = [
            // Standard function dispatcher pattern
            /63([0-9a-fA-F]{8})80600e6000396000f3/g,
            // Proxy pattern
            /63([0-9a-fA-F]{8})14[0-9a-fA-F]{2,}/g,
            // Direct comparison pattern
            /63([0-9a-fA-F]{8})811461/g
        ];
        for (const pattern of patterns) {
            let match;
            while ((match = pattern.exec(code)) !== null) {
                const selector = '0x' + match[1];
                if (this.isValidSelector(selector)) {
                    const signatures = this.functionSignatureDatabase.get(selector) || [];
                    functions.push({
                        selector,
                        signature: signatures[0],
                        name: signatures[0] ? this.extractFunctionName(signatures[0]) : undefined,
                        confidence: signatures.length > 0 ? 0.8 : 0.4
                    });
                }
            }
        }
        return functions;
    }
}
exports.BytecodeDecompiler = BytecodeDecompiler;
exports.bytecodeDecompiler = new BytecodeDecompiler(config_1.CHAIN_CONFIGS);
//# sourceMappingURL=decompiler.js.map