/**
 * 🏺 SURGICAL PRECISION SIMULATOR
 *
 * This module provides surgical precision simulation using only modern components.
 * All legacy fuzzing and parameter generation has been removed.
 *
 * ONLY PRECISION. NO LEGACY.
 */
import { ethers } from 'ethers';
import { SimulationResult } from './types';
import { FilterResult } from './filter';
declare class ContractSimulator {
    private providers;
    constructor();
    private initializeProviders;
    /**
     * 🏺 SURGICAL PRECISION: Main simulation method using only precision simulator
     */
    simulateContract(filterResult: FilterResult): Promise<SimulationResult[]>;
    /**
     * 🏺 BATCH SIMULATION: Process multiple filter results
     */
    batchSimulate(filterResults: FilterResult[]): Promise<SimulationResult[]>;
    /**
     * 🏺 GET PROVIDER: Get provider for specific chain
     */
    getProvider(chainId: number): ethers.Provider | undefined;
}
export declare const contractSimulator: ContractSimulator;
export {};
//# sourceMappingURL=simulator.d.ts.map