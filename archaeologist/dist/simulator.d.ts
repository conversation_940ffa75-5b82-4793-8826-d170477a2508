import { ethers } from 'ethers';
import { ContractInfo, SimulationResult } from './types';
import { FilterResult } from './filter';
export declare class ContractSimulator {
    private providers;
    constructor();
    private initializeProviders;
    simulateContract(filterResult: FilterResult): Promise<SimulationResult[]>;
    private simulateFunction;
    private callFunction;
    private generateFunctionParameters;
    private generateArchaeologistParameters;
    private generateBasicParameters;
    private generateFunctionSignature;
    private getSimulationAddress;
    private getArchaeologistAddress;
    private getArchaeologistUint;
    private getArchaeologistBytes;
    private getArchaeologistArray;
    private formatReturnData;
    private extractPotentialValue;
    batchSimulate(filterResults: FilterResult[]): Promise<SimulationResult[]>;
    checkContractBalance(contractAddress: string, chainId: number): Promise<{
        ethBalance: string;
        hasTokens: boolean;
        tokenBalances: {
            address: string;
            symbol: string;
            balance: string;
        }[];
    }>;
    private checkERC20Balances;
    simulateWithCustomParams(contract: ContractInfo, functionName: string, params: any[]): Promise<SimulationResult>;
    getProvider(chainId: number): ethers.JsonRpcProvider | undefined;
}
export declare const contractSimulator: ContractSimulator;
//# sourceMappingURL=simulator.d.ts.map