export declare class BlockMonitor {
    private providers;
    private listeners;
    private isMonitoring;
    constructor();
    private initializeProviders;
    startMonitoring(): Promise<void>;
    private startChainMonitoring;
    private processNewBlock;
    stopMonitoring(): Promise<void>;
    catchUpMissedBlocks(): Promise<void>;
    private catchUpChainBlocks;
    isRunning(): boolean;
    getMonitoringStatus(): {
        [chainId: number]: boolean;
    };
}
export declare const blockMonitor: BlockMonitor;
//# sourceMappingURL=monitor.d.ts.map