{"version": 3, "file": "fetcher.d.ts", "sourceRoot": "", "sources": ["../src/fetcher.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAIpD,qBAAa,eAAe;IAC1B,OAAO,CAAC,YAAY,CAAgB;IACpC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAK;YAE5B,SAAS;YAqBT,qBAAqB;IAsB7B,4BAA4B,CAChC,KAAK,EAAE,WAAW,EAClB,IAAI,GAAE,MAAU,EAChB,MAAM,GAAE,MAAa,GACpB,OAAO,CAAC,YAAY,EAAE,CAAC;YA6CZ,0BAA0B;IAiClC,oBAAoB,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;YAwD/E,uBAAuB;IAsD/B,0BAA0B,CAC9B,KAAK,EAAE,WAAW,EAClB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,YAAY,EAAE,CAAC;IAsDpB,iBAAiB,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,GAAE,MAAY,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;YA2C3E,sBAAsB;YA2GtB,6BAA6B;YA4D7B,8BAA8B;IAkCtC,wBAAwB,CAC5B,KAAK,EAAE,WAAW,EAClB,KAAK,GAAE,MAAU,GAChB,OAAO,CAAC,YAAY,EAAE,CAAC;YAuDZ,oCAAoC;YAqEpC,0BAA0B;YAmD1B,yBAAyB;YAgCzB,mCAAmC;YAqCnC,kBAAkB;YA+BlB,0BAA0B;CAsCzC;AAED,eAAO,MAAM,eAAe,iBAAwB,CAAC"}