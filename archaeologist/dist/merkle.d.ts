export interface MerkleProof {
    leaf: string;
    proof: string[];
    root: string;
    index: number;
}
export interface AirdropClaim {
    address: string;
    amount: string;
    proof: string[];
}
export declare class MerkleProofGenerator {
    generateTestProofs(userAddress: string): MerkleProof[];
    private generateSyntheticProof;
    private calculateRoot;
    verifyProof(proof: string[], leaf: string, root: string): boolean;
    generateAirdropClaimParams(userAddress: string): any[][];
    generateClaimParameters(functionSignature: string, userAddress: string): any[][];
    generateAdvancedTestData(userAddress: string): {
        merkleProofs: MerkleProof[];
        commonAmounts: string[];
        testAddresses: string[];
        timeBasedParams: number[];
    };
    detectMerkleProofFunction(functionSignature: string): boolean;
    generateParametersFromABI(abiFunction: any, userAddress: string): any[][];
    private generateMerkleProofParameters;
    private generateParameterCombinations;
    private getTestValueForType;
    private getDefaultValueForType;
}
export declare const merkleProofGenerator: MerkleProofGenerator;
//# sourceMappingURL=merkle.d.ts.map