/**
 * 🏺 SURGICAL PRECISION: Self-Monitoring System
 *
 * This module provides internal validation and self-correction mechanisms.
 * Detects and fixes issues automatically to maintain surgical precision.
 *
 * Features:
 * 1. Continuous system health monitoring
 * 2. Automatic error detection and correction
 * 3. Performance metrics tracking
 * 4. Configuration validation
 * 5. Self-healing mechanisms
 *
 * NO GUESSES. SURGICAL PRECISION.
 */
export interface SystemHealth {
    overall: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'FAILED';
    components: ComponentHealth[];
    metrics: SystemMetrics;
    issues: SystemIssue[];
    recommendations: string[];
    lastCheck: number;
}
export interface ComponentHealth {
    name: string;
    status: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'FAILED';
    metrics: {
        [key: string]: any;
    };
    issues: string[];
    lastCheck: number;
}
export interface SystemMetrics {
    uptime: number;
    totalScans: number;
    successRate: number;
    avgResponseTime: number;
    errorRate: number;
    memoryUsage: number;
    apiCallsPerMinute: number;
}
export interface SystemIssue {
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    component: string;
    description: string;
    autoFixable: boolean;
    fixAttempted: boolean;
    timestamp: number;
}
declare class SelfMonitor {
    private healthHistory;
    private metrics;
    private startTime;
    private scanCount;
    private errorCount;
    private responseTimes;
    private apiCalls;
    constructor();
    /**
     * Initialize system metrics
     */
    private initializeMetrics;
    /**
     * SURGICAL PRECISION: Comprehensive system health check
     */
    performHealthCheck(): Promise<SystemHealth>;
    /**
     * Check configuration health
     */
    private checkConfigurationHealth;
    /**
     * Check network connectivity health
     */
    private checkNetworkHealth;
    /**
     * Check signature verification system
     */
    private checkSignatureVerification;
    /**
     * Check economic engine
     */
    private checkEconomicEngine;
    /**
     * Check memory health
     */
    private checkMemoryHealth;
    /**
     * Check API health
     */
    private checkAPIHealth;
    /**
     * Update system metrics
     */
    private updateMetrics;
    /**
     * Determine overall system health
     */
    private determineOverallHealth;
    /**
     * Generate recommendations
     */
    private generateRecommendations;
    /**
     * Determine issue severity
     */
    private determineSeverity;
    /**
     * Check if issue is auto-fixable
     */
    private isAutoFixable;
    /**
     * Attempt automatic fixes
     */
    private attemptAutoFixes;
    /**
     * Log health status
     */
    private logHealthStatus;
    /**
     * Start continuous monitoring
     */
    private startContinuousMonitoring;
    /**
     * Record scan metrics
     */
    recordScan(success: boolean, responseTime: number): void;
    /**
     * Record API call
     */
    recordApiCall(): void;
    /**
     * Get current system health
     */
    getCurrentHealth(): SystemHealth | null;
    /**
     * Get health history
     */
    getHealthHistory(): SystemHealth[];
}
export declare const selfMonitor: SelfMonitor;
export {};
//# sourceMappingURL=self-monitor.d.ts.map