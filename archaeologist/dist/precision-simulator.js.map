{"version": 3, "file": "precision-simulator.js", "sourceRoot": "", "sources": ["../src/precision-simulator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,mCAAgC;AAChC,qCAAkC;AAoClC,MAAM,kBAAkB;IAGtB;QAFQ,eAAU,GAAG,IAAI,GAAG,EAA8B,CAAC;QAGzD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,uDAAuD;QACvD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE;YAC7B,YAAY,EAAE,OAAO;YACrB,SAAS,EAAE,SAAS;YACpB,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,qBAAqB;oBAClC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,6BAA6B;SAC3C,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE;YAChC,YAAY,EAAE,UAAU;YACxB,SAAS,EAAE,YAAY;YACvB,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,iBAAiB;oBACvB,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,8BAA8B;oBAC3C,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,yBAAyB;SACvC,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,EAAE;YACvC,YAAY,EAAE,UAAU;YACxB,SAAS,EAAE,mBAAmB;YAC9B,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,aAAa;oBACnB,UAAU,EAAE,CAAC,EAAE,CAAC;oBAChB,WAAW,EAAE,iDAAiD;oBAC9D,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,IAAI,EAAE,cAAc;oBACpB,UAAU,EAAE,CAAC,EAAE,CAAC;oBAChB,WAAW,EAAE,0BAA0B;oBACvC,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,IAAI,EAAE,gBAAgB;oBACtB,UAAU,EAAE,CAAC,eAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oBACtC,WAAW,EAAE,mBAAmB;oBAChC,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,IAAI,EAAE,cAAc;oBACpB,UAAU,EAAE,CAAC,eAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;oBAC1C,WAAW,EAAE,iDAAiD;oBAC9D,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,IAAI,EAAE,aAAa;oBACnB,UAAU,EAAE,CAAC,eAAM,CAAC,UAAU,CAAC;oBAC/B,WAAW,EAAE,6CAA6C;oBAC1D,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,yBAAyB;SACvC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE;YAC5B,YAAY,EAAE,MAAM;YACpB,SAAS,EAAE,QAAQ;YACnB,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,eAAe;oBACrB,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,oBAAoB;oBACjC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,kBAAkB;SAChC,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,EAAE;YACvC,YAAY,EAAE,UAAU;YACxB,SAAS,EAAE,mBAAmB;YAC9B,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,aAAa;oBACnB,UAAU,EAAE,CAAC,EAAE,CAAC;oBAChB,WAAW,EAAE,iDAAiD;oBAC9D,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,IAAI,EAAE,cAAc;oBACpB,UAAU,EAAE,CAAC,EAAE,CAAC;oBAChB,WAAW,EAAE,qBAAqB;oBAClC,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,IAAI,EAAE,sBAAsB;oBAC5B,UAAU,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;oBACjC,WAAW,EAAE,gCAAgC;oBAC7C,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,UAAU,EAAE,CAAC,eAAM,CAAC,UAAU,CAAC;oBAC/B,WAAW,EAAE,sBAAsB;oBACnC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,2BAA2B;SACzC,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE;YAC/B,YAAY,EAAE,SAAS;YACvB,SAAS,EAAE,WAAW;YACtB,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,qBAAqB;oBAC3B,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,yBAAyB;oBACtC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,oBAAoB;SAClC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE;YAChC,YAAY,EAAE,UAAU;YACxB,SAAS,EAAE,YAAY;YACvB,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,uBAAuB;oBAC7B,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,4BAA4B;oBACzC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,uBAAuB;SACrC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE;YAC9B,YAAY,EAAE,QAAQ;YACtB,SAAS,EAAE,UAAU;YACrB,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,qBAAqB;oBAC3B,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,0BAA0B;oBACvC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,kBAAkB;SAChC,CAAC,CAAC;QAEH,gCAAgC;QAChC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE;YACnC,YAAY,EAAE,aAAa;YAC3B,SAAS,EAAE,eAAe;YAC1B,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,sBAAsB;oBAC5B,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,yBAAyB;oBACtC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,0BAA0B;SACxC,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,qBAAqB,EAAE;YACzC,YAAY,EAAE,mBAAmB;YACjC,SAAS,EAAE,qBAAqB;YAChC,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,sBAAsB;oBAC5B,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,2BAA2B;oBACxC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,gCAAgC;SAC9C,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE;YAC7B,YAAY,EAAE,OAAO;YACrB,SAAS,EAAE,SAAS;YACpB,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,gBAAgB;oBACtB,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,yBAAyB;oBACtC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,kBAAkB;SAChC,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,EAAE;YACpC,YAAY,EAAE,cAAc;YAC5B,SAAS,EAAE,gBAAgB;YAC3B,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,eAAe;oBACrB,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,2BAA2B;oBACxC,QAAQ,EAAE,CAAC;iBACZ;aACF;YACD,WAAW,EAAE,uCAAuC;SACrD,CAAC,CAAC;QAEH,eAAM,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAC,IAAI,kCAAkC,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,QAAyB,EACzB,eAAuB,EACvB,iBAAyB,EACzB,GAAU,EACV,WAAmB;QAGnB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,mCAAmC,iBAAiB,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,4BAA4B,iBAAiB,OAAO,eAAe,EAAE,CAAC,CAAC;QAEpF,MAAM,QAAQ,GAAG,IAAI,eAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrE,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,kCAAkC;QAClC,MAAM,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE3F,KAAK,MAAM,YAAY,IAAI,mBAAmB,EAAE,CAAC;YAC/C,IAAI,CAAC;gBACH,eAAM,CAAC,KAAK,CAAC,cAAc,YAAY,CAAC,IAAI,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC;gBAE9E,oBAAoB;gBACpB,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,UAAU,CAC7D,GAAG,YAAY,CAAC,UAAU,EAC1B;oBACE,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,MAAM,CAAC,sCAAsC;iBACxD,CACF,CAAC;gBAEF,qBAAqB;gBACrB,MAAM,OAAO,GAAsB;oBACjC,YAAY;oBACZ,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;oBAClD,UAAU,EAAE,IAAI,CAAC,0BAA0B,CAAC,YAAY,EAAE,MAAM,CAAC;oBACjE,cAAc,EAAE,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;iBACpD,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAEtB,eAAM,CAAC,OAAO,CAAC,kBAAkB,YAAY,CAAC,IAAI,aAAa,OAAO,CAAC,UAAU,IAAI,MAAM,EAAE,CAAC,CAAC;YAEjG,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,MAAM,OAAO,GAAsB;oBACjC,YAAY;oBACZ,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,UAAU,EAAE,CAAC;iBACd,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAEtB,uCAAuC;gBACvC,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3C,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,4CAA4C;oBACrE,eAAM,CAAC,IAAI,CAAC,6BAA6B,YAAY,CAAC,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACnF,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,KAAK,CAAC,iBAAiB,YAAY,CAAC,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxE,CAAC;YACH,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,MAAM,UAAU,GAAG,OAAO;aACvB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;aACtB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAElD,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QACnE,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAEnF,MAAM,gBAAgB,GAA8B;YAClD,YAAY,EAAE,QAAQ,CAAC,YAAY;YACnC,SAAS,EAAE,iBAAiB;YAC5B,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YACxC,OAAO;YACP,UAAU;YACV,UAAU,EAAE,iBAAiB;YAC7B,mBAAmB;SACpB,CAAC;QAEF,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;YAChC,eAAM,CAAC,OAAO,CAAC,uCAAuC,iBAAiB,KAAK,iBAAiB,eAAe,CAAC,CAAC;QAChH,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,mCAAmC,iBAAiB,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,YAA0B,EAAE,MAAW;QACxE,IAAI,UAAU,GAAG,EAAE,CAAC,CAAC,kBAAkB;QAEvC,uDAAuD;QACvD,UAAU,IAAI,CAAC,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QAE/C,kBAAkB;QAClB,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAC5C,UAAU,IAAI,EAAE,CAAC;YAEjB,qCAAqC;YACrC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,GAAG,EAAE,EAAE,CAAC;gBAC9C,UAAU,IAAI,EAAE,CAAC,CAAC,0CAA0C;YAC9D,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/C,UAAU,IAAI,EAAE,CAAC,CAAC,0CAA0C;YAC9D,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,OAA4B;QAC7D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEnC,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE7C,MAAM,aAAa,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;QAC7G,MAAM,WAAW,GAAG,CAAC,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAEtE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,OAA4B,EAAE,UAA8B;QAC/F,IAAI,CAAC,UAAU;YAAE,OAAO,CAAC,CAAC;QAE1B,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,4CAA4C;QAC5C,SAAS,IAAI,EAAE,CAAC;QAEhB,6BAA6B;QAC7B,IAAI,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YAChE,SAAS,IAAI,EAAE,CAAC;QAClB,CAAC;QAED,wBAAwB;QACxB,IAAI,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,CAAC;YAChC,SAAS,IAAI,EAAE,CAAC;QAClB,CAAC;QAED,qCAAqC;QACrC,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAC9D,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC;QAE/C,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,MAAW;QACxC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/C,0CAA0C;YAC1C,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;gBAC1B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;oBAC1C,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,YAAoB;QAC7C,MAAM,mBAAmB,GAAG;YAC1B,sBAAsB;YACtB,gBAAgB;YAChB,iBAAiB;YACjB,cAAc;YACd,QAAQ;YACR,aAAa;YACb,OAAO;SACR,CAAC;QAEF,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CACxC,YAAY,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAC7C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,iBAAyB;QACnC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;CACF;AAEY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC"}