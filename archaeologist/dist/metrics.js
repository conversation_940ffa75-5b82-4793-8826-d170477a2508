"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceMonitor = exports.PerformanceMonitor = void 0;
const logger_1 = require("./logger");
const database_1 = require("./database");
class PerformanceMonitor {
    constructor() {
        this.timings = new Map();
        this.counters = new Map();
        this.startTime = Date.now();
        this.rpcCallCount = 0;
        this.rpcFailureCount = 0;
        this.databaseOperations = 0;
    }
    // Start timing an operation
    startTiming(operationId, operation, metadata) {
        this.timings.set(operationId, {
            operation,
            startTime: Date.now(),
            metadata
        });
    }
    // End timing an operation
    endTiming(operationId, success = true) {
        const timing = this.timings.get(operationId);
        if (!timing) {
            logger_1.logger.warn(`No timing found for operation: ${operationId}`);
            return 0;
        }
        timing.endTime = Date.now();
        timing.duration = timing.endTime - timing.startTime;
        timing.success = success;
        // Log slow operations
        if (timing.duration > 5000) { // 5 seconds
            logger_1.logger.warn(`Slow operation detected: ${timing.operation} took ${timing.duration}ms`);
        }
        this.timings.delete(operationId);
        return timing.duration;
    }
    // Increment a counter
    incrementCounter(name, value = 1) {
        const current = this.counters.get(name) || 0;
        this.counters.set(name, current + value);
    }
    // Record RPC call
    recordRpcCall(success = true) {
        this.rpcCallCount++;
        if (!success) {
            this.rpcFailureCount++;
        }
    }
    // Record database operation
    recordDatabaseOperation() {
        this.databaseOperations++;
    }
    // Get current performance metrics
    async getMetrics() {
        const scanningMetrics = await this.getScanningMetrics();
        const simulationMetrics = await this.getSimulationMetrics();
        const executionMetrics = await this.getExecutionMetrics();
        const systemMetrics = this.getSystemMetrics();
        return {
            scanningMetrics,
            simulationMetrics,
            executionMetrics,
            systemMetrics
        };
    }
    async getScanningMetrics() {
        const totalScans = this.counters.get('total_scans') || 0;
        const successfulScans = this.counters.get('successful_scans') || 0;
        const failedScans = this.counters.get('failed_scans') || 0;
        // Calculate average scan time from completed timings
        const scanTimes = Array.from(this.timings.values())
            .filter(t => t.operation === 'scan' && t.duration)
            .map(t => t.duration);
        const averageScanTime = scanTimes.length > 0
            ? scanTimes.reduce((a, b) => a + b, 0) / scanTimes.length
            : 0;
        const uptime = Date.now() - this.startTime;
        const contractsPerSecond = totalScans > 0 ? (totalScans / (uptime / 1000)) : 0;
        return {
            totalScans,
            successfulScans,
            failedScans,
            averageScanTime,
            contractsPerSecond,
            chainsScanned: this.counters.get('chains_scanned') || 0,
            lastScanTime: new Date()
        };
    }
    async getSimulationMetrics() {
        const totalSimulations = this.counters.get('total_simulations') || 0;
        const successfulSimulations = this.counters.get('successful_simulations') || 0;
        const failedSimulations = this.counters.get('failed_simulations') || 0;
        const timeoutCount = this.counters.get('simulation_timeouts') || 0;
        const gasEstimationFailures = this.counters.get('gas_estimation_failures') || 0;
        const simulationTimes = Array.from(this.timings.values())
            .filter(t => t.operation === 'simulation' && t.duration)
            .map(t => t.duration);
        const averageSimulationTime = simulationTimes.length > 0
            ? simulationTimes.reduce((a, b) => a + b, 0) / simulationTimes.length
            : 0;
        const uptime = Date.now() - this.startTime;
        const simulationsPerSecond = totalSimulations > 0 ? (totalSimulations / (uptime / 1000)) : 0;
        return {
            totalSimulations,
            successfulSimulations,
            failedSimulations,
            averageSimulationTime,
            simulationsPerSecond,
            timeoutCount,
            gasEstimationFailures
        };
    }
    async getExecutionMetrics() {
        const totalExecutions = this.counters.get('total_executions') || 0;
        const successfulExecutions = this.counters.get('successful_executions') || 0;
        const failedExecutions = this.counters.get('failed_executions') || 0;
        const transactionFailures = this.counters.get('transaction_failures') || 0;
        const executionTimes = Array.from(this.timings.values())
            .filter(t => t.operation === 'execution' && t.duration)
            .map(t => t.duration);
        const averageExecutionTime = executionTimes.length > 0
            ? executionTimes.reduce((a, b) => a + b, 0) / executionTimes.length
            : 0;
        // Get total value extracted from database
        const stats = await database_1.db.getStats();
        const totalValueExtracted = stats.totalValueExtracted || '0';
        const averageGasUsed = this.counters.get('total_gas_used') || 0 / Math.max(totalExecutions, 1);
        return {
            totalExecutions,
            successfulExecutions,
            failedExecutions,
            averageExecutionTime,
            totalValueExtracted,
            averageGasUsed,
            transactionFailures
        };
    }
    getSystemMetrics() {
        const memoryUsage = process.memoryUsage();
        const uptime = Date.now() - this.startTime;
        // Simple CPU usage approximation (not perfect but useful)
        const cpuUsage = process.cpuUsage();
        const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000 / (uptime / 1000) * 100;
        return {
            memoryUsage,
            cpuUsage: Math.min(cpuPercent, 100), // Cap at 100%
            uptime,
            rpcCallCount: this.rpcCallCount,
            rpcFailureCount: this.rpcFailureCount,
            databaseOperations: this.databaseOperations
        };
    }
    // Display metrics in a formatted way
    async displayMetrics() {
        const metrics = await this.getMetrics();
        logger_1.logger.info('=== PERFORMANCE METRICS ===');
        // Scanning metrics
        logger_1.logger.info('📊 Scanning Performance:');
        logger_1.logger.info(`  Total scans: ${metrics.scanningMetrics.totalScans}`);
        logger_1.logger.info(`  Success rate: ${this.calculateSuccessRate(metrics.scanningMetrics.successfulScans, metrics.scanningMetrics.totalScans)}%`);
        logger_1.logger.info(`  Average scan time: ${metrics.scanningMetrics.averageScanTime.toFixed(2)}ms`);
        logger_1.logger.info(`  Contracts/second: ${metrics.scanningMetrics.contractsPerSecond.toFixed(2)}`);
        // Simulation metrics
        logger_1.logger.info('🔬 Simulation Performance:');
        logger_1.logger.info(`  Total simulations: ${metrics.simulationMetrics.totalSimulations}`);
        logger_1.logger.info(`  Success rate: ${this.calculateSuccessRate(metrics.simulationMetrics.successfulSimulations, metrics.simulationMetrics.totalSimulations)}%`);
        logger_1.logger.info(`  Average simulation time: ${metrics.simulationMetrics.averageSimulationTime.toFixed(2)}ms`);
        logger_1.logger.info(`  Simulations/second: ${metrics.simulationMetrics.simulationsPerSecond.toFixed(2)}`);
        logger_1.logger.info(`  Timeouts: ${metrics.simulationMetrics.timeoutCount}`);
        // Execution metrics
        logger_1.logger.info('⚡ Execution Performance:');
        logger_1.logger.info(`  Total executions: ${metrics.executionMetrics.totalExecutions}`);
        logger_1.logger.info(`  Success rate: ${this.calculateSuccessRate(metrics.executionMetrics.successfulExecutions, metrics.executionMetrics.totalExecutions)}%`);
        logger_1.logger.info(`  Total value extracted: ${metrics.executionMetrics.totalValueExtracted} ETH`);
        logger_1.logger.info(`  Average gas used: ${metrics.executionMetrics.averageGasUsed.toFixed(0)}`);
        // System metrics
        logger_1.logger.info('💻 System Performance:');
        logger_1.logger.info(`  Memory usage: ${(metrics.systemMetrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
        logger_1.logger.info(`  CPU usage: ${metrics.systemMetrics.cpuUsage.toFixed(2)}%`);
        logger_1.logger.info(`  Uptime: ${(metrics.systemMetrics.uptime / 1000 / 60).toFixed(2)} minutes`);
        logger_1.logger.info(`  RPC calls: ${metrics.systemMetrics.rpcCallCount} (${metrics.systemMetrics.rpcFailureCount} failures)`);
        logger_1.logger.info(`  Database operations: ${metrics.systemMetrics.databaseOperations}`);
    }
    calculateSuccessRate(successful, total) {
        return total > 0 ? Math.round((successful / total) * 100) : 0;
    }
    // Reset all metrics
    reset() {
        this.timings.clear();
        this.counters.clear();
        this.startTime = Date.now();
        this.rpcCallCount = 0;
        this.rpcFailureCount = 0;
        this.databaseOperations = 0;
    }
    // Get specific counter value
    getCounter(name) {
        return this.counters.get(name) || 0;
    }
    // Get timing statistics for an operation type
    getTimingStats(operation) {
        const times = Array.from(this.timings.values())
            .filter(t => t.operation === operation && t.duration)
            .map(t => t.duration);
        if (times.length === 0) {
            return { count: 0, average: 0, min: 0, max: 0 };
        }
        return {
            count: times.length,
            average: times.reduce((a, b) => a + b, 0) / times.length,
            min: Math.min(...times),
            max: Math.max(...times)
        };
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
exports.performanceMonitor = new PerformanceMonitor();
//# sourceMappingURL=metrics.js.map