{"version": 3, "file": "bytecode-analyzer.js", "sourceRoot": "", "sources": ["../src/bytecode-analyzer.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;;;;AAEH,kDAA0B;AAC1B,mCAAgC;AAChC,iDAAqC;AACrC,+BAAiC;AACjC,qCAAkC;AAClC,qCAAkC;AAElC,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,oBAAI,CAAC,CAAC;AA4BlC,MAAM,gBAAgB;IAAtB;QACU,UAAK,GAAG,IAAI,GAAG,EAA4B,CAAC;QACnC,eAAU,GAAG,kCAAkC,CAAC;QAChD,gBAAW,GAAG,8BAA8B,CAAC;IA6ThE,CAAC;IA3TC;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,OAAe,EACf,OAAe,EACf,QAAiB;QAEjB,MAAM,QAAQ,GAAG,GAAG,OAAO,IAAI,OAAO,EAAE,CAAC;QAEzC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QACnC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,6BAA6B,OAAO,aAAa,OAAO,EAAE,CAAC,CAAC;QAEzE,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,QAAQ,GAAqB;YACjC,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS,EAAE,EAAE;YACb,oBAAoB,EAAE,EAAE;YACxB,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE;gBACR,kBAAkB,EAAE,KAAK;gBACzB,eAAe,EAAE,EAAE;gBACnB,SAAS,EAAE,KAAK;gBAChB,mBAAmB,EAAE,CAAC;aACvB;SACF,CAAC;QAEF,wBAAwB;QACxB,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;YAC/C,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;YAClC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAChC,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC;SAC3C,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,SAAS,GAAG,IAAI,GAAG,EAA4B,CAAC;QAEtD,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxC,MAAM,OAAO,GAAG,CAAC,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEtD,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBAClD,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEnD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAsB,EAAE,EAAE;oBAC9C,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBACvD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;wBACvD,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;oBAC9C,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QACpD,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAEzD,6BAA6B;QAC7B,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAEtC,iCAAiC;QACjC,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;QAE5C,eAAM,CAAC,KAAK,CAAC,yBAAyB,QAAQ,CAAC,SAAS,CAAC,MAAM,wBAAwB,QAAQ,CAAC,UAAU,eAAe,CAAC,CAAC;QAE3H,IAAI,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YACzC,eAAM,CAAC,IAAI,CAAC,iCAAiC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACnC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,OAAe;QAC1D,MAAM,KAAK,GAAG,eAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;QAC7D,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,mCAAmC,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,eAAM,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC1D,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEjD,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,gCAAgC,OAAO,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAChD,IAAI,CAAC;YACH,iCAAiC;YACjC,MAAM,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAElC,qCAAqC;YACrC,MAAM,QAAQ,GAAG,iBAAiB,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEhD,6BAA6B;YAC7B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,sBAAsB,QAAQ,8BAA8B,EAAE;gBAC/F,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,gDAAgD;YAChD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAEnD,UAAU;YACV,OAAO,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEnC,eAAM,CAAC,KAAK,CAAC,2BAA2B,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;YACtE,OAAO,SAAS,CAAC;QAEnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;YACtD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACjD,QAAQ,EAAE,QAAQ;gBAClB,iBAAiB,EAAE,IAAI;aACxB,EAAE;gBACD,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClE,eAAM,CAAC,KAAK,CAAC,uBAAuB,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;gBAClE,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;YACjD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B,CAAC,QAAgB;QACxD,MAAM,SAAS,GAAuB,EAAE,CAAC;QAEzC,oDAAoD;QACpD,eAAM,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC1C,MAAM,SAAS,GAAG,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;YAErE,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBAC7D,SAAS,CAAC,IAAI,CAAC;oBACb,SAAS,EAAE,UAAU,CAAC,SAAS;oBAC/B,iBAAiB,EAAE,UAAU,CAAC,iBAAiB;oBAC/C,UAAU,EAAE,EAAE,EAAE,6CAA6C;oBAC7D,MAAM,EAAE,2BAA2B;iBACpC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,eAAM,CAAC,OAAO,CAAC,sCAAsC,SAAS,CAAC,MAAM,mBAAmB,CAAC,CAAC;QAC5F,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,MAAc;QACxC,MAAM,SAAS,GAAuB,EAAE,CAAC;QAEzC,iDAAiD;QACjD,MAAM,aAAa,GAAG,+BAA+B,CAAC;QACtD,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACrD,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAEpD,IAAI,CAAC;gBACH,MAAM,iBAAiB,GAAG,eAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAE5D,SAAS,CAAC,IAAI,CAAC;oBACb,SAAS;oBACT,iBAAiB;oBACjB,UAAU,EAAE,EAAE;oBACd,MAAM,EAAE,aAAa;iBACtB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,0BAA0B;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,eAAsB;QAC9C,MAAM,SAAS,GAAuB,EAAE,CAAC;QAEzC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACpC,SAAS,CAAC,IAAI,CAAC;oBACb,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,iBAAiB,EAAE,IAAI,CAAC,QAAQ;oBAChC,UAAU,EAAE,EAAE,EAAE,4BAA4B;oBAC5C,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,SAAS,EAAE,IAAI,CAAC,OAAO;oBACvB,MAAM,EAAE,IAAI,CAAC,IAAI;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAA0B;QACpD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE9C,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC;QACrH,MAAM,WAAW,GAAG,QAAQ,CAAC,oBAAoB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,6BAA6B;QAE5F,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAA0B;QACvD,MAAM,gBAAgB,GAAG,eAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC,CAAC;QAE5F,QAAQ,CAAC,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,SAAS;aACnD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC,CAAC;aAC/E,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE/B,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,GAAG,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpF,2CAA2C;QAC3C,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAClD,QAAQ,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC;QACvC,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACzD,QAAQ,CAAC,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,QAA0B;QAC7D,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,uCAAuC;QACvC,SAAS,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,GAAG,EAAE,CAAC;QAE3D,oCAAoC;QACpC,MAAM,qBAAqB,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;QACzF,MAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACpE,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACpE,CAAC;QAEF,IAAI,eAAe;YAAE,SAAS,IAAI,EAAE,CAAC;QAErC,2CAA2C;QAC3C,SAAS,IAAI,QAAQ,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtD,mBAAmB;QACnB,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;QAEnD,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAAe,EAAE,OAAe;QAChD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,IAAI,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,eAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAClD,CAAC;CACF;AAEY,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC"}