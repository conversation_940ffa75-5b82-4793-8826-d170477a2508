import { ContractInfo, SimulationR<PERSON>ult, ExploitableContract, ExecutionResult, ScanStatus } from './types';
export declare class Database {
    private db;
    private ready;
    constructor();
    private initializeTables;
    private run;
    private get;
    private all;
    saveContract(contract: ContractInfo): Promise<void>;
    saveSimulation(simulation: SimulationResult): Promise<void>;
    saveExploitableContract(exploit: ExploitableContract): Promise<void>;
    saveExecution(execution: ExecutionResult): Promise<void>;
    saveFuzzingResult(fuzzingResult: {
        contractAddress: string;
        strategy: string;
        parameters: string;
        success: boolean;
        returnData: string;
        gasEstimate: number;
        riskScore: number;
        potentialExploit: boolean;
        timestamp: number;
    }): Promise<void>;
    getFuzzingResults(contractAddress?: string, limit?: number): Promise<any[]>;
    markExploitExecuted(address: string, functionName: string): Promise<void>;
    updateScanStatus(status: ScanStatus): Promise<void>;
    getScanStatus(chainId: number): Promise<ScanStatus | null>;
    getUnexecutedExploits(): Promise<ExploitableContract[]>;
    hasBeenSimulated(contractAddress: string, functionName: string): Promise<boolean>;
    getContractsByChain(chainId: number, limit?: number): Promise<ContractInfo[]>;
    getStats(): Promise<any>;
    saveExtraction(extraction: {
        contractAddress: string;
        functionName: string;
        extractedValue: bigint;
        gasCost: bigint;
        netProfit: bigint;
        txHash: string;
        timestamp: Date;
    }): Promise<void>;
    getExtractions(): Promise<Array<{
        contractAddress: string;
        functionName: string;
        extractedValue: bigint;
        gasCost: bigint;
        netProfit: bigint;
        txHash: string;
        timestamp: Date;
    }>>;
    saveTargetFunding(funding: {
        type: string;
        description: string;
        amount: bigint;
        timestamp: Date;
    }): Promise<void>;
    close(): Promise<void>;
}
export declare const db: Database;
//# sourceMappingURL=database.d.ts.map