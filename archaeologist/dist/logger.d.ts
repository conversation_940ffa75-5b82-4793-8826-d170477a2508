export declare enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3
}
declare class Logger {
    private level;
    constructor();
    private parseLogLevel;
    private timestamp;
    error(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
    success(message: string, ...args: any[]): void;
    exploit(message: string, ...args: any[]): void;
    transaction(message: string, ...args: any[]): void;
    extract(message: string, ...args: any[]): void;
    mark(message: string, ...args: any[]): void;
    relic(message: string, ...args: any[]): void;
    ghost(message: string, ...args: any[]): void;
    dust(message: string, ...args: any[]): void;
}
export declare const logger: Logger;
export {};
//# sourceMappingURL=logger.d.ts.map