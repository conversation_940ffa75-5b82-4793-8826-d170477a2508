export interface PerformanceMetrics {
    scanningMetrics: ScanningMetrics;
    simulationMetrics: SimulationMetrics;
    executionMetrics: ExecutionMetrics;
    systemMetrics: SystemMetrics;
}
export interface ScanningMetrics {
    totalScans: number;
    successfulScans: number;
    failedScans: number;
    averageScanTime: number;
    contractsPerSecond: number;
    chainsScanned: number;
    lastScanTime: Date;
}
export interface SimulationMetrics {
    totalSimulations: number;
    successfulSimulations: number;
    failedSimulations: number;
    averageSimulationTime: number;
    simulationsPerSecond: number;
    timeoutCount: number;
    gasEstimationFailures: number;
}
export interface ExecutionMetrics {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    totalValueExtracted: string;
    averageGasUsed: number;
    transactionFailures: number;
}
export interface SystemMetrics {
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: number;
    uptime: number;
    rpcCallCount: number;
    rpcFailureCount: number;
    databaseOperations: number;
}
export interface TimingData {
    operation: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    success?: boolean;
    metadata?: any;
}
export declare class PerformanceMonitor {
    private timings;
    private counters;
    private startTime;
    private rpcCallCount;
    private rpcFailureCount;
    private databaseOperations;
    startTiming(operationId: string, operation: string, metadata?: any): void;
    endTiming(operationId: string, success?: boolean): number;
    incrementCounter(name: string, value?: number): void;
    recordRpcCall(success?: boolean): void;
    recordDatabaseOperation(): void;
    getMetrics(): Promise<PerformanceMetrics>;
    private getScanningMetrics;
    private getSimulationMetrics;
    private getExecutionMetrics;
    private getSystemMetrics;
    displayMetrics(): Promise<void>;
    private calculateSuccessRate;
    reset(): void;
    getCounter(name: string): number;
    getTimingStats(operation: string): {
        count: number;
        average: number;
        min: number;
        max: number;
    };
}
export declare const performanceMonitor: PerformanceMonitor;
//# sourceMappingURL=metrics.d.ts.map