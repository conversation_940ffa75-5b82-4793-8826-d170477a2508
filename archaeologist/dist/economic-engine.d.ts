/**
 * 🏺 SURGICAL PRECISION: Economic Calculation Engine
 *
 * This module provides accurate economic calculations with real-time data feeds.
 * Eliminates guesswork in profitability assessment and risk calculation.
 *
 * Features:
 * 1. Real-time gas price feeds from multiple sources
 * 2. Accurate profit calculations with 2x safety margin
 * 3. Multi-chain gas price tracking
 * 4. Risk assessment with confidence intervals
 * 5. MEV protection cost calculation
 *
 * NO GUESSES. SURGICAL PRECISION.
 */
export interface EconomicAnalysis {
    chainId: number;
    gasPrice: {
        current: bigint;
        fast: bigint;
        standard: bigint;
        safe: bigint;
        source: string;
        timestamp: number;
    };
    costs: {
        estimatedGas: bigint;
        gasCost: bigint;
        flashbotsTip: bigint;
        totalCost: bigint;
    };
    profitability: {
        estimatedValue: bigint;
        netProfit: bigint;
        profitMargin: number;
        meetsThreshold: boolean;
        riskAdjustedProfit: bigint;
    };
    risk: {
        level: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
        factors: string[];
        confidence: number;
        maxLoss: bigint;
    };
}
export interface GasPriceData {
    chainId: number;
    gasPrice: bigint;
    fast: bigint;
    standard: bigint;
    safe: bigint;
    source: string;
    timestamp: number;
}
declare class EconomicEngine {
    private gasPriceCache;
    private readonly CACHE_DURATION;
    private readonly PROFIT_THRESHOLD;
    private readonly FLASHBOTS_TIP_PERCENTAGE;
    /**
     * SURGICAL PRECISION: Comprehensive economic analysis
     */
    analyzeEconomics(chainId: number, estimatedValue: bigint, estimatedGas: bigint, riskFactors?: string[]): Promise<EconomicAnalysis>;
    /**
     * Get real-time gas prices from multiple sources
     */
    private getGasPrices;
    /**
     * Fetch gas prices from multiple sources for accuracy
     */
    private fetchGasPricesMultiSource;
    /**
     * Fetch from ETH Gas Station (Ethereum mainnet)
     */
    private fetchFromEthGasStation;
    /**
     * Fetch from RPC provider
     */
    private fetchFromRPC;
    /**
     * Fetch from Owlracle (multi-chain)
     */
    private fetchFromOwlracle;
    /**
     * Fallback gas prices when all sources fail
     */
    private getFallbackGasPrices;
    /**
     * Assess risk factors
     */
    private assessRisk;
    /**
     * Calculate risk-adjusted profit
     */
    private calculateRiskAdjustedProfit;
    /**
     * Log economic analysis
     */
    private logEconomicAnalysis;
    /**
     * Clear gas price cache
     */
    clearCache(): void;
}
export declare const economicEngine: EconomicEngine;
export {};
//# sourceMappingURL=economic-engine.d.ts.map