{"version": 3, "file": "merkle.js", "sourceRoot": "", "sources": ["../src/merkle.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AAgBhC,MAAa,oBAAoB;IAE/B,oDAAoD;IACpD,kBAAkB,CAAC,WAAmB;QACpC,MAAM,MAAM,GAAkB,EAAE,CAAC;QAEjC,kCAAkC;QAClC,MAAM,OAAO,GAAG,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,sBAAsB,CAAC,CAAC,CAAC,eAAe;QAEvG,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,kFAAkF;YAClF,MAAM,IAAI,GAAG,eAAM,CAAC,SAAS,CAC3B,eAAM,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CACrE,CAAC;YAEF,kDAAkD;YAClD,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI;gBACJ,KAAK;gBACL,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC;gBACrC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;aACxC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,+CAA+C;IACvC,sBAAsB,CAAC,IAAY;QACzC,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,WAAW,GAAG,IAAI,CAAC;QAEvB,2DAA2D;QAC3D,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,0BAA0B;YAC1B,MAAM,OAAO,GAAG,eAAM,CAAC,SAAS,CAC9B,eAAM,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAC/D,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEpB,4BAA4B;YAC5B,WAAW,GAAG,eAAM,CAAC,SAAS,CAC5B,eAAM,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CACtE,CAAC;QACJ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,IAAY,EAAE,KAAe;QACjD,IAAI,WAAW,GAAG,IAAI,CAAC;QAEvB,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;YAC5B,WAAW,GAAG,eAAM,CAAC,SAAS,CAC5B,eAAM,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CACtE,CAAC;QACJ,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,wBAAwB;IACxB,WAAW,CAAC,KAAe,EAAE,IAAY,EAAE,IAAY;QACrD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACvD,OAAO,cAAc,KAAK,IAAI,CAAC;IACjC,CAAC;IAED,2CAA2C;IAC3C,0BAA0B,CAAC,WAAmB;QAC5C,MAAM,SAAS,GAAY,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAEpD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,gCAAgC;YAChC,SAAS,CAAC,IAAI,CAAC;gBACb,WAAW;gBACX,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,mBAAmB;gBACxC,KAAK,CAAC,KAAK;aACZ,CAAC,CAAC;YAEH,SAAS,CAAC,IAAI,CAAC;gBACb,WAAW;gBACX,qBAAqB,EAAE,QAAQ;gBAC/B,KAAK,CAAC,KAAK;aACZ,CAAC,CAAC;YAEH,SAAS,CAAC,IAAI,CAAC;gBACb,KAAK,CAAC,KAAK;gBACX,WAAW;gBACX,qBAAqB;gBACrB,KAAK,CAAC,KAAK;aACZ,CAAC,CAAC;YAEH,mBAAmB;YACnB,SAAS,CAAC,IAAI,CAAC;gBACb,WAAW;gBACX,qBAAqB;gBACrB,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,IAAI;aACX,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,8DAA8D;IAC9D,uBAAuB,CAAC,iBAAyB,EAAE,WAAmB;QACpE,MAAM,SAAS,GAAY,EAAE,CAAC;QAE9B,QAAQ,iBAAiB,EAAE,CAAC;YAC1B,KAAK,SAAS;gBACZ,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACnB,MAAM;YAER,KAAK,gBAAgB;gBACnB,SAAS,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC9B,SAAS,CAAC,IAAI,CAAC,CAAC,eAAM,CAAC,WAAW,CAAC,CAAC,CAAC;gBACrC,MAAM;YAER,KAAK,0CAA0C;gBAC7C,OAAO,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;YAEtD,KAAK,kCAAkC;gBACrC,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBACpD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;oBAC3B,SAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,qBAAqB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpE,CAAC;gBACD,MAAM;YAER,KAAK,eAAe;gBAClB,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACnB,MAAM;YAER,KAAK,sBAAsB;gBACzB,SAAS,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC9B,MAAM;YAER;gBACE,gCAAgC;gBAChC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACnB,SAAS,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC9B,SAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC,CAAC;gBACrD,MAAM;QACV,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,yDAAyD;IACzD,wBAAwB,CAAC,WAAmB;QAM1C,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC;YAClD,aAAa,EAAE;gBACb,GAAG,EAAE,cAAc;gBACnB,GAAG,EAAE,iBAAiB;gBACtB,qBAAqB,EAAE,QAAQ;gBAC/B,qBAAqB,EAAE,QAAQ;gBAC/B,sBAAsB,EAAE,SAAS;gBACjC,uBAAuB,EAAE,UAAU;gBACnC,eAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,cAAc;aAC5C;YACD,aAAa,EAAE;gBACb,WAAW;gBACX,eAAM,CAAC,WAAW;gBAClB,4CAA4C;gBAC5C,4CAA4C;gBAC5C,4CAA4C;aAC7C;YACD,eAAe,EAAE;gBACf,CAAC,EAAE,UAAU;gBACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,oBAAoB;gBACnD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,EAAE,WAAW;gBAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,EAAE,YAAY;gBACnD,UAAU,EAAE,cAAc;gBAC1B,UAAU,CAAC,YAAY;aACxB;SACF,CAAC;IACJ,CAAC;IAED,iDAAiD;IACjD,yBAAyB,CAAC,iBAAyB;QACjD,MAAM,gBAAgB,GAAG;YACvB,WAAW,EAAE,cAAc;YAC3B,OAAO;YACP,QAAQ;YACR,OAAO;YACP,SAAS;SACV,CAAC;QAEF,OAAO,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CACvC,iBAAiB,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CACpD,CAAC;IACJ,CAAC;IAED,qDAAqD;IACrD,yBAAyB,CAAC,WAAgB,EAAE,WAAmB;QAC7D,MAAM,SAAS,GAAY,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,IAAI,EAAE,CAAC;QAExC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,EAAE,CAAC,CAAC;QACd,CAAC;QAED,mDAAmD;QACnD,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAChD,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAC1E,CAAC;QAEF,IAAI,cAAc,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,6BAA6B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACjE,CAAC;QAED,2CAA2C;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAE5D,sCAAsC;QACtC,MAAM,iBAAiB,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAE/E,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,2BAA2B;IACpE,CAAC;IAEO,6BAA6B,CAAC,MAAa,EAAE,WAAmB;QACtE,MAAM,SAAS,GAAY,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAEpD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAU,EAAE,CAAC;YAEzB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,SAAS;wBACZ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBACzB,MAAM;oBACR,KAAK,SAAS;wBACZ,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,QAAQ;wBAC5C,MAAM;oBACR,KAAK,WAAW;wBACd,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBACzB,MAAM;oBACR,KAAK,SAAS;wBACZ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACxB,MAAM;oBACR;wBACE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;YAED,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,6BAA6B,CAAC,MAAa,EAAE,QAAa;QAChE,MAAM,YAAY,GAAY,EAAE,CAAC;QAEjC,kCAAkC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAU,EAAE,CAAC;YAEzB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;YACjE,CAAC;YAED,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,mBAAmB,CAAC,IAAY,EAAE,QAAa,EAAE,KAAa;QACpE,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACvE,KAAK,SAAS,CAAC;YACf,KAAK,MAAM;gBACT,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACvE,KAAK,SAAS;gBACZ,OAAO,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,eAAM,CAAC,QAAQ,CAAC;YAC3D,KAAK,WAAW;gBACd,OAAO,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;YAC/C,KAAK,MAAM;gBACT,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;YACzB,KAAK,QAAQ;gBACX,OAAO,eAAe,KAAK,EAAE,CAAC;YAChC;gBACE,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,IAAY;QACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,CAAC;QACtC,IAAI,IAAI,KAAK,SAAS;YAAE,OAAO,eAAM,CAAC,WAAW,CAAC;QAClD,IAAI,IAAI,KAAK,MAAM;YAAE,OAAO,KAAK,CAAC;QAClC,IAAI,IAAI,KAAK,QAAQ;YAAE,OAAO,EAAE,CAAC;QACjC,IAAI,IAAI,KAAK,SAAS;YAAE,OAAO,eAAM,CAAC,QAAQ,CAAC;QAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,EAAE,CAAC;QACnC,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AArTD,oDAqTC;AAEY,QAAA,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC"}