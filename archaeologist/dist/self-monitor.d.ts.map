{"version": 3, "file": "self-monitor.d.ts", "sourceRoot": "", "sources": ["../src/self-monitor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAQH,MAAM,WAAW,YAAY;IAC3B,OAAO,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,CAAC;IACvD,UAAU,EAAE,eAAe,EAAE,CAAC;IAC9B,OAAO,EAAE,aAAa,CAAC;IACvB,MAAM,EAAE,WAAW,EAAE,CAAC;IACtB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,CAAC;IACtD,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAChC,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,aAAa;IAC5B,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,eAAe,EAAE,MAAM,CAAC;IACxB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,iBAAiB,EAAE,MAAM,CAAC;CAC3B;AAED,MAAM,WAAW,WAAW;IAC1B,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IACjD,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,OAAO,CAAC;IACrB,YAAY,EAAE,OAAO,CAAC;IACtB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,cAAM,WAAW;IACf,OAAO,CAAC,aAAa,CAAsB;IAC3C,OAAO,CAAC,OAAO,CAAgB;IAC/B,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,SAAS,CAAK;IACtB,OAAO,CAAC,UAAU,CAAK;IACvB,OAAO,CAAC,aAAa,CAAgB;IACrC,OAAO,CAAC,QAAQ,CAA+B;;IAQ/C;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAYzB;;OAEG;IACG,kBAAkB,IAAI,OAAO,CAAC,YAAY,CAAC;IA8DjD;;OAEG;YACW,wBAAwB;IA8CtC;;OAEG;YACW,kBAAkB;IAoEhC;;OAEG;YACW,0BAA0B;IAkCxC;;OAEG;YACW,mBAAmB;IAsCjC;;OAEG;YACW,iBAAiB;IAoC/B;;OAEG;YACW,cAAc;IAgC5B;;OAEG;IACH,OAAO,CAAC,aAAa;IAWrB;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAmB9B;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAiC/B;;OAEG;IACH,OAAO,CAAC,iBAAiB;IASzB;;OAEG;IACH,OAAO,CAAC,aAAa;IAarB;;OAEG;YACW,gBAAgB;IAoC9B;;OAEG;IACH,OAAO,CAAC,eAAe;IAqBvB;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAWjC;;OAEG;IACH,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,GAAG,IAAI;IAUxD;;OAEG;IACH,aAAa,IAAI,IAAI;IAQrB;;OAEG;IACH,gBAAgB,IAAI,YAAY,GAAG,IAAI;IAKvC;;OAEG;IACH,gBAAgB,IAAI,YAAY,EAAE;CAGnC;AAED,eAAO,MAAM,WAAW,aAAoB,CAAC"}