import { Database } from './database';
/**
 * 🏺 DUST COLLECTION: Compound Accumulation System
 * Automatically reinvests accumulated dust into larger opportunities
 */
export interface AccumulationTarget {
    type: 'infrastructure' | 'tools' | 'opportunities';
    description: string;
    requiredAmount: bigint;
    priority: number;
    action: () => Promise<void>;
}
export interface AccumulationState {
    totalAccumulated: bigint;
    availableBalance: bigint;
    reinvestedAmount: bigint;
    targetsFunded: number;
    nextTarget?: AccumulationTarget;
}
export declare class CompoundAccumulator {
    private db;
    private readonly DUST_THRESHOLD;
    private readonly COMPOUND_THRESHOLD;
    private readonly WAR_CHEST_THRESHOLD;
    constructor(db: Database);
    /**
     * 🏺 DUST COLLECTION: Record successful extraction for accumulation
     */
    recordExtraction(contractAddress: string, functionName: string, extractedValue: bigint, gasCost: bigint, txHash: string): Promise<void>;
    /**
     * 🏺 DUST COLLECTION: Get current accumulation state
     */
    getAccumulationState(): Promise<AccumulationState>;
    /**
     * 🏺 DUST COLLECTION: Check if we should compound accumulated dust
     */
    private checkCompoundingOpportunity;
    /**
     * 🏺 DUST COLLECTION: Execute compounding strategy
     */
    private executeCompounding;
    /**
     * 🏺 DUST COLLECTION: Get available balance for compounding
     */
    private getAvailableBalance;
    /**
     * 🏺 DUST COLLECTION: Get number of targets already funded
     */
    private getTargetsFunded;
    /**
     * 🏺 DUST COLLECTION: Get next compounding target based on available balance
     */
    private getNextTarget;
    /**
     * 🏺 DUST COLLECTION: Define compounding targets in priority order
     */
    private getCompoundingTargets;
    /**
     * 🏺 DUST COLLECTION: Record that a target was funded
     */
    private recordTargetFunding;
    /**
     * 🏺 DUST COLLECTION: Log accumulation progress
     */
    logAccumulationProgress(): Promise<void>;
    /**
     * 🏺 DUST COLLECTION: Calculate compound growth rate
     */
    calculateGrowthRate(): Promise<number>;
}
//# sourceMappingURL=compound-accumulator.d.ts.map