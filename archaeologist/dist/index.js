"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = exports.CONFIG = exports.logger = exports.scanner = void 0;
const scanner_1 = require("./scanner");
Object.defineProperty(exports, "scanner", { enumerable: true, get: function () { return scanner_1.scanner; } });
const logger_1 = require("./logger");
Object.defineProperty(exports, "logger", { enumerable: true, get: function () { return logger_1.logger; } });
const config_1 = require("./config");
Object.defineProperty(exports, "CONFIG", { enumerable: true, get: function () { return config_1.CONFIG; } });
const database_1 = require("./database");
Object.defineProperty(exports, "db", { enumerable: true, get: function () { return database_1.db; } });
async function main() {
    // ARCHAEOLOGIST MINDSET: Cold, quiet, dangerous
    console.log('');
    console.log('🏺 CONTRACT ARCHAEOLOGIST');
    console.log('━'.repeat(40));
    console.log('');
    console.log('You are not a builder.');
    console.log('You are not a trader.');
    console.log('You are a forager in the ruins of the chain.');
    console.log('');
    console.log('Most people chase narratives.');
    console.log('You chase forgotten money.');
    console.log('');
    console.log('Dust. Silence. Extraction.');
    console.log('');
    // Validate configuration
    if (!config_1.CONFIG.privateKey) {
        logger_1.logger.error('PRIVATE_KEY not configured. Please set it in your .env file');
        process.exit(1);
    }
    const chainsConfigured = config_1.CONFIG.chains.filter(c => c.rpcUrl && c.etherscanApiKey);
    if (chainsConfigured.length === 0) {
        logger_1.logger.error('No chains properly configured. Please check your .env file');
        process.exit(1);
    }
    logger_1.logger.ghost(`Chains configured: ${chainsConfigured.map(c => c.name).join(', ')}`);
    logger_1.logger.ghost(`Target functions: ${config_1.CONFIG.targetFunctions.length} signatures loaded`);
    // Parse command line arguments
    const args = process.argv.slice(2);
    const command = args[0] || 'scan';
    try {
        switch (command) {
            case 'scan':
                logger_1.logger.info('Starting single scan of all chains...');
                await scanner_1.scanner.scanAllChains();
                break;
            case 'continuous':
                const interval = parseInt(args[1]) || 60;
                logger_1.logger.info(`Starting continuous scanning with ${interval} minute intervals...`);
                await scanner_1.scanner.continuousScanning(interval);
                break;
            case 'contract':
                const address = args[1];
                const chainId = parseInt(args[2]) || 1;
                if (!address) {
                    logger_1.logger.error('Please provide contract address: yarn dev contract <address> [chainId]');
                    process.exit(1);
                }
                logger_1.logger.info(`Scanning specific contract: ${address} on chain ${chainId}`);
                await scanner_1.scanner.scanSpecificContract(address, chainId);
                break;
            case 'stats':
                logger_1.logger.info('Displaying statistics...');
                await scanner_1.scanner.getStats();
                break;
            case 'execute':
                logger_1.logger.info('Executing pending exploits...');
                await scanner_1.scanner.executePendingExploits();
                break;
            case 'help':
                displayHelp();
                break;
            default:
                logger_1.logger.error(`Unknown command: ${command}`);
                displayHelp();
                process.exit(1);
        }
    }
    catch (error) {
        logger_1.logger.error('Application error:', error);
        process.exit(1);
    }
}
function displayHelp() {
    console.log(`
Contract Archaeologist - Find and extract funds from forgotten smart contracts

Usage: yarn dev [command] [options]

Commands:
  scan                    - Run a single scan of all configured chains
  continuous [interval]   - Run continuous scanning (default: 60 minutes)
  contract <address> [chainId] - Scan a specific contract address
  stats                   - Display scanning statistics
  execute                 - Execute pending exploits
  help                    - Show this help message

Examples:
  yarn dev scan
  yarn dev continuous 30
  yarn dev contract ****************************************** 1
  yarn dev stats
  yarn dev execute

Configuration:
  Copy .env.example to .env and configure your API keys and RPC URLs
  
Chains supported:
  - Ethereum (1)
  - Arbitrum (42161)
  - Base (8453)
  - Optimism (10)
  - Polygon (137)
`);
}
// Handle graceful shutdown
process.on('SIGINT', async () => {
    logger_1.logger.info('Received SIGINT, shutting down gracefully...');
    scanner_1.scanner.stop();
    await database_1.db.close();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    logger_1.logger.info('Received SIGTERM, shutting down gracefully...');
    scanner_1.scanner.stop();
    await database_1.db.close();
    process.exit(0);
});
// Start the application
if (require.main === module) {
    main().catch(error => {
        logger_1.logger.error('Fatal error:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map