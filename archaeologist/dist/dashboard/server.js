"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.server = exports.app = void 0;
exports.broadcast = broadcast;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const ws_1 = require("ws");
const http_1 = require("http");
const database_1 = require("../database");
const scanner_1 = require("../scanner");
const monitor_1 = require("../monitor");
// 🏺 LEGACY FUZZER REMOVED
const logger_1 = require("../logger");
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
exports.server = server;
const wss = new ws_1.WebSocketServer({ server });
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json());
app.use(express_1.default.static(path_1.default.join(__dirname, 'public')));
// WebSocket connections for real-time updates
const clients = new Set();
wss.on('connection', (ws) => {
    clients.add(ws);
    logger_1.logger.info('Dashboard client connected');
    ws.on('close', () => {
        clients.delete(ws);
        logger_1.logger.info('Dashboard client disconnected');
    });
});
// Broadcast to all connected clients
function broadcast(data) {
    clients.forEach((client) => {
        if (client.readyState === 1) { // WebSocket.OPEN
            client.send(JSON.stringify(data));
        }
    });
}
// API Routes
// Get system statistics
app.get('/api/stats', async (req, res) => {
    try {
        const stats = await database_1.db.getStats();
        const pendingExploits = await database_1.db.getUnexecutedExploits();
        res.json({
            ...stats,
            pendingExploits: pendingExploits.length,
            isScanning: scanner_1.scanner.getIsRunning(),
            isMonitoring: monitor_1.blockMonitor.isRunning(),
            monitoringStatus: monitor_1.blockMonitor.getMonitoringStatus()
        });
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to fetch stats' });
    }
});
// Get recent exploits
app.get('/api/exploits', async (req, res) => {
    try {
        const exploits = await database_1.db.getUnexecutedExploits();
        res.json(exploits);
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to fetch exploits' });
    }
});
// Get contracts by chain
app.get('/api/contracts/:chainId', async (req, res) => {
    try {
        const chainId = parseInt(req.params.chainId);
        const limit = parseInt(req.query.limit) || 100;
        const contracts = await database_1.db.getContractsByChain(chainId, limit);
        res.json(contracts);
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to fetch contracts' });
    }
});
// Get scan status for all chains
app.get('/api/scan-status', async (req, res) => {
    try {
        const chains = [1, 42161, 8453, 10, 137]; // Ethereum, Arbitrum, Base, Optimism, Polygon
        const statuses = await Promise.all(chains.map(async (chainId) => {
            const status = await database_1.db.getScanStatus(chainId);
            return { chainId, ...status };
        }));
        res.json(statuses);
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to fetch scan status' });
    }
});
// Get fuzzing results
app.get('/api/fuzzing-results', async (req, res) => {
    try {
        const contractAddress = req.query.contract;
        const limit = parseInt(req.query.limit) || 100;
        const results = await database_1.db.getFuzzingResults(contractAddress, limit);
        res.json(results);
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to fetch fuzzing results' });
    }
});
// Control endpoints
// Start/stop scanning
app.post('/api/scan/:action', async (req, res) => {
    try {
        const action = req.params.action;
        if (action === 'start') {
            scanner_1.scanner.scanAllChains();
            broadcast({ type: 'scan_started' });
            res.json({ message: 'Scan started' });
        }
        else if (action === 'stop') {
            scanner_1.scanner.stop();
            broadcast({ type: 'scan_stopped' });
            res.json({ message: 'Scan stopped' });
        }
        else if (action === 'incremental') {
            scanner_1.scanner.scanAllChainsIncremental();
            broadcast({ type: 'incremental_scan_started' });
            res.json({ message: 'Incremental scan started' });
        }
        else {
            res.status(400).json({ error: 'Invalid action' });
        }
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to control scanner' });
    }
});
// Start/stop monitoring
app.post('/api/monitor/:action', async (req, res) => {
    try {
        const action = req.params.action;
        if (action === 'start') {
            await monitor_1.blockMonitor.startMonitoring();
            broadcast({ type: 'monitoring_started' });
            res.json({ message: 'Monitoring started' });
        }
        else if (action === 'stop') {
            await monitor_1.blockMonitor.stopMonitoring();
            broadcast({ type: 'monitoring_stopped' });
            res.json({ message: 'Monitoring stopped' });
        }
        else {
            res.status(400).json({ error: 'Invalid action' });
        }
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to control monitor' });
    }
});
// Execute pending exploits
app.post('/api/execute', async (req, res) => {
    try {
        await scanner_1.scanner.executePendingExploits();
        broadcast({ type: 'exploits_executed' });
        res.json({ message: 'Exploits executed' });
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to execute exploits' });
    }
});
// Scan specific contract
app.post('/api/scan-contract', async (req, res) => {
    try {
        const { address, chainId } = req.body;
        if (!address || !chainId) {
            return res.status(400).json({ error: 'Address and chainId are required' });
        }
        await scanner_1.scanner.scanSpecificContract(address, chainId);
        broadcast({ type: 'contract_scanned', address, chainId });
        res.json({ message: 'Contract scanned' });
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to scan contract' });
    }
});
// Fuzz specific contract
app.post('/api/fuzz-contract', async (req, res) => {
    try {
        const { address, chainId, maxIterations } = req.body;
        if (!address || !chainId) {
            return res.status(400).json({ error: 'Address and chainId are required' });
        }
        // This would need to be implemented with proper contract fetching
        // const contract = await contractFetcher.fetchContractDetails(chain, address);
        // const results = await contractFuzzer.fuzzContract(contract, targetFunctions, maxIterations);
        broadcast({ type: 'contract_fuzzed', address, chainId });
        res.json({ message: 'Contract fuzzing started' });
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to fuzz contract' });
    }
});
// Get system logs
app.get('/api/logs', async (req, res) => {
    try {
        // This would need to be implemented with proper log storage
        const logs = [
            { timestamp: new Date().toISOString(), level: 'info', message: 'System started' },
            { timestamp: new Date().toISOString(), level: 'warn', message: 'No contracts found on chain' },
        ];
        res.json(logs);
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to fetch logs' });
    }
});
// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});
// Serve the dashboard
app.get('/', (req, res) => {
    res.sendFile(path_1.default.join(__dirname, 'public', 'index.html'));
});
const PORT = process.env.DASHBOARD_PORT || 3000;
server.listen(PORT, () => {
    logger_1.logger.info(`Dashboard server running on port ${PORT}`);
    logger_1.logger.info(`Dashboard URL: http://localhost:${PORT}`);
});
//# sourceMappingURL=server.js.map