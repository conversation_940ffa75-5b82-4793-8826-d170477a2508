{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../src/logger.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,qCAAkC;AAElC,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,yCAAS,CAAA;IACT,uCAAQ,CAAA;IACR,uCAAQ,CAAA;IACR,yCAAS,CAAA;AACX,CAAC,EALW,QAAQ,wBAAR,QAAQ,QAKnB;AAED,MAAM,MAAM;IAGV;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAM,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAEO,aAAa,CAAC,KAAa;QACjC,QAAQ,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5B,KAAK,OAAO,CAAC,CAAC,OAAO,QAAQ,CAAC,KAAK,CAAC;YACpC,KAAK,MAAM,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC;YAClC,KAAK,MAAM,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC;YAClC,KAAK,OAAO,CAAC,CAAC,OAAO,QAAQ,CAAC,KAAK,CAAC;YACpC,OAAO,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC;QAChC,CAAC;IACH,CAAC;IAEO,SAAS;QACf,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,GAAG,IAAW;QACnC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,YAAY,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,GAAG,IAAW;QAClC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,WAAW,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,GAAG,IAAW;QAClC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,WAAW,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,GAAG,IAAW;QACnC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,YAAY,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,OAAO,CAAC,OAAe,EAAE,GAAG,IAAW;QACrC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,cAAc,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,OAAO,CAAC,OAAe,EAAE,GAAG,IAAW;QACrC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,cAAc,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,GAAG,IAAW;QACzC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,SAAS,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,gDAAgD;IAEhD,iDAAiD;IACjD,OAAO,CAAC,OAAe,EAAE,GAAG,IAAW;QACrC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,cAAc,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,uCAAuC;IACvC,IAAI,CAAC,OAAe,EAAE,GAAG,IAAW;QAClC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,WAAW,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,6CAA6C;IAC7C,KAAK,CAAC,OAAe,EAAE,GAAG,IAAW;QACnC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,YAAY,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,iCAAiC;IACjC,KAAK,CAAC,OAAe,EAAE,GAAG,IAAW;QACnC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,YAAY,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,+CAA+C;IAC/C,IAAI,CAAC,OAAe,EAAE,GAAG,IAAW;QAClC,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,WAAW,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;CACF;AAEY,QAAA,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC"}