{"version": 3, "file": "evaluator.js", "sourceRoot": "", "sources": ["../src/evaluator.ts"], "names": [], "mappings": ";;;AACA,qCAAkC;AAClC,yCAAgC;AAChC,oCAAoC;AACpC,uDAAmD;AACnD,iDAA6C;AAE7C,MAAa,SAAS;IACpB,KAAK,CAAC,mBAAmB,CAAC,WAA+B;QACvD,MAAM,QAAQ,GAA0B,EAAE,CAAC;QAE3C,eAAM,CAAC,KAAK,CAAC,qCAAqC,WAAW,CAAC,MAAM,qBAAqB,CAAC,CAAC;QAE3F,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YACjC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,mFAAmF;gBACnF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBAEzD,IAAI,QAAQ,EAAE,CAAC;oBACb,yDAAyD;oBACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;oBAExD,MAAM,OAAO,GAAwB;wBACnC,OAAO,EAAE,MAAM,CAAC,eAAe;wBAC/B,YAAY,EAAE,MAAM,CAAC,YAAY;wBACjC,SAAS,EAAE,MAAM,CAAC,SAAS;wBAC3B,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,GAAG;wBAC5C,WAAW,EAAE,MAAM,CAAC,WAAW;wBAC/B,QAAQ;wBACR,OAAO,EAAE,CAAC,EAAE,wCAAwC;wBACpD,UAAU,EAAE,IAAI,IAAI,EAAE;wBACtB,QAAQ,EAAE,KAAK;qBAChB,CAAC;oBAEF,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvB,MAAM,aAAE,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;oBAE1C,eAAM,CAAC,OAAO,CAAC,qBAAqB,MAAM,CAAC,eAAe,MAAM,MAAM,CAAC,YAAY,eAAe,QAAQ,GAAG,CAAC,CAAC;oBAE/G,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;wBACtB,eAAM,CAAC,OAAO,CAAC,kBAAkB,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;oBACzD,CAAC;oBACD,IAAI,MAAM,CAAC,mBAAmB,EAAE,CAAC;wBAC/B,eAAM,CAAC,OAAO,CAAC,4BAA4B,MAAM,CAAC,mBAAmB,GAAG,CAAC,CAAC;oBAC5E,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,2DAA2D;oBAC3D,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,EAAE,CAAC;wBAC1C,eAAM,CAAC,KAAK,CAAC,kDAAkD,MAAM,CAAC,eAAe,MAAM,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;wBAClH,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;4BAC1B,eAAM,CAAC,KAAK,CAAC,uBAAuB,MAAM,CAAC,cAAc,MAAM,CAAC,CAAC;wBACnE,CAAC;wBACD,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;4BACvB,eAAM,CAAC,KAAK,CAAC,oBAAoB,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;wBACzD,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,IAAI,CAAC,qBAAqB,MAAM,CAAC,eAAe,MAAM,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;oBACtF,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,eAAe,MAAM,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAC1F,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,eAAM,CAAC,OAAO,CAAC,oCAAoC,QAAQ,CAAC,MAAM,4BAA4B,CAAC,CAAC;QAClG,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,YAAY,CAAC,UAA4B;QAC/C,uDAAuD;QACvD,IAAI,CAAC,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QAE7D,qDAAqD;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,iBAAiB;QACxC,MAAM,UAAU,GAAG,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC;QACrD,MAAM,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,CAAC,iBAAiB;QAEvD,+DAA+D;QAC/D,MAAM,eAAe,GAAG,UAAU,GAAG,CAAC,CAAC;QAEvC,eAAM,CAAC,KAAK,CAAC,8BAA8B,cAAc,aAAa,UAAU,mBAAmB,eAAe,MAAM,CAAC,CAAC;QAE1H,OAAO,cAAc,GAAG,eAAe,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,UAA4B;QAC7D,IAAI,CAAC;YACH,IAAI,CAAC,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAiB;YAC1G,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAEpD,2BAA2B;YAC3B,MAAM,WAAW,GAAa,EAAE,CAAC;YAEjC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;gBACxD,WAAW,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,UAAU,CAAC,mBAAmB,IAAI,UAAU,CAAC,mBAAmB,GAAG,EAAE,EAAE,CAAC;gBAC1E,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC/C,CAAC;YAED,2CAA2C;YAC3C,MAAM,gBAAgB,GAAG,MAAM,gCAAc,CAAC,gBAAgB,CAC5D,CAAC,EAAE,4BAA4B;YAC/B,cAAc,EACd,YAAY,EACZ,WAAW,CACZ,CAAC;YAEF,iCAAiC;YACjC,0BAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;YAEzE,IAAI,gBAAgB,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;gBAClD,eAAM,CAAC,OAAO,CAAC,kBAAkB,UAAU,CAAC,eAAe,kBAAkB,gBAAgB,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACxI,eAAM,CAAC,OAAO,CAAC,kBAAkB,gBAAgB,CAAC,IAAI,CAAC,KAAK,KAAK,gBAAgB,CAAC,IAAI,CAAC,UAAU,eAAe,CAAC,CAAC;YACpH,CAAC;YAED,OAAO,gBAAgB,CAAC,aAAa,CAAC,cAAc,CAAC;QAEvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,UAAU,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC,CAAC;YACrF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,UAA4B;QAC5D,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,0CAA0C;QAE5D,oBAAoB;QACpB,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAC1B,IAAI,UAAU,CAAC,UAAU,IAAI,EAAE;gBAAE,QAAQ,IAAI,CAAC,CAAC;iBAC1C,IAAI,UAAU,CAAC,UAAU,IAAI,EAAE;gBAAE,QAAQ,IAAI,CAAC,CAAC;iBAC/C,IAAI,UAAU,CAAC,UAAU,GAAG,EAAE;gBAAE,QAAQ,IAAI,CAAC,CAAC;QACrD,CAAC;QAED,8BAA8B;QAC9B,IAAI,UAAU,CAAC,mBAAmB,EAAE,CAAC;YACnC,IAAI,UAAU,CAAC,mBAAmB,IAAI,EAAE;gBAAE,QAAQ,IAAI,CAAC,CAAC;iBACnD,IAAI,UAAU,CAAC,mBAAmB,GAAG,EAAE;gBAAE,QAAQ,IAAI,CAAC,CAAC;QAC9D,CAAC;QAED,4DAA4D;QAC5D,MAAM,qBAAqB,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;QACjF,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAE3D,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAClE,QAAQ,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,sCAAsC;QACtC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,aAAa,CAAC,iBAAqC;QACjD,OAAO,iBAAiB;aACrB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;aACzB,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/C,CAAC;IAEO,aAAa,CAAC,MAAwB;QAC5C,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,cAAc,IAAI,GAAG,CAAC,CAAC;QAChE,MAAM,UAAU,GAAwB;YACtC,OAAO,EAAE,MAAM,CAAC,eAAe;YAC/B,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,GAAG;YAC5C,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,QAAQ,EAAE,CAAC,EAAE,mBAAmB;YAChC,OAAO,EAAE,CAAC,EAAE,qCAAqC;YACjD,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,QAAQ,EAAE,KAAK;SAChB,CAAC;QAEF,eAAM,CAAC,OAAO,CAAC,+BAA+B,UAAU,CAAC,OAAO,sBAAsB,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;QACnH,OAAO,UAAU,CAAC;IACpB,CAAC;CACF;AA5LD,8BA4LC;AAEY,QAAA,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC"}