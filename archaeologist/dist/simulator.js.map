{"version": 3, "file": "simulator.js", "sourceRoot": "", "sources": ["../src/simulator.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;;AAEH,mCAAgC;AAIhC,qCAAkC;AAClC,yCAAgC;AAEhC,gDAAgD;AAChD,+DAA2D;AAC3D,2DAAuD;AAEvD,iDAA6C;AAE7C,MAAM,iBAAiB;IAGrB;QAFQ,cAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;QAGrD,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACzB,4CAA4C;QAC5C,MAAM,MAAM,GAAG;YACb,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;YACtE,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;YAC1E,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;YACjE,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;YACvE,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;SACvE,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,eAAM,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1D,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAC5C,eAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;gBAC5E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC1E,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,YAA0B;QAC/C,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,YAAY,CAAC;QACpD,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,yDAAyD;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,0BAAW,CAAC,aAAa,EAAE,CAAC;QAE5B,eAAM,CAAC,KAAK,CAAC,2BAA2B,gBAAgB,CAAC,MAAM,kBAAkB,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QAErG,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,eAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YACrE,0BAAW,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YACtD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,CAAC;YACH,6EAA6E;YAC7E,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/C,eAAM,CAAC,KAAK,CAAC,8CAA8C,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;gBAE/E,MAAM,gBAAgB,GAAG,MAAM,oCAAgB,CAAC,eAAe,CAC7D,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,OAAO,CACjB,CAAC;gBAEF,IAAI,gBAAgB,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;oBACjD,eAAM,CAAC,OAAO,CAAC,+BAA+B,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,mBAAmB,CAAC,CAAC;oBACnH,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACvD,eAAM,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;oBACnC,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,2BAA2B;YAC3B,MAAM,gBAAgB,GAAG,IAAI,eAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEvF,KAAK,MAAM,cAAc,IAAI,gBAAgB,EAAE,CAAC;gBAC9C,IAAI,CAAC;oBACH,4BAA4B;oBAC5B,IAAI,MAAM,aAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;wBACrE,eAAM,CAAC,KAAK,CAAC,8BAA8B,cAAc,CAAC,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;wBACzF,SAAS;oBACX,CAAC;oBAED,+DAA+D;oBAC/D,eAAM,CAAC,KAAK,CAAC,4BAA4B,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC;oBAErE,IAAI,eAAe,CAAC;oBACpB,IAAI,CAAC;wBACH,eAAe,GAAG,MAAM,wCAAkB,CAAC,gBAAgB,CACzD,QAAQ,EACR,QAAQ,CAAC,OAAO,EAChB,cAAc,CAAC,SAAS,EACxB,QAAQ,CAAC,GAAG,EACZ,4CAA4C,CAAC,uBAAuB;yBACrE,CAAC;oBACJ,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,gEAAgE;wBAChE,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBAE5E,IAAI,YAAY,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EAAE,CAAC;4BACzD,eAAM,CAAC,KAAK,CAAC,uBAAuB,cAAc,CAAC,SAAS,aAAa,CAAC,CAAC;wBAC7E,CAAC;6BAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,2BAA2B,CAAC,EAAE,CAAC;4BAC9D,eAAM,CAAC,KAAK,CAAC,2BAA2B,cAAc,CAAC,SAAS,aAAa,CAAC,CAAC;wBACjF,CAAC;6BAAM,CAAC;4BACN,eAAM,CAAC,IAAI,CAAC,kCAAkC,cAAc,CAAC,SAAS,MAAM,YAAY,EAAE,CAAC,CAAC;wBAC9F,CAAC;wBAED,yBAAyB;wBACzB,eAAe,GAAG;4BAChB,YAAY,EAAE,cAAc,CAAC,IAAI;4BACjC,SAAS,EAAE,cAAc,CAAC,SAAS;4BACnC,UAAU,EAAE,KAAK;4BACjB,OAAO,EAAE,EAAE;4BACX,UAAU,EAAE,CAAC;4BACb,mBAAmB,EAAE,CAAC;yBACvB,CAAC;oBACJ,CAAC;oBAED,4CAA4C;oBAC5C,MAAM,MAAM,GAAqB;wBAC/B,eAAe,EAAE,QAAQ,CAAC,OAAO;wBACjC,YAAY,EAAE,cAAc,CAAC,IAAI;wBACjC,SAAS,EAAE,cAAc,CAAC,SAAS;wBACnC,OAAO,EAAE,eAAe,CAAC,UAAU;wBACnC,UAAU,EAAE,eAAe,CAAC,UAAU,EAAE,UAAU,IAAI,EAAE;wBACxD,WAAW,EAAE,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE,CAAC;wBAC9D,KAAK,EAAE,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,6BAA6B;wBAC7E,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,0CAA0C;wBAC1C,UAAU,EAAE,eAAe,CAAC,UAAU;wBACtC,mBAAmB,EAAE,eAAe,CAAC,mBAAmB;wBACxD,gBAAgB,EAAE,eAAe,CAAC,OAAO;qBAC1C,CAAC;oBAEF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAErB,yBAAyB;oBACzB,MAAM,aAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;oBAEhC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wBACnB,eAAM,CAAC,OAAO,CAAC,wBAAwB,cAAc,CAAC,IAAI,KAAK,eAAe,CAAC,UAAU,eAAe,CAAC,CAAC;oBAC5G,CAAC;yBAAM,CAAC;wBACN,yEAAyE;wBACzE,IAAI,eAAe,CAAC,UAAU,GAAG,CAAC,IAAI,eAAe,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;4BAC9E,oDAAoD;4BACpD,eAAM,CAAC,KAAK,CAAC,sBAAsB,cAAc,CAAC,IAAI,KAAK,eAAe,CAAC,UAAU,eAAe,CAAC,CAAC;wBACxG,CAAC;6BAAM,CAAC;4BACN,yDAAyD;4BACzD,eAAM,CAAC,KAAK,CAAC,8BAA8B,cAAc,CAAC,IAAI,aAAa,CAAC,CAAC;wBAC/E,CAAC;oBACH,CAAC;gBAEH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,cAAc,CAAC,IAAI,OAAO,QAAQ,CAAC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;oBAEzF,MAAM,WAAW,GAAqB;wBACpC,eAAe,EAAE,QAAQ,CAAC,OAAO;wBACjC,YAAY,EAAE,cAAc,CAAC,IAAI;wBACjC,SAAS,EAAE,cAAc,CAAC,SAAS;wBACnC,OAAO,EAAE,KAAK;wBACd,UAAU,EAAE,EAAE;wBACd,WAAW,EAAE,CAAC;wBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;wBAC/D,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;qBACtB,CAAC;oBAEF,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC1B,MAAM,aAAE,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;YAED,0BAAW,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YACrD,eAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,CAAC,MAAM,gBAAgB,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QAE5F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,QAAQ,CAAC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,0BAAW,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,aAA6B;QAC/C,MAAM,UAAU,GAAuB,EAAE,CAAC;QAE1C,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;gBAC1D,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,YAAY,CAAC,QAAQ,CAAC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YACvF,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAAe;QACzB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;CAGF;AAEY,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC"}