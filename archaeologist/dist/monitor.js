"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.blockMonitor = exports.BlockMonitor = void 0;
const ethers_1 = require("ethers");
const config_1 = require("./config");
const fetcher_1 = require("./fetcher");
const filter_1 = require("./filter");
const simulator_1 = require("./simulator");
const evaluator_1 = require("./evaluator");
const executor_1 = require("./executor");
const database_1 = require("./database");
const logger_1 = require("./logger");
const ora_1 = __importDefault(require("ora"));
class BlockMonitor {
    constructor() {
        this.providers = new Map();
        this.listeners = new Map();
        this.isMonitoring = false;
        this.initializeProviders();
    }
    initializeProviders() {
        for (const chain of config_1.CONFIG.chains) {
            if (chain.rpcUrl) {
                try {
                    const provider = new ethers_1.ethers.JsonRpcProvider(chain.rpcUrl);
                    this.providers.set(chain.chainId, provider);
                    logger_1.logger.debug(`Block monitor initialized for ${chain.name} (${chain.chainId})`);
                }
                catch (error) {
                    logger_1.logger.error(`Failed to initialize block monitor for ${chain.name}:`, error);
                }
            }
        }
    }
    async startMonitoring() {
        if (this.isMonitoring) {
            logger_1.logger.warn('Block monitoring is already running');
            return;
        }
        this.isMonitoring = true;
        logger_1.logger.info('Starting real-time block monitoring...');
        for (const chain of config_1.CONFIG.chains) {
            if (chain.rpcUrl && chain.etherscanApiKey) {
                try {
                    await this.startChainMonitoring(chain);
                }
                catch (error) {
                    logger_1.logger.error(`Failed to start monitoring for ${chain.name}:`, error);
                }
            }
        }
    }
    async startChainMonitoring(chain) {
        const provider = this.providers.get(chain.chainId);
        if (!provider) {
            logger_1.logger.error(`No provider configured for ${chain.name}`);
            return;
        }
        logger_1.logger.info(`Starting block monitoring for ${chain.name}...`);
        // Get the last processed block
        const scanStatus = await database_1.db.getScanStatus(chain.chainId);
        let lastProcessedBlock = scanStatus?.lastProcessedBlock || 0;
        // If no previous scan, start from current block minus some buffer
        if (lastProcessedBlock === 0) {
            try {
                const currentBlock = await provider.getBlockNumber();
                lastProcessedBlock = currentBlock - 10; // Start 10 blocks back
            }
            catch (error) {
                logger_1.logger.error(`Failed to get current block for ${chain.name}:`, error);
                return;
            }
        }
        // Set up block listener
        const blockListener = async (blockNumber) => {
            try {
                await this.processNewBlock(chain, blockNumber, lastProcessedBlock);
                lastProcessedBlock = blockNumber;
            }
            catch (error) {
                logger_1.logger.error(`Error processing block ${blockNumber} on ${chain.name}:`, error);
            }
        };
        provider.on('block', blockListener);
        this.listeners.set(chain.chainId, () => provider.off('block', blockListener));
        logger_1.logger.info(`Block monitoring started for ${chain.name} from block ${lastProcessedBlock}`);
    }
    async processNewBlock(chain, blockNumber, lastProcessedBlock) {
        if (blockNumber <= lastProcessedBlock) {
            return; // Already processed
        }
        logger_1.logger.debug(`Processing new block ${blockNumber} on ${chain.name}`);
        try {
            // Get contracts created in this block
            const contracts = await fetcher_1.contractFetcher.fetchContractsByBlockRange(chain, blockNumber, blockNumber);
            if (contracts.length === 0) {
                // Update scan status even if no contracts found
                await database_1.db.updateScanStatus({
                    chainId: chain.chainId,
                    lastProcessedBlock: blockNumber,
                    lastScanTime: new Date(),
                    contractsScanned: 0,
                    exploitsFound: 0,
                    totalValue: '0'
                });
                return;
            }
            logger_1.logger.info(`Found ${contracts.length} new contracts in block ${blockNumber} on ${chain.name}`);
            // Save contracts to database
            for (const contract of contracts) {
                await database_1.db.saveContract(contract);
            }
            // Filter contracts with target functions
            const filteredResults = filter_1.contractFilter.filterContracts(contracts);
            if (filteredResults.length === 0) {
                await database_1.db.updateScanStatus({
                    chainId: chain.chainId,
                    lastProcessedBlock: blockNumber,
                    lastScanTime: new Date(),
                    contractsScanned: contracts.length,
                    exploitsFound: 0,
                    totalValue: '0'
                });
                return;
            }
            logger_1.logger.info(`Found ${filteredResults.length} contracts with target functions in block ${blockNumber}`);
            // Simulate target functions
            const simulationResults = await simulator_1.contractSimulator.batchSimulate(filteredResults);
            // Evaluate simulation results
            const exploitableContracts = await evaluator_1.evaluator.evaluateSimulations(simulationResults);
            if (exploitableContracts.length > 0) {
                logger_1.logger.info(`🎯 Found ${exploitableContracts.length} exploitable contracts in block ${blockNumber}!`);
                // Execute exploits immediately for time-sensitive opportunities
                const executionResults = await executor_1.executor.batchExecuteExploits(exploitableContracts);
                const successfulExecutions = executionResults.filter(r => r.success);
                if (successfulExecutions.length > 0) {
                    logger_1.logger.info(`✅ Successfully executed ${successfulExecutions.length} exploits from block ${blockNumber}`);
                }
            }
            // Update scan status
            await database_1.db.updateScanStatus({
                chainId: chain.chainId,
                lastProcessedBlock: blockNumber,
                lastScanTime: new Date(),
                contractsScanned: contracts.length,
                exploitsFound: exploitableContracts.length,
                totalValue: exploitableContracts.reduce((sum, e) => sum + parseFloat(e.estimatedValue || '0'), 0).toString()
            });
        }
        catch (error) {
            logger_1.logger.error(`Error processing block ${blockNumber} on ${chain.name}:`, error);
        }
    }
    async stopMonitoring() {
        if (!this.isMonitoring) {
            logger_1.logger.warn('Block monitoring is not running');
            return;
        }
        logger_1.logger.info('Stopping block monitoring...');
        // Remove all listeners
        for (const [chainId, removeListener] of this.listeners) {
            try {
                removeListener();
                logger_1.logger.debug(`Stopped monitoring for chain ${chainId}`);
            }
            catch (error) {
                logger_1.logger.error(`Error stopping monitoring for chain ${chainId}:`, error);
            }
        }
        this.listeners.clear();
        this.isMonitoring = false;
        logger_1.logger.info('Block monitoring stopped');
    }
    async catchUpMissedBlocks() {
        if (this.isMonitoring) {
            logger_1.logger.warn('Cannot catch up while monitoring is active');
            return;
        }
        logger_1.logger.info('Catching up missed blocks...');
        for (const chain of config_1.CONFIG.chains) {
            if (chain.rpcUrl && chain.etherscanApiKey) {
                await this.catchUpChainBlocks(chain);
            }
        }
    }
    async catchUpChainBlocks(chain) {
        const provider = this.providers.get(chain.chainId);
        if (!provider) {
            logger_1.logger.error(`No provider configured for ${chain.name}`);
            return;
        }
        const spinner = (0, ora_1.default)(`Catching up blocks for ${chain.name}...`).start();
        try {
            const scanStatus = await database_1.db.getScanStatus(chain.chainId);
            const currentBlock = await provider.getBlockNumber();
            const lastProcessedBlock = scanStatus?.lastProcessedBlock || currentBlock - 1000;
            if (currentBlock <= lastProcessedBlock) {
                spinner.info(`${chain.name} is up to date`);
                return;
            }
            const blockGap = currentBlock - lastProcessedBlock;
            spinner.text = `Catching up ${blockGap} blocks for ${chain.name}...`;
            // Process in chunks to avoid overwhelming the system
            const chunkSize = 100;
            for (let start = lastProcessedBlock + 1; start <= currentBlock; start += chunkSize) {
                const end = Math.min(start + chunkSize - 1, currentBlock);
                spinner.text = `Processing blocks ${start}-${end} on ${chain.name}...`;
                const contracts = await fetcher_1.contractFetcher.fetchContractsByBlockRange(chain, start, end);
                if (contracts.length > 0) {
                    // Save contracts
                    for (const contract of contracts) {
                        await database_1.db.saveContract(contract);
                    }
                    // Process as usual
                    const filteredResults = filter_1.contractFilter.filterContracts(contracts);
                    if (filteredResults.length > 0) {
                        const simulationResults = await simulator_1.contractSimulator.batchSimulate(filteredResults);
                        const exploitableContracts = await evaluator_1.evaluator.evaluateSimulations(simulationResults);
                        if (exploitableContracts.length > 0) {
                            spinner.info(`Found ${exploitableContracts.length} exploitable contracts in blocks ${start}-${end}`);
                            await executor_1.executor.batchExecuteExploits(exploitableContracts);
                        }
                    }
                }
                // Update progress
                await database_1.db.updateScanStatus({
                    chainId: chain.chainId,
                    lastProcessedBlock: end,
                    lastScanTime: new Date(),
                    contractsScanned: contracts.length,
                    exploitsFound: 0,
                    totalValue: '0'
                });
            }
            spinner.succeed(`Caught up ${blockGap} blocks for ${chain.name}`);
        }
        catch (error) {
            spinner.fail(`Failed to catch up blocks for ${chain.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            logger_1.logger.error(`Catch up error for ${chain.name}:`, error);
        }
    }
    isRunning() {
        return this.isMonitoring;
    }
    getMonitoringStatus() {
        const status = {};
        for (const chain of config_1.CONFIG.chains) {
            status[chain.chainId] = this.listeners.has(chain.chainId);
        }
        return status;
    }
}
exports.BlockMonitor = BlockMonitor;
exports.blockMonitor = new BlockMonitor();
//# sourceMappingURL=monitor.js.map