{"version": 3, "file": "batch-optimizer.js", "sourceRoot": "", "sources": ["../src/batch-optimizer.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AAChC,qCAAkC;AAwBlC,MAAa,cAAc;IAA3B;QACmB,sBAAiB,GAAG,4CAA4C,CAAC,CAAC,aAAa;QAC/E,mBAAc,GAAG,CAAC,CAAC;QACnB,mBAAc,GAAG,EAAE,CAAC;QACpB,0BAAqB,GAAG,IAAI,CAAC,CAAC,kCAAkC;IAgOnF,CAAC;IA9NC;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,WAAkC;QAC1D,eAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QAElE,0BAA0B;QAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QACnD,MAAM,gBAAgB,GAAkB,EAAE,CAAC;QAE3C,KAAK,MAAM,CAAC,OAAO,EAAE,gBAAgB,CAAC,IAAI,WAAW,EAAE,CAAC;YACtD,eAAM,CAAC,KAAK,CAAC,cAAc,gBAAgB,CAAC,MAAM,yBAAyB,OAAO,EAAE,CAAC,CAAC;YAEtF,2CAA2C;YAC3C,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACvD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC;gBACxD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC;gBACxD,OAAO,MAAM,GAAG,MAAM,CAAC,CAAC,mBAAmB;YAC7C,CAAC,CAAC,CAAC;YAEH,yBAAyB;YACzB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YAC7D,gBAAgB,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,eAAM,CAAC,OAAO,CAAC,aAAa,gBAAgB,CAAC,MAAM,2BAA2B,WAAW,CAAC,MAAM,cAAc,CAAC,CAAC;QAChH,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,WAAkC;QACrD,MAAM,MAAM,GAAG,IAAI,GAAG,EAAiC,CAAC;QAExD,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,WAAkC;QAC7D,MAAM,OAAO,GAAkB,EAAE,CAAC;QAClC,MAAM,SAAS,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;QAEnC,OAAO,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAChD,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpB,4CAA4C;gBAC5C,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;oBAC3C,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CACpC,CAAC,CAAC,eAAe,KAAK,UAAU,CAAC,eAAe;wBAChD,CAAC,CAAC,YAAY,KAAK,UAAU,CAAC,YAAY,CAC3C,CAAC;oBACF,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;wBACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;oBAC7B,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,sCAAsC;YAC/C,CAAC;QACH,CAAC;QAED,kEAAkE;QAClE,KAAK,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,WAAkC;QAC1D,IAAI,SAAS,GAAuB,IAAI,CAAC;QACzC,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,4BAA4B;QAC5B,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;YACvG,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YACjE,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,GAAG,UAAU,EAAE,CAAC;gBAC1C,SAAS,GAAG,KAAK,CAAC;gBAClB,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,WAAkC;QAC1D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE1C,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QAC9E,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YACvD,CAAC,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QAElE,+BAA+B;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,UAAU;QACjC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,UAAU,GAAG,UAAU,CAAC;QAE1C,gDAAgD;QAChD,MAAM,eAAe,GAAG,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO;QACvD,IAAI,UAAU,GAAG,eAAe,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0BAA0B;QAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;QAE9D,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS;YACT,WAAW;YACX,aAAa;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,WAAkC;QAC9D,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACzC,uBAAuB;YACvB,MAAM,KAAK,GAAG,IAAI,eAAM,CAAC,SAAS,CAAC;gBACjC,YAAY,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG;aACxF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,KAAK,CAAC,kBAAkB,CAAC,UAAU,CAAC,YAAY,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;YAE1F,OAAO;gBACL,MAAM,EAAE,UAAU,CAAC,eAAe;gBAClC,QAAQ,EAAE,QAAQ;aACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,kBAAkB,GAAG,IAAI,eAAM,CAAC,SAAS,CAAC;YAC9C,qHAAqH;SACtH,CAAC,CAAC;QAEH,OAAO,kBAAkB,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAAiB;QACzC,OAAO,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC5B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC3D,OAAO,SAAS,CAAC;YACnB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;gBACtF,OAAO,SAAS,CAAC;YACnB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtC,OAAO,MAAM,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,UAA+B;QAClE,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,UAAU;QACjC,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QACrE,MAAM,eAAe,GAAG,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO;QAEvD,OAAO,UAAU,CAAC,cAAc,IAAI,eAAe,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,UAA+B;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,UAAU;QACjC,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAErE,OAAO;YACL,UAAU,EAAE,UAAU,CAAC,cAAc;YACrC,QAAQ,EAAE,UAAU,CAAC,WAAW;YAChC,SAAS,EAAE,UAAU,CAAC,cAAc,GAAG,UAAU;YACjD,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,aAAa,EAAE,EAAE,CAAC,oCAAoC;SACvD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAsB;QACpC,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3F,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAC9E,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAE9E,eAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACjD,eAAM,CAAC,OAAO,CAAC,SAAS,OAAO,CAAC,MAAM,4BAA4B,CAAC,CAAC;QACpE,eAAM,CAAC,OAAO,CAAC,SAAS,gBAAgB,oBAAoB,CAAC,CAAC;QAC9D,eAAM,CAAC,OAAO,CAAC,SAAS,eAAM,CAAC,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;QAC1E,eAAM,CAAC,OAAO,CAAC,SAAS,eAAM,CAAC,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;QAE1E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACzB,eAAM,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,WAAW,CAAC,MAAM,WAAW,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC5H,CAAC;IACH,CAAC;CACF;AApOD,wCAoOC"}