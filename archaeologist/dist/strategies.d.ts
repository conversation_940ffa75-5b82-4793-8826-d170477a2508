interface FuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(functionAbi: any, userAddress: string): any[][];
}
declare abstract class BaseFuzzingStrategy implements FuzzingStrategy {
    abstract name: string;
    abstract description: string;
    abstract priority: number;
    abstract generateParameters(abiFunction: any): any[][];
    protected getParameterTypes(abiFunction: any): string[];
    protected generateAddress(type: 'zero' | 'random' | 'max' | 'common'): string;
    protected generateUint(bits: number, type: 'zero' | 'one' | 'max' | 'boundary' | 'random'): bigint;
}
export declare class BoundaryValueStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class OverflowUnderflowStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class ReentrancyStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class AccessControlStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class StateManipulationStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class TokenAmountStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class AddressStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class TimestampStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class ArrayStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class SlippageStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class GasLimitStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class MerkleProofStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class SignatureStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class PricingStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export declare class RoundingStrategy extends BaseFuzzingStrategy {
    name: string;
    description: string;
    priority: number;
    generateParameters(abiFunction: any): any[][];
}
export {};
//# sourceMappingURL=strategies.d.ts.map