"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ALL_STRATEGIES = exports.MicroDustStrategy = exports.RoundingStrategy = exports.PricingStrategy = exports.SignatureStrategy = exports.MerkleProofStrategy = exports.GasLimitStrategy = exports.SlippageStrategy = exports.ArrayStrategy = exports.TimestampStrategy = exports.AddressStrategy = exports.TokenAmountStrategy = exports.StateManipulationStrategy = exports.AccessControlStrategy = exports.ReentrancyStrategy = exports.OverflowUnderflowStrategy = exports.BoundaryValueStrategy = void 0;
const ethers_1 = require("ethers");
// Base strategy class
class BaseFuzzingStrategy {
    getParameterTypes(abiFunction) {
        return (abiFunction.inputs || []).map((input) => input.type);
    }
    generateAddress(type) {
        switch (type) {
            case 'zero':
                return ethers_1.ethers.ZeroAddress;
            case 'random':
                return ethers_1.ethers.Wallet.createRandom().address;
            case 'max':
                return '******************************************';
            case 'common':
                // Common addresses that might have special behavior
                const commonAddresses = [
                    '******************************************',
                    '******************************************',
                    '******************************************',
                    '******************************************'
                ];
                return commonAddresses[Math.floor(Math.random() * commonAddresses.length)];
            default:
                return ethers_1.ethers.ZeroAddress;
        }
    }
    generateUint(bits, type) {
        const maxValue = (2n ** BigInt(bits)) - 1n;
        switch (type) {
            case 'zero':
                return 0n;
            case 'one':
                return 1n;
            case 'max':
                return maxValue;
            case 'boundary':
                // Common boundary values
                const boundaries = [0n, 1n, maxValue, maxValue - 1n, 2n ** 255n - 1n];
                return boundaries[Math.floor(Math.random() * boundaries.length)];
            case 'random':
                return BigInt(Math.floor(Math.random() * Number(maxValue > 2n ** 32n ? 2n ** 32n : maxValue)));
            default:
                return 0n;
        }
    }
}
// Boundary value testing strategy
class BoundaryValueStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'BoundaryValueStrategy';
        this.description = 'Tests boundary values like 0, 1, max values';
        this.priority = 1;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        const parameterSets = [];
        if (paramTypes.length === 0) {
            return [[]];
        }
        // Generate boundary value combinations
        const boundaryTypes = ['zero', 'one', 'max', 'boundary'];
        for (const boundaryType of boundaryTypes) {
            const params = paramTypes.map(type => {
                if (type === 'address') {
                    return this.generateAddress(boundaryType);
                }
                else if (type.startsWith('uint')) {
                    const bits = type === 'uint' ? 256 : parseInt(type.slice(4));
                    return this.generateUint(bits, boundaryType);
                }
                else if (type === 'bool') {
                    return boundaryType === 'zero' ? false : true;
                }
                else if (type === 'string') {
                    return boundaryType === 'zero' ? '' : 'test';
                }
                else if (type.startsWith('bytes')) {
                    return boundaryType === 'zero' ? '0x' : '0x1234';
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.BoundaryValueStrategy = BoundaryValueStrategy;
// Overflow/Underflow testing strategy
class OverflowUnderflowStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'OverflowUnderflowStrategy';
        this.description = 'Tests for integer overflow and underflow vulnerabilities';
        this.priority = 1;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        const parameterSets = [];
        if (paramTypes.length === 0) {
            return [[]];
        }
        // Generate overflow/underflow test cases
        const overflowValues = [
            { type: 'max', description: 'Maximum value' },
            { type: 'max_minus_one', description: 'Maximum value - 1' },
            { type: 'near_max', description: 'Near maximum value' },
            { type: 'overflow', description: 'Overflow attempt' }
        ];
        for (const valueType of overflowValues) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    const bits = type === 'uint' ? 256 : parseInt(type.slice(4));
                    const maxValue = (2n ** BigInt(bits)) - 1n;
                    switch (valueType.type) {
                        case 'max':
                            return maxValue;
                        case 'max_minus_one':
                            return maxValue - 1n;
                        case 'near_max':
                            return maxValue - BigInt(Math.floor(Math.random() * 100));
                        case 'overflow':
                            return maxValue + 1n; // This should fail, but might reveal issues
                        default:
                            return 0n;
                    }
                }
                else if (type === 'address') {
                    return this.generateAddress('zero');
                }
                else if (type === 'bool') {
                    return false;
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.OverflowUnderflowStrategy = OverflowUnderflowStrategy;
// Reentrancy testing strategy
class ReentrancyStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'ReentrancyStrategy';
        this.description = 'Tests for reentrancy vulnerabilities';
        this.priority = 1;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        // Generate parameters that might trigger reentrancy
        const parameterSets = [];
        // Test with attacker-controlled addresses
        const attackerAddresses = [
            '******************************************',
            '******************************************',
            '******************************************'
        ];
        for (const attackerAddress of attackerAddresses) {
            const params = paramTypes.map(type => {
                if (type === 'address') {
                    return attackerAddress;
                }
                else if (type.startsWith('uint')) {
                    // Small amounts for reentrancy testing
                    return BigInt(Math.floor(Math.random() * 1000) + 1);
                }
                else if (type === 'bool') {
                    return true;
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.ReentrancyStrategy = ReentrancyStrategy;
// Access control testing strategy
class AccessControlStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'AccessControlStrategy';
        this.description = 'Tests for access control vulnerabilities';
        this.priority = 1;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        // Generate parameters to test access control
        const parameterSets = [];
        // 🏺 DUST COLLECTION: Enhanced addresses for access control bypass
        const testAddresses = [
            ethers_1.ethers.ZeroAddress,
            '******************************************',
            '******************************************',
            '******************************************',
            '******************************************',
            '******************************************',
            // 🏺 DUST COLLECTION: Common protocol addresses that might have special access
            '******************************************', // WETH
            '******************************************', // Common multisig pattern
            '******************************************', // Uniswap V2 Router
            '******************************************', // Uniswap V3 Router
            '******************************************', // UNI Token
            '******************************************', // DAI
            '******************************************', // USDT
            // 🏺 DUST COLLECTION: Burn addresses that might unlock value
            '******************************************',
            '******************************************',
            '******************************************'
        ];
        for (const testAddress of testAddresses) {
            const params = paramTypes.map(type => {
                if (type === 'address') {
                    return testAddress;
                }
                else if (type.startsWith('uint')) {
                    return 1n; // Small amounts for access control testing
                }
                else if (type === 'bool') {
                    return true;
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.AccessControlStrategy = AccessControlStrategy;
// State manipulation strategy
class StateManipulationStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'StateManipulationStrategy';
        this.description = 'Tests state manipulation vulnerabilities';
        this.priority = 2;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Generate parameters that might manipulate state unexpectedly
        const stateValues = [0, 1, 2, 10, 100, 1000];
        for (const value of stateValues) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    return BigInt(value);
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                else if (type === 'bool') {
                    return value % 2 === 0;
                }
                return value;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.StateManipulationStrategy = StateManipulationStrategy;
// Token amount strategy
class TokenAmountStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'TokenAmountStrategy';
        this.description = 'Tests various token amounts including dust and large amounts';
        this.priority = 2;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Common token amounts (considering 18 decimals)
        const tokenAmounts = [
            1n, // 1 wei
            1000n, // 1000 wei
            ethers_1.ethers.parseEther('0.001'), // 0.001 ETH
            ethers_1.ethers.parseEther('1'), // 1 ETH
            ethers_1.ethers.parseEther('100'), // 100 ETH
            ethers_1.ethers.parseEther('10000'), // 10,000 ETH
            ethers_1.ethers.parseEther('1000000'), // 1M ETH
        ];
        for (const amount of tokenAmounts) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    return amount;
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                else if (type === 'bool') {
                    return true;
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.TokenAmountStrategy = TokenAmountStrategy;
// Address strategy for testing different address types
class AddressStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'AddressStrategy';
        this.description = 'Tests various address types and patterns';
        this.priority = 2;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Various address types to test
        const addressTypes = ['zero', 'random', 'max', 'common'];
        for (const addressType of addressTypes) {
            const params = paramTypes.map(type => {
                if (type === 'address') {
                    return this.generateAddress(addressType);
                }
                else if (type.startsWith('uint')) {
                    return 1n;
                }
                else if (type === 'bool') {
                    return true;
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.AddressStrategy = AddressStrategy;
// Timestamp strategy
class TimestampStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'TimestampStrategy';
        this.description = 'Tests timestamp-related vulnerabilities';
        this.priority = 3;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Timestamp values to test
        const now = BigInt(Math.floor(Date.now() / 1000));
        const timestampValues = [
            0n, // Unix epoch
            1n, // Near epoch
            now - 86400n, // 1 day ago
            now, // Current time
            now + 86400n, // 1 day from now
            now + 31536000n, // 1 year from now
            2n ** 32n - 1n, // Max 32-bit timestamp
        ];
        for (const timestamp of timestampValues) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    return timestamp;
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                else if (type === 'bool') {
                    return true;
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.TimestampStrategy = TimestampStrategy;
// Array strategy
class ArrayStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'ArrayStrategy';
        this.description = 'Tests array-related vulnerabilities';
        this.priority = 3;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Array sizes to test
        const arraySizes = [0, 1, 2, 10, 100, 1000];
        for (const size of arraySizes) {
            const params = paramTypes.map(type => {
                if (type.endsWith('[]')) {
                    const baseType = type.slice(0, -2);
                    const array = [];
                    for (let i = 0; i < size; i++) {
                        if (baseType === 'address') {
                            array.push(this.generateAddress('random'));
                        }
                        else if (baseType.startsWith('uint')) {
                            array.push(BigInt(i));
                        }
                        else {
                            array.push(i);
                        }
                    }
                    return array;
                }
                else if (type.startsWith('uint')) {
                    return BigInt(size);
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.ArrayStrategy = ArrayStrategy;
// Additional specialized strategies
class SlippageStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'SlippageStrategy';
        this.description = 'Tests slippage-related vulnerabilities in DEX functions';
        this.priority = 3;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Slippage percentages (basis points)
        const slippageValues = [0n, 1n, 50n, 100n, 500n, 1000n, 5000n, 10000n];
        for (const slippage of slippageValues) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    return slippage;
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.SlippageStrategy = SlippageStrategy;
class GasLimitStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'GasLimitStrategy';
        this.description = 'Tests gas limit related issues';
        this.priority = 3;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Gas amounts to test
        const gasValues = [1n, 21000n, 100000n, 1000000n, 8000000n];
        for (const gasValue of gasValues) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    return gasValue;
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.GasLimitStrategy = GasLimitStrategy;
class MerkleProofStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'MerkleProofStrategy';
        this.description = 'Tests Merkle proof vulnerabilities';
        this.priority = 3;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Mock Merkle proof data
        const mockProofs = [
            [],
            ['0x1234567890123456789012345678901234567890123456789012345678901234'],
            ['******************************************111111111111111111111111', '******************************************222222222222222222222222']
        ];
        for (const proof of mockProofs) {
            const params = paramTypes.map(type => {
                if (type === 'bytes32[]') {
                    return proof;
                }
                else if (type === 'bytes32') {
                    return proof[0] || '******************************************000000000000000000000000';
                }
                else if (type.startsWith('uint')) {
                    return 1n;
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.MerkleProofStrategy = MerkleProofStrategy;
class SignatureStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'SignatureStrategy';
        this.description = 'Tests signature-related vulnerabilities';
        this.priority = 3;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Mock signature components
        const mockSignatures = [
            { v: 27, r: '******************************************111111111111111111111111', s: '******************************************222222222222222222222222' },
            { v: 28, r: '0x3333333333333333333333333333333333333333333333333333333333333333', s: '0x4444444444444444444444444444444444444444444444444444444444444444' }
        ];
        for (const sig of mockSignatures) {
            const params = paramTypes.map(type => {
                if (type === 'uint8' || type === 'uint256') {
                    return BigInt(sig.v);
                }
                else if (type === 'bytes32') {
                    return sig.r;
                }
                else if (type === 'bytes') {
                    return sig.r + sig.s.slice(2);
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.SignatureStrategy = SignatureStrategy;
class PricingStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'PricingStrategy';
        this.description = 'Tests pricing oracle vulnerabilities';
        this.priority = 3;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // Price values to test (considering different decimal places)
        const priceValues = [
            0n, // Zero price
            1n, // Minimum price
            ethers_1.ethers.parseEther('0.01'), // Low price
            ethers_1.ethers.parseEther('1'), // Normal price
            ethers_1.ethers.parseEther('1000'), // High price
            ethers_1.ethers.parseEther('1000000'), // Extreme price
        ];
        for (const price of priceValues) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    return price;
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.PricingStrategy = PricingStrategy;
class RoundingStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'RoundingStrategy';
        this.description = 'Tests rounding vulnerabilities';
        this.priority = 3;
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // 🏺 DUST COLLECTION: Micro-dust specific values for maximum extraction
        const microDustValues = [
            // Wei-level dust (1-1000 wei)
            1n, 2n, 3n, 5n, 10n, 50n, 100n, 500n, 1000n,
            // Micro-ETH amounts (0.000001-0.001 ETH in wei)
            BigInt('1000000000000'), // 0.000001 ETH
            BigInt('10000000000000'), // 0.00001 ETH
            BigInt('100000000000000'), // 0.0001 ETH
            BigInt('1000000000000000'), // 0.001 ETH
            // Fractional amounts that might bypass checks
            BigInt('1337'), // Leet number
            BigInt('4269'), // Random small number
            BigInt('31337'), // Larger leet
            BigInt('123456'), // Sequential
            // Boundary values for rounding exploits
            BigInt('999999999999999'), // Just under 0.001 ETH
            BigInt('1000000000000001'), // Just over 0.001 ETH
            // Original rounding values (kept for compatibility)
            9n, 99n, 999n, 9999n, 99999n
        ];
        for (const value of microDustValues) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    return value;
                }
                else if (type === 'address') {
                    return this.generateAddress('random');
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.RoundingStrategy = RoundingStrategy;
// 🏺 DUST COLLECTION: Micro-dust specific strategy
class MicroDustStrategy extends BaseFuzzingStrategy {
    constructor() {
        super(...arguments);
        this.name = 'MicroDustStrategy';
        this.description = 'Tests micro-dust amounts and wei-level extractions';
        this.priority = 1; // Highest priority for dust collection
    }
    generateParameters(abiFunction) {
        const paramTypes = this.getParameterTypes(abiFunction);
        if (paramTypes.length === 0) {
            return [[]];
        }
        const parameterSets = [];
        // 🏺 DUST COLLECTION: Ultra-specific micro amounts
        const microAmounts = [
            // Single wei amounts
            1n, 2n, 3n, 4n, 5n, 6n, 7n, 8n, 9n,
            // Powers of 10 in wei
            10n, 100n, 1000n, 10000n, 100000n,
            // Common dust amounts from failed transactions
            BigInt('21000'), // Base gas cost
            BigInt('42000'), // 2x base gas
            BigInt('63000'), // 3x base gas
            // Micro-ETH in wei (dust from rounding errors)
            BigInt('1000000000'), // 1 gwei
            BigInt('10000000000'), // 10 gwei
            BigInt('100000000000'), // 100 gwei
            BigInt('1000000000000'), // 0.000001 ETH
            // Token dust amounts (18 decimal tokens)
            BigInt('1000000000000000'), // 0.001 token
            BigInt('10000000000000000'), // 0.01 token
            BigInt('100000000000000000'), // 0.1 token
            // Specific dust patterns from DeFi protocols
            BigInt('1337000000000000'), // 0.001337 ETH (leet dust)
            BigInt('420000000000000'), // 0.00042 ETH
            BigInt('69000000000000'), // 0.000069 ETH
        ];
        for (const amount of microAmounts) {
            const params = paramTypes.map(type => {
                if (type.startsWith('uint')) {
                    return amount;
                }
                else if (type === 'address') {
                    // Use zero address for micro-dust (often bypasses checks)
                    return ethers_1.ethers.ZeroAddress;
                }
                else if (type === 'bool') {
                    return true; // Try to enable whatever the function does
                }
                return 0;
            });
            parameterSets.push(params);
        }
        return parameterSets;
    }
}
exports.MicroDustStrategy = MicroDustStrategy;
// 🏺 DUST COLLECTION: Export all strategies with MicroDustStrategy prioritized
exports.ALL_STRATEGIES = [
    new MicroDustStrategy(), // 🏺 DUST COLLECTION: Highest priority for micro-dust
    new BoundaryValueStrategy(),
    new OverflowUnderflowStrategy(),
    new ReentrancyStrategy(),
    new AccessControlStrategy(),
    new StateManipulationStrategy(),
    new TokenAmountStrategy(),
    new AddressStrategy(),
    new TimestampStrategy(),
    new ArrayStrategy(),
    new SlippageStrategy(),
    new GasLimitStrategy(),
    new MerkleProofStrategy(),
    new SignatureStrategy(),
    new PricingStrategy(),
    new RoundingStrategy()
];
//# sourceMappingURL=strategies.js.map