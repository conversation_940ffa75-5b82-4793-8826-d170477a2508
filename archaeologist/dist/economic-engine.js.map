{"version": 3, "file": "economic-engine.js", "sourceRoot": "", "sources": ["../src/economic-engine.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,kDAA0B;AAC1B,mCAAgC;AAChC,qCAAkC;AAClC,qCAAkC;AA2ClC,MAAM,cAAc;IAApB;QACU,kBAAa,GAAG,IAAI,GAAG,EAAwB,CAAC;QACvC,mBAAc,GAAG,KAAK,CAAC,CAAC,aAAa;QACrC,qBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAChE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,yCAAyC;QAC1E,6BAAwB,GAAG,GAAG,CAAC,CAAC,6BAA6B;IAiVhF,CAAC;IA/UC;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,OAAe,EACf,cAAsB,EACtB,YAAoB,EACpB,cAAwB,EAAE;QAG1B,eAAM,CAAC,KAAK,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;QAE1D,2BAA2B;QAC3B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAEtD,kBAAkB;QAClB,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC;QACrD,MAAM,YAAY,GAAG,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,wBAAwB,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAC9F,MAAM,SAAS,GAAG,OAAO,GAAG,YAAY,CAAC;QAEzC,0BAA0B;QAC1B,MAAM,SAAS,GAAG,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/E,MAAM,YAAY,GAAG,cAAc,GAAG,EAAE,CAAC,CAAC;YACxC,MAAM,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,2EAA2E;QAC3E,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,qBAAqB;QACjG,MAAM,cAAc,GAAG,SAAS,IAAI,SAAS,GAAG,MAAM,CAAC,mBAAmB,CAAC,GAAG,KAAK,CAAC;QAEpF,kBAAkB;QAClB,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CACpC,cAAc,EACd,SAAS,EACT,SAAS,EACT,WAAW,EACX,OAAO,CACR,CAAC;QAEF,MAAM,QAAQ,GAAqB;YACjC,OAAO;YACP,QAAQ,EAAE;gBACR,OAAO,EAAE,YAAY,CAAC,QAAQ;gBAC9B,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,MAAM,EAAE,YAAY,CAAC,MAAM;gBAC3B,SAAS,EAAE,YAAY,CAAC,SAAS;aAClC;YACD,KAAK,EAAE;gBACL,YAAY;gBACZ,OAAO;gBACP,YAAY;gBACZ,SAAS;aACV;YACD,aAAa,EAAE;gBACb,cAAc;gBACd,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,kBAAkB,EAAE,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,cAAc,CAAC,KAAK,CAAC;aACtF;YACD,IAAI,EAAE,cAAc;SACrB,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,OAAe;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAClE,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,6CAA6C,OAAO,EAAE,CAAC,CAAC;QAErE,sCAAsC;QACtC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAEnE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC9C,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CAAC,OAAe;QACrD,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC1C,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YAChC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;SACtC,CAAC;QAEF,IAAI,SAAS,GAAiB,IAAI,CAAC;QAEnC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,MAAM,EAAE,CAAC;gBAC5B,IAAI,IAAI,EAAE,CAAC;oBACT,eAAM,CAAC,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;oBACzD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAC;gBAC3B,eAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,eAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,OAAe;QAClD,IAAI,OAAO,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC,CAAC,4BAA4B;QAE5D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,+CAA+C,EAAE;gBAChF,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE3B,OAAO;gBACL,OAAO;gBACP,QAAQ,EAAE,eAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;gBAChE,IAAI,EAAE,eAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;gBAC/D,QAAQ,EAAE,eAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;gBACnE,IAAI,EAAE,eAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;gBAC/D,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,OAAe;QACxC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,eAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;YAC7D,IAAI,CAAC,KAAK,EAAE,MAAM;gBAAE,OAAO,IAAI,CAAC;YAEhC,MAAM,QAAQ,GAAG,IAAI,eAAM,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;YAE5C,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAAE,OAAO,IAAI,CAAC;YAEnC,yDAAyD;YACzD,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;YAEnC,OAAO;gBACL,OAAO;gBACP,QAAQ,EAAE,SAAS;gBACnB,IAAI,EAAE,SAAS,GAAG,IAAI,GAAG,IAAI,EAAE,aAAa;gBAC5C,QAAQ,EAAE,SAAS;gBACnB,IAAI,EAAE,SAAS,GAAG,GAAG,GAAG,IAAI,EAAE,YAAY;gBAC1C,MAAM,EAAE,cAAc;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC7C,IAAI,CAAC;YACH,MAAM,UAAU,GAA8B;gBAC5C,CAAC,EAAE,KAAK;gBACR,GAAG,EAAE,MAAM;gBACX,KAAK,EAAE,KAAK;gBACZ,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,CAAC,OAAO;gBAAE,OAAO,IAAI,CAAC;YAE1B,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,gCAAgC,OAAO,MAAM,EAAE;gBAC9E,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE3B,OAAO;gBACL,OAAO;gBACP,QAAQ,EAAE,eAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,EAAE,WAAW;gBACpF,IAAI,EAAE,eAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;gBACnE,QAAQ,EAAE,eAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;gBACvE,IAAI,EAAE,eAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;gBACnE,MAAM,EAAE,UAAU;gBAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAe;QAC1C,MAAM,cAAc,GAA8B;YAChD,CAAC,EAAE,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,WAAW;YAC/C,GAAG,EAAE,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU;YAChD,KAAK,EAAE,eAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,WAAW;YACpD,EAAE,EAAE,eAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,WAAW;YACnD,IAAI,EAAE,eAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO;SACjD,CAAC;QAEF,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,eAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAE7E,OAAO;YACL,OAAO;YACP,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,SAAS,GAAG,IAAI,GAAG,IAAI;YAC7B,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,SAAS,GAAG,GAAG,GAAG,IAAI;YAC5B,MAAM,EAAE,UAAU;YAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,UAAU,CAChB,cAAsB,EACtB,SAAiB,EACjB,SAAiB,EACjB,WAAqB,EACrB,OAAe;QAOf,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,mBAAmB;QACnB,IAAI,cAAc,GAAG,SAAS,EAAE,CAAC;YAC/B,SAAS,IAAI,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC7C,CAAC;QAED,qBAAqB;QACrB,MAAM,YAAY,GAAG,cAAc,GAAG,EAAE,CAAC,CAAC;YACxC,MAAM,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1D,IAAI,YAAY,GAAG,GAAG,EAAE,CAAC,CAAC,sBAAsB;YAC9C,SAAS,IAAI,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,sBAAsB;QACtB,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC,sBAAsB;YACzC,SAAS,IAAI,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACrC,CAAC;QAED,wBAAwB;QACxB,SAAS,IAAI,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;QACrC,OAAO,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAE7B,uBAAuB;QACvB,IAAI,KAA4C,CAAC;QACjD,IAAI,SAAS,IAAI,EAAE;YAAE,KAAK,GAAG,SAAS,CAAC;aAClC,IAAI,SAAS,IAAI,EAAE;YAAE,KAAK,GAAG,MAAM,CAAC;aACpC,IAAI,SAAS,IAAI,EAAE;YAAE,KAAK,GAAG,QAAQ,CAAC;;YACtC,KAAK,GAAG,KAAK,CAAC;QAEnB,OAAO;YACL,KAAK;YACL,OAAO;YACP,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC;YACxC,OAAO,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,SAAiB,EAAE,SAAiB;QACtE,MAAM,eAAe,GAAG;YACtB,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,GAAG;YACX,SAAS,EAAE,GAAG;SACf,CAAC;QAEF,MAAM,UAAU,GAAG,eAAe,CAAC,SAAyC,CAAC,IAAI,GAAG,CAAC;QACrF,OAAO,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAA0B;QACpD,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC;QAEhD,eAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACtC,eAAM,CAAC,KAAK,CAAC,iBAAiB,eAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,UAAU,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1H,eAAM,CAAC,KAAK,CAAC,kBAAkB,eAAM,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1E,eAAM,CAAC,KAAK,CAAC,uBAAuB,eAAM,CAAC,WAAW,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC5F,eAAM,CAAC,KAAK,CAAC,kBAAkB,eAAM,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAClF,eAAM,CAAC,KAAK,CAAC,qBAAqB,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,eAAe,CAAC,CAAC;QAE9E,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;YACjC,eAAM,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;QACpE,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,eAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;IAChD,CAAC;CACF;AAEY,QAAA,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC"}