"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.evaluator = exports.Evaluator = void 0;
const logger_1 = require("./logger");
const database_1 = require("./database");
// 🏺 SURGICAL PRECISION INTEGRATION
const economic_engine_1 = require("./economic-engine");
const self_monitor_1 = require("./self-monitor");
class Evaluator {
    async evaluateSimulations(simulations) {
        const exploits = [];
        logger_1.logger.relic(`🔍 SURGICAL EVALUATION: Analyzing ${simulations.length} simulation results`);
        for (const result of simulations) {
            if (result.success) {
                // 🏺 SURGICAL PRECISION: Use economic engine for accurate profitability assessment
                const isViable = await this.isProfitableSurgical(result);
                if (isViable) {
                    // Calculate priority based on surgical precision metrics
                    const priority = this.calculateSurgicalPriority(result);
                    const exploit = {
                        address: result.contractAddress,
                        functionName: result.functionName,
                        signature: result.signature,
                        estimatedValue: result.potentialValue || '0',
                        gasEstimate: result.gasEstimate,
                        priority,
                        chainId: 1, // TODO: Extract from simulation context
                        discovered: new Date(),
                        executed: false
                    };
                    exploits.push(exploit);
                    await database_1.db.saveExploitableContract(exploit);
                    logger_1.logger.extract(`🎯 VIABLE TARGET: ${result.contractAddress} - ${result.functionName} (Priority: ${priority})`);
                    if (result.confidence) {
                        logger_1.logger.extract(`   Confidence: ${result.confidence}%`);
                    }
                    if (result.extractionPotential) {
                        logger_1.logger.extract(`   Extraction Potential: ${result.extractionPotential}%`);
                    }
                }
                else {
                    // 🏺 DISCOVERY MODE: Show what we're finding but rejecting
                    if (process.env.DISCOVERY_MODE === 'true') {
                        logger_1.logger.relic(`🔍 DISCOVERY: Found target but not profitable: ${result.contractAddress} - ${result.functionName}`);
                        if (result.potentialValue) {
                            logger_1.logger.relic(`   Potential Value: ${result.potentialValue} ETH`);
                        }
                        if (result.gasEstimate) {
                            logger_1.logger.relic(`   Gas Estimate: ${result.gasEstimate}`);
                        }
                    }
                    else {
                        logger_1.logger.mark(`❌ Not profitable: ${result.contractAddress} - ${result.functionName}`);
                    }
                }
            }
            else {
                logger_1.logger.ghost(`⚪ Simulation failed: ${result.contractAddress} - ${result.functionName}`);
            }
        }
        if (exploits.length > 0) {
            logger_1.logger.extract(`🏺 SURGICAL EVALUATION COMPLETE: ${exploits.length} viable targets identified`);
        }
        else {
            logger_1.logger.ghost(`🔍 No viable targets found in current batch`);
        }
        return exploits;
    }
    isProfitable(simulation) {
        // ARCHAEOLOGIST MODE: Proper profitability calculation
        if (!simulation.potentialValue || simulation.gasEstimate <= 0) {
            return false;
        }
        const potentialValue = parseFloat(simulation.potentialValue);
        // Realistic gas cost calculation (20 gwei gas price)
        const gasPrice = 20e9; // 20 gwei in wei
        const gasCostWei = simulation.gasEstimate * gasPrice;
        const gasCostEth = gasCostWei / 1e18; // Convert to ETH
        // ARCHAEOLOGIST THRESHOLD: Profit must be at least 2x gas cost
        const profitThreshold = gasCostEth * 2;
        logger_1.logger.debug(`Profitability check: Value=${potentialValue} ETH, Gas=${gasCostEth} ETH, Threshold=${profitThreshold} ETH`);
        return potentialValue > profitThreshold;
    }
    /**
     * 🏺 SURGICAL PRECISION: Advanced profitability assessment using economic engine
     */
    async isProfitableSurgical(simulation) {
        try {
            if (!simulation.potentialValue || simulation.gasEstimate <= 0) {
                return false;
            }
            const estimatedValue = BigInt(Math.floor(parseFloat(simulation.potentialValue) * 1e18)); // Convert to wei
            const estimatedGas = BigInt(simulation.gasEstimate);
            // Build risk factors array
            const riskFactors = [];
            if (simulation.confidence && simulation.confidence < 80) {
                riskFactors.push('Low simulation confidence');
            }
            if (simulation.extractionPotential && simulation.extractionPotential < 50) {
                riskFactors.push('Low extraction potential');
            }
            // Use economic engine for precise analysis
            const economicAnalysis = await economic_engine_1.economicEngine.analyzeEconomics(1, // TODO: Get actual chain ID
            estimatedValue, estimatedGas, riskFactors);
            // Record analysis for monitoring
            self_monitor_1.selfMonitor.recordScan(economicAnalysis.profitability.meetsThreshold, 0);
            if (economicAnalysis.profitability.meetsThreshold) {
                logger_1.logger.extract(`💰 PROFITABLE: ${simulation.contractAddress} - Net profit: ${economicAnalysis.profitability.netProfit.toString()} wei`);
                logger_1.logger.extract(`   Risk Level: ${economicAnalysis.risk.level} (${economicAnalysis.risk.confidence}% confidence)`);
            }
            return economicAnalysis.profitability.meetsThreshold;
        }
        catch (error) {
            logger_1.logger.error(`Economic analysis failed for ${simulation.contractAddress}: ${error}`);
            return false;
        }
    }
    /**
     * 🏺 SURGICAL PRECISION: Calculate priority based on multiple factors
     */
    calculateSurgicalPriority(simulation) {
        let priority = 5; // Base priority (1 = highest, 5 = lowest)
        // Confidence factor
        if (simulation.confidence) {
            if (simulation.confidence >= 90)
                priority -= 2;
            else if (simulation.confidence >= 80)
                priority -= 1;
            else if (simulation.confidence < 60)
                priority += 1;
        }
        // Extraction potential factor
        if (simulation.extractionPotential) {
            if (simulation.extractionPotential >= 80)
                priority -= 1;
            else if (simulation.extractionPotential < 40)
                priority += 1;
        }
        // Function type priority (based on archaeologist targeting)
        const highPriorityFunctions = ['claim', 'withdraw', 'exit', 'emergencyWithdraw'];
        const functionName = simulation.functionName.toLowerCase();
        if (highPriorityFunctions.some(hpf => functionName.includes(hpf))) {
            priority -= 1;
        }
        // Ensure priority stays within bounds
        return Math.max(1, Math.min(5, priority));
    }
    evaluateBatch(simulationResults) {
        return simulationResults
            .filter(this.isProfitable)
            .map(result => this.createExploit(result));
    }
    createExploit(result) {
        const potentialValue = parseFloat(result.potentialValue || '0');
        const newExploit = {
            address: result.contractAddress,
            functionName: result.functionName,
            signature: result.signature,
            estimatedValue: result.potentialValue || '0',
            gasEstimate: result.gasEstimate,
            priority: 1, // Default priority
            chainId: 1, // Default, replace with actual logic
            discovered: new Date(),
            executed: false
        };
        logger_1.logger.exploit(`Found exploitable contract: ${newExploit.address}, potential value: ${newExploit.estimatedValue}`);
        return newExploit;
    }
}
exports.Evaluator = Evaluator;
exports.evaluator = new Evaluator();
//# sourceMappingURL=evaluator.js.map