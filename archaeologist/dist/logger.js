"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.LogLevel = void 0;
const chalk_1 = __importDefault(require("chalk"));
const config_1 = require("./config");
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor() {
        this.level = this.parseLogLevel(config_1.CONFIG.logLevel);
    }
    parseLogLevel(level) {
        switch (level.toLowerCase()) {
            case 'error': return LogLevel.ERROR;
            case 'warn': return LogLevel.WARN;
            case 'info': return LogLevel.INFO;
            case 'debug': return LogLevel.DEBUG;
            default: return LogLevel.INFO;
        }
    }
    timestamp() {
        return new Date().toISOString();
    }
    error(message, ...args) {
        if (this.level >= LogLevel.ERROR) {
            console.error(chalk_1.default.red(`[${this.timestamp()}] ERROR: ${message}`), ...args);
        }
    }
    warn(message, ...args) {
        if (this.level >= LogLevel.WARN) {
            console.warn(chalk_1.default.yellow(`[${this.timestamp()}] WARN: ${message}`), ...args);
        }
    }
    info(message, ...args) {
        if (this.level >= LogLevel.INFO) {
            console.info(chalk_1.default.blue(`[${this.timestamp()}] INFO: ${message}`), ...args);
        }
    }
    debug(message, ...args) {
        if (this.level >= LogLevel.DEBUG) {
            console.debug(chalk_1.default.gray(`[${this.timestamp()}] DEBUG: ${message}`), ...args);
        }
    }
    success(message, ...args) {
        if (this.level >= LogLevel.INFO) {
            console.info(chalk_1.default.green(`[${this.timestamp()}] SUCCESS: ${message}`), ...args);
        }
    }
    exploit(message, ...args) {
        if (this.level >= LogLevel.INFO) {
            console.info(chalk_1.default.magenta.bold(`[${this.timestamp()}] EXPLOIT: ${message}`), ...args);
        }
    }
    transaction(message, ...args) {
        if (this.level >= LogLevel.INFO) {
            console.info(chalk_1.default.cyan(`[${this.timestamp()}] TX: ${message}`), ...args);
        }
    }
    // ARCHAEOLOGIST MINDSET: Cold, surgical logging
    // Silent extraction - no celebration, just facts
    extract(message, ...args) {
        if (this.level >= LogLevel.INFO) {
            console.info(chalk_1.default.green(`[${this.timestamp()}] EXTRACT: ${message}`), ...args);
        }
    }
    // Mark targets for future exploitation
    mark(message, ...args) {
        if (this.level >= LogLevel.INFO) {
            console.info(chalk_1.default.yellow(`[${this.timestamp()}] MARK: ${message}`), ...args);
        }
    }
    // Relic analysis - dissecting dead contracts
    relic(message, ...args) {
        if (this.level >= LogLevel.DEBUG) {
            console.debug(chalk_1.default.cyan(`[${this.timestamp()}] RELIC: ${message}`), ...args);
        }
    }
    // Ghost mode - silent operations
    ghost(message, ...args) {
        if (this.level >= LogLevel.DEBUG) {
            console.debug(chalk_1.default.gray(`[${this.timestamp()}] GHOST: ${message}`), ...args);
        }
    }
    // Dust collection - small but guaranteed value
    dust(message, ...args) {
        if (this.level >= LogLevel.INFO) {
            console.info(chalk_1.default.dim.green(`[${this.timestamp()}] DUST: ${message}`), ...args);
        }
    }
}
exports.logger = new Logger();
//# sourceMappingURL=logger.js.map