import { contractFetcher } from './fetcher';
import { contractFilter } from './filter';
import { contractSimulator } from './simulator';
import { evaluator } from './evaluator';
import { executor } from './executor';
import { db } from './database';
import { logger } from './logger';
import { CONFIG } from './config';
import { ChainConfig } from './types';
import { performanceMonitor } from './metrics';
import { bytecodeDecompiler } from './decompiler';
import { ContractInfo } from './types';
import ora from 'ora';

export class Scanner {
  private isRunning: boolean = false;

  // Validate production configuration
  private validateConfiguration(): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required environment variables
    if (!process.env.ETHERSCAN_API_KEY) {
      errors.push('ETHERSCAN_API_KEY is required for Ethereum mainnet');
    }

    // Check if at least one chain has proper configuration
    const validChains = CONFIG.chains.filter(chain => chain.rpcUrl && chain.etherscanApiKey);
    if (validChains.length === 0) {
      errors.push('At least one chain must have both RPC_URL and API_KEY configured');
      errors.push('Example: ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_KEY');
    }

    // Warn about chains that will be skipped
    const invalidChains = CONFIG.chains.filter(chain => !chain.rpcUrl || !chain.etherscanApiKey);
    if (invalidChains.length > 0) {
      warnings.push(`The following chains will be skipped due to missing configuration:`);
      invalidChains.forEach(chain => {
        const missing = [];
        if (!chain.rpcUrl) missing.push('RPC_URL');
        if (!chain.etherscanApiKey) missing.push('API_KEY');
        warnings.push(`  - ${chain.name}: Missing ${missing.join(', ')}`);
      });
    }

    // Check private key for execution
    if (!process.env.PRIVATE_KEY) {
      errors.push('PRIVATE_KEY is required for transaction execution');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  async scanChain(chain: ChainConfig): Promise<void> {
    const scanId = `scan_${chain.chainId}_${Date.now()}`;
    performanceMonitor.startTiming(scanId, 'scan', { chain: chain.name });
    performanceMonitor.incrementCounter('total_scans');

    const spinner = ora(`Scanning ${chain.name} (${chain.chainId})...`).start();

    try {
      // Step 1: Fetch recent verified contracts
      spinner.text = `Fetching verified contracts from ${chain.name}...`;
      const fetchId = `fetch_${chain.chainId}_${Date.now()}`;
      performanceMonitor.startTiming(fetchId, 'fetch');

      const verifiedContracts = await contractFetcher.fetchRecentVerifiedContracts(chain, 1, CONFIG.scanBatchSize);
      performanceMonitor.endTiming(fetchId, true);

      // Step 1b: Fetch unverified contracts with bytecode decompilation
      spinner.text = `Fetching unverified contracts from ${chain.name}...`;
      const unverifiedContracts = await contractFetcher.fetchUnverifiedContracts(chain, Math.min(5, Math.floor(CONFIG.scanBatchSize / 2))) || [];

      // Combine verified and unverified contracts
      const contracts = [...verifiedContracts, ...(unverifiedContracts || [])];

      if (contracts.length === 0) {
        spinner.warn(`No contracts found on ${chain.name}`);
        performanceMonitor.incrementCounter('failed_scans');
        performanceMonitor.endTiming(scanId, false);
        return;
      }

      logger.info(`Found ${verifiedContracts.length} verified and ${unverifiedContracts.length} unverified contracts on ${chain.name}`);

      // Save contracts to database
      for (const contract of contracts) {
        await db.saveContract(contract);
      }

      // Step 2: Filter contracts with target functions
      spinner.text = `Filtering contracts on ${chain.name}...`;
      const filterId = `filter_${chain.chainId}_${Date.now()}`;
      performanceMonitor.startTiming(filterId, 'filter');

      const filteredResults = contractFilter.filterContracts(contracts);
      performanceMonitor.endTiming(filterId, true);

      if (filteredResults.length === 0) {
        spinner.warn(`No contracts with target functions found on ${chain.name}`);
        performanceMonitor.incrementCounter('failed_scans');
        performanceMonitor.endTiming(scanId, false);
        return;
      }

      logger.info(`Found ${filteredResults.length} contracts with target functions on ${chain.name}`);

      // Step 3: Simulate target functions
      spinner.text = `Simulating functions on ${chain.name}...`;
      const simId = `sim_${chain.chainId}_${Date.now()}`;
      performanceMonitor.startTiming(simId, 'simulation');
      performanceMonitor.incrementCounter('total_simulations', filteredResults.length);

      const simulationResults = await contractSimulator.batchSimulate(filteredResults);
      const successfulSims = simulationResults.filter(r => r.success).length;
      performanceMonitor.incrementCounter('successful_simulations', successfulSims);
      performanceMonitor.endTiming(simId, true);

      // Step 4: Evaluate simulation results
      spinner.text = `Evaluating results on ${chain.name}...`;
      const evalId = `eval_${chain.chainId}_${Date.now()}`;
      performanceMonitor.startTiming(evalId, 'evaluation');

      const exploitableContracts = evaluator.evaluateSimulations(simulationResults);
      performanceMonitor.endTiming(evalId, true);

      // Step 5: Execute exploits (if any found)
      if (exploitableContracts.length > 0) {
        logger.info(`🎯 Found ${exploitableContracts.length} potentially exploitable contracts on ${chain.name}!`);

        spinner.text = `Executing ${exploitableContracts.length} exploits on ${chain.name}...`;
        const execId = `exec_${chain.chainId}_${Date.now()}`;
        performanceMonitor.startTiming(execId, 'execution');
        performanceMonitor.incrementCounter('total_executions', exploitableContracts.length);

        const executionResults = await executor.batchExecuteExploits(exploitableContracts);

        const successfulExecutions = executionResults.filter(r => r.success);
        performanceMonitor.incrementCounter('successful_executions', successfulExecutions.length);
        performanceMonitor.endTiming(execId, successfulExecutions.length > 0);

        if (successfulExecutions.length > 0) {
          logger.info(`💰 Successfully executed ${successfulExecutions.length} exploits on ${chain.name}!`);
        }

        spinner.succeed(`Scan completed on ${chain.name}: ${successfulExecutions.length} successful exploits`);
      } else {
        spinner.succeed(`Scan completed on ${chain.name}: No exploitable contracts found`);
      }

      // Get current block number for tracking
      const provider = contractSimulator.getProvider(chain.chainId);
      let currentBlock = 0;
      if (provider) {
        try {
          currentBlock = await provider.getBlockNumber();
        } catch (error) {
          logger.debug(`Failed to get current block for ${chain.name}:`, error);
        }
      }

      // Update scan status with proper block tracking
      performanceMonitor.recordDatabaseOperation();
      await db.updateScanStatus({
        chainId: chain.chainId,
        lastProcessedBlock: currentBlock,
        lastScanTime: new Date(),
        contractsScanned: contracts.length,
        exploitsFound: exploitableContracts.length,
        totalValue: exploitableContracts.reduce((sum, e) => sum + parseFloat(e.estimatedValue || '0'), 0).toString()
      });

      performanceMonitor.incrementCounter('successful_scans');
      performanceMonitor.incrementCounter('chains_scanned');
      performanceMonitor.endTiming(scanId, true);

    } catch (error) {
      performanceMonitor.incrementCounter('failed_scans');
      performanceMonitor.endTiming(scanId, false);
      spinner.fail(`Scan failed on ${chain.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      logger.error(`Scan error on ${chain.name}:`, error);
    }
  }

  // Advanced scanning with block tracking to avoid rescanning
  async scanChainFromLastBlock(chain: ChainConfig): Promise<void> {
    const spinner = ora(`Scanning ${chain.name} from last processed block...`).start();
    
    try {
      // Get last scan status
      const scanStatus = await db.getScanStatus(chain.chainId);
      
      // Get current block number
      const provider = contractSimulator.getProvider(chain.chainId);
      if (!provider) {
        throw new Error(`No provider configured for ${chain.name}`);
      }
      
      const currentBlock = await provider.getBlockNumber();
      const lastProcessedBlock = scanStatus?.lastProcessedBlock || currentBlock - 1000;
      
      if (currentBlock <= lastProcessedBlock) {
        spinner.info(`${chain.name} is up to date (current: ${currentBlock}, last processed: ${lastProcessedBlock})`);
        return;
      }
      
      spinner.text = `Scanning ${chain.name} from block ${lastProcessedBlock + 1} to ${currentBlock}...`;
      
      // Fetch contracts from the unprocessed block range
      const contracts = await contractFetcher.fetchContractsByBlockRange(
        chain, 
        lastProcessedBlock + 1, 
        currentBlock
      );
      
      if (contracts.length === 0) {
        spinner.info(`No new contracts found in blocks ${lastProcessedBlock + 1} to ${currentBlock}`);
        
        // Update scan status even if no contracts found
        await db.updateScanStatus({
          chainId: chain.chainId,
          lastProcessedBlock: currentBlock,
          lastScanTime: new Date(),
          contractsScanned: 0,
          exploitsFound: 0,
          totalValue: '0'
        });
        return;
      }
      
      // Save contracts to database
      for (const contract of contracts) {
        await db.saveContract(contract);
      }
      
      // Process contracts as usual
      const filteredResults = contractFilter.filterContracts(contracts);
      
      if (filteredResults.length > 0) {
        spinner.text = `Processing ${filteredResults.length} contracts with target functions...`;
        
        const simulationResults = await contractSimulator.batchSimulate(filteredResults);
        const exploitableContracts = evaluator.evaluateSimulations(simulationResults);
        
        if (exploitableContracts.length > 0) {
          spinner.text = `Executing ${exploitableContracts.length} exploits...`;
          const executionResults = await executor.batchExecuteExploits(exploitableContracts);
          
          const successfulExecutions = executionResults.filter(r => r.success);
          spinner.succeed(`Scan completed: ${successfulExecutions.length} successful exploits from ${contracts.length} new contracts`);
        } else {
          spinner.succeed(`Scan completed: No exploitable contracts found from ${contracts.length} new contracts`);
        }
        
        // Update scan status
        await db.updateScanStatus({
          chainId: chain.chainId,
          lastProcessedBlock: currentBlock,
          lastScanTime: new Date(),
          contractsScanned: contracts.length,
          exploitsFound: exploitableContracts.length,
          totalValue: exploitableContracts.reduce((sum, e) => sum + parseFloat(e.estimatedValue || '0'), 0).toString()
        });
      } else {
        spinner.succeed(`Scan completed: No contracts with target functions found from ${contracts.length} new contracts`);
        
        // Update scan status
        await db.updateScanStatus({
          chainId: chain.chainId,
          lastProcessedBlock: currentBlock,
          lastScanTime: new Date(),
          contractsScanned: contracts.length,
          exploitsFound: 0,
          totalValue: '0'
        });
      }
      
    } catch (error) {
      spinner.fail(`Scan failed on ${chain.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      logger.error(`Scan error on ${chain.name}:`, error);
    }
  }

  async scanAllChains(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Scanner is already running');
      return;
    }

    // Validate configuration first
    const validation = this.validateConfiguration();
    if (!validation.valid) {
      logger.error('❌ Configuration validation failed:');
      validation.errors.forEach(error => logger.error(`  - ${error}`));
      logger.info('');
      logger.info('📋 Required environment variables:');
      logger.info('  - ETHERSCAN_API_KEY=your_etherscan_api_key');
      logger.info('  - ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_key');
      logger.info('  - PRIVATE_KEY=your_private_key_for_execution');
      logger.info('');
      logger.info('💡 For other chains, you may need separate API keys:');
      logger.info('  - ARBISCAN_API_KEY for Arbitrum');
      logger.info('  - BASESCAN_API_KEY for Base');
      logger.info('  - OPTIMISM_API_KEY for Optimism');
      logger.info('  - POLYGONSCAN_API_KEY for Polygon');
      return;
    }

    // Show warnings about skipped chains
    if (validation.warnings.length > 0) {
      logger.warn('⚠️  Configuration warnings:');
      validation.warnings.forEach(warning => logger.warn(`  ${warning}`));
      logger.info('');
    }

    this.isRunning = true;
    logger.info('✅ Configuration validated successfully');
    logger.info('Starting multi-chain scan...');

    // Get valid chains
    const validChains = CONFIG.chains.filter(chain => chain.rpcUrl && chain.etherscanApiKey);
    logger.info(`📡 Scanning ${validChains.length} chains: ${validChains.map(c => c.name).join(', ')}`);

    try {
      for (const chain of validChains) {
        await this.scanChain(chain);

        // Small delay between chains to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      logger.error('Multi-chain scan error:', error);
    } finally {
      this.isRunning = false;
      logger.info('Multi-chain scan completed');
    }
  }

  async scanAllChainsIncremental(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Scanner is already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting incremental multi-chain scan...');

    // Validate configuration
    const validChains = CONFIG.chains.filter(chain => chain.rpcUrl && chain.etherscanApiKey);

    if (validChains.length === 0) {
      logger.error('❌ No valid chain configurations found for incremental scan.');
      this.isRunning = false;
      return;
    }

    try {
      for (const chain of validChains) {
        await this.scanChainFromLastBlock(chain);

        // Small delay between chains to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      logger.error('Incremental multi-chain scan error:', error);
    } finally {
      this.isRunning = false;
      logger.info('Incremental multi-chain scan completed');
    }
  }

  async continuousScanning(intervalMinutes: number = 60): Promise<void> {
    logger.info(`Starting continuous scanning with ${intervalMinutes} minute intervals`);
    
    while (true) {
      await this.scanAllChains();
      
      logger.info(`Waiting ${intervalMinutes} minutes before next scan...`);
      await new Promise(resolve => setTimeout(resolve, intervalMinutes * 60 * 1000));
    }
  }

  async scanSpecificContract(address: string, chainId: number): Promise<void> {
    const chain = CONFIG.chains.find(c => c.chainId === chainId);
    if (!chain) {
      logger.error(`Chain ${chainId} not configured`);
      return;
    }

    const spinner = ora(`🏺 ARCHAEOLOGIST: Deep scanning ${address} on ${chain.name}...`).start();

    try {
      let contract: ContractInfo | null = null;

      // Step 1: Try to fetch as verified contract
      spinner.text = `Checking if ${address} is verified...`;
      contract = await contractFetcher.fetchContractDetails(chain, address);

      if (!contract) {
        // Step 2: If not verified, try bytecode decompilation
        spinner.text = `🔓 Contract not verified, attempting bytecode analysis...`;
        logger.info(`Contract ${address} not verified, attempting bytecode decompilation`);

        const decompilationResult = await bytecodeDecompiler.decompileContract(address, chainId);
        if (decompilationResult && decompilationResult.hasTargetFunctions) {
          // Convert decompilation result to ContractInfo with proper ABI
          const abi = decompilationResult.matchedTargetFunctions.map((targetFunc: any) => {
            // Find the decompiled function that matches this target
            const decompiledFunc = decompilationResult.functions.find(f =>
              f.selector === targetFunc.fourByteSignature
            );

            return {
              name: targetFunc.name, // Use target function name for consistency
              type: 'function',
              inputs: decompiledFunc?.inputs ? decompiledFunc.inputs.map((type: any) => ({ type })) : [],
              outputs: [],
              selector: targetFunc.fourByteSignature
            };
          });

          contract = {
            address: address,
            name: `Unverified_${address.slice(0, 8)}`,
            abi: abi,
            sourceCode: `// Decompiled contract - ${decompilationResult.functions.length} functions found`,
            compiler: 'decompiled',
            txHash: '0x0',
            blockNumber: 0,
            timestamp: Date.now(),
            chainId: chainId
          };

          logger.info(`🎯 Bytecode analysis found ${decompilationResult.matchedTargetFunctions.length} target functions in ${address}`);
        }
      }

      if (!contract) {
        spinner.fail(`Contract ${address} not found, not verified, and bytecode analysis failed`);
        return;
      }

      // Step 3: Filter contract for target functions
      spinner.text = `Analyzing target functions...`;
      const filterResults = contractFilter.filterContracts([contract]);
      if (filterResults.length === 0) {
        spinner.fail(`Contract ${address} has no target functions`);
        return;
      }

      logger.info(`Found ${filterResults[0].matchedFunctions.length} target functions: ${filterResults[0].matchedFunctions.map(f => f.name).join(', ')}`);

      // Step 4: Simulate functions
      spinner.text = `🎯 Simulating target functions...`;
      const simulationResults = await contractSimulator.simulateContract(filterResults[0]);

      // Step 5: Evaluate results
      const exploitableContracts = evaluator.evaluateSimulations(simulationResults);

      if (exploitableContracts.length > 0) {
        spinner.succeed(`💰 Contract ${address} is exploitable with ${exploitableContracts.length} potential exploits`);

        // Show details
        for (const exploit of exploitableContracts) {
          logger.exploit(`${exploit.functionName}: ${exploit.estimatedValue} potential value`);
        }
      } else {
        spinner.warn(`Contract ${address} has no profitable exploits (simulations may have failed or gas costs too high)`);

        // Show simulation details for debugging
        logger.info(`Simulation results: ${simulationResults.length} total, ${simulationResults.filter(r => r.success).length} successful`);
        for (const result of simulationResults) {
          logger.debug(`${result.functionName}: success=${result.success}, value=${result.potentialValue}, error=${result.error}`);
        }
      }

    } catch (error) {
      spinner.fail(`Error scanning contract ${address}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getStats(): Promise<void> {
    const stats = await db.getStats();
    const unexecutedExploits = await db.getUnexecutedExploits();
    
    logger.info('=== CONTRACT ARCHAEOLOGIST STATS ===');
    logger.info(`Total contracts scanned: ${stats.totalContracts}`);
    logger.info(`Successful simulations: ${stats.successfulSimulations}`);
    logger.info(`Exploitable contracts found: ${stats.exploitableContracts}`);
    logger.info(`Successful executions: ${stats.successfulExecutions}`);
    logger.info(`Total value extracted: ${stats.totalValueExtracted} ETH`);
    logger.info(`Pending exploits: ${unexecutedExploits.length}`);
    
    if (unexecutedExploits.length > 0) {
      logger.info('=== PENDING EXPLOITS ===');
      for (const exploit of unexecutedExploits.slice(0, 10)) {
        logger.info(`${exploit.address} - ${exploit.functionName} (${exploit.estimatedValue} value)`);
      }
    }
  }

  async executePendingExploits(): Promise<void> {
    const pendingExploits = await db.getUnexecutedExploits();
    
    if (pendingExploits.length === 0) {
      logger.info('No pending exploits to execute');
      return;
    }

    logger.info(`Executing ${pendingExploits.length} pending exploits...`);
    
    // Execute in batches to avoid overwhelming the network
    const batchSize = 5;
    for (let i = 0; i < pendingExploits.length; i += batchSize) {
      const batch = pendingExploits.slice(i, i + batchSize);
      const results = await executor.batchExecuteExploits(batch);
      
      const successful = results.filter(r => r.success);
      logger.info(`Batch ${Math.floor(i / batchSize) + 1}: ${successful.length}/${batch.length} successful`);
    }
  }

  async rescanFailedContracts(): Promise<void> {
    logger.info('Rescanning contracts with failed simulations...');
    
    // This would require additional database queries to find failed simulations
    // For now, we'll just log that this feature needs implementation
    logger.warn('Rescan feature not yet implemented');
  }

  stop(): void {
    this.isRunning = false;
    logger.info('Scanner stopped');
  }

  // Public getter for dashboard access
  getIsRunning(): boolean {
    return this.isRunning;
  }
}

export const scanner = new Scanner();

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'scan':
      scanner.scanAllChains();
      break;
    case 'incremental':
      scanner.scanAllChainsIncremental();
      break;
    case 'continuous':
      const interval = parseInt(args[1]) || 60;
      scanner.continuousScanning(interval);
      break;
    case 'contract':
      const address = args[1];
      const chainId = parseInt(args[2]) || 1;
      if (address) {
        scanner.scanSpecificContract(address, chainId);
      } else {
        logger.error('Please provide contract address');
      }
      break;
    case 'stats':
      scanner.getStats();
      break;
    case 'execute':
      scanner.executePendingExploits();
      break;
    case 'dashboard':
      logger.info('Starting dashboard server...');
      require('./dashboard/server');
      break;
    case 'monitor':
      const { blockMonitor } = require('./monitor');
      blockMonitor.startMonitoring();
      break;
    case 'metrics':
      logger.info('Displaying performance metrics...');
      performanceMonitor.displayMetrics();
      break;
    case 'fuzz':
      const fuzzAddress = args[1];
      const fuzzChainId = parseInt(args[2]) || 1;
      const maxIterations = parseInt(args[3]) || 100;
      if (fuzzAddress) {
        logger.info(`Starting fuzzing for contract ${fuzzAddress} on chain ${fuzzChainId} with ${maxIterations} iterations`);
        // This would need proper implementation
        logger.warn('Fuzzing command not yet fully implemented');
      } else {
        logger.error('Please provide contract address for fuzzing');
      }
      break;
    default:
      logger.info('Usage: ts-node scanner.ts [scan|incremental|continuous|contract|stats|execute|dashboard|monitor|metrics|fuzz]');
      logger.info('Commands:');
      logger.info('  scan        - Full scan of all chains');
      logger.info('  incremental - Incremental scan from last processed block');
      logger.info('  continuous  - Continuous scanning with interval');
      logger.info('  contract    - Scan specific contract');
      logger.info('  stats       - Show statistics');
      logger.info('  execute     - Execute pending exploits');
      logger.info('  dashboard   - Start web dashboard');
      logger.info('  monitor     - Start real-time block monitoring');
      logger.info('  metrics     - Show performance metrics');
      logger.info('  fuzz        - Fuzz specific contract');
      break;
  }
}
