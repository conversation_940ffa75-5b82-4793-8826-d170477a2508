/**
 * 🏺 SURGICAL PRECISION: Enhanced Bytecode Analysis System
 * 
 * This module provides multi-source bytecode analysis for unverified contracts.
 * Integrates proven open-source decompilers and analysis tools for maximum accuracy.
 * 
 * Sources:
 * 1. Heimdall-rs (Primary decompiler)
 * 2. Dedaub API (Commercial grade)
 * 3. EtherVM (Backup)
 * 4. Internal bytecode pattern matching
 * 
 * NO GUESSES. SURGICAL PRECISION.
 */

import axios from 'axios';
import { ethers } from 'ethers';
import { exec } from 'child_process';
import { promisify } from 'util';
import { logger } from './logger';
import { CONFIG } from './config';

const execAsync = promisify(exec);

export interface BytecodeAnalysis {
  address: string;
  chainId: number;
  bytecode: string;
  functions: DetectedFunction[];
  decompilationSources: string[];
  confidence: number;
  isVerified: boolean;
  analysis: {
    hasTargetFunctions: boolean;
    targetFunctions: string[];
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    extractionPotential: number; // 0-100
  };
}

export interface DetectedFunction {
  signature: string;
  fourByteSignature: string;
  confidence: number;
  source: string;
  parameters?: string[];
  isPayable?: boolean;
  isView?: boolean;
}

class BytecodeAnalyzer {
  private cache = new Map<string, BytecodeAnalysis>();
  private readonly DEDAUB_API = 'https://api.dedaub.com/decompile';
  private readonly ETHERVM_API = 'https://ethervm.io/decompile';

  /**
   * SURGICAL PRECISION: Analyze bytecode with multiple decompilers
   */
  async analyzeBytecode(
    address: string, 
    chainId: number, 
    bytecode?: string
  ): Promise<BytecodeAnalysis> {
    const cacheKey = `${address}:${chainId}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    logger.relic(`🔍 Analyzing bytecode for ${address} on chain ${chainId}`);

    // Get bytecode if not provided
    if (!bytecode) {
      bytecode = await this.fetchBytecode(address, chainId);
    }

    const analysis: BytecodeAnalysis = {
      address,
      chainId,
      bytecode,
      functions: [],
      decompilationSources: [],
      confidence: 0,
      isVerified: false,
      analysis: {
        hasTargetFunctions: false,
        targetFunctions: [],
        riskLevel: 'LOW',
        extractionPotential: 0
      }
    };

    // Multi-source analysis
    const analysisResults = await Promise.allSettled([
      this.analyzeWithHeimdall(bytecode),
      this.analyzeWithDedaub(bytecode),
      this.analyzeWithInternalPatterns(bytecode)
    ]);

    // Consolidate results
    const functions = new Map<string, DetectedFunction>();
    
    analysisResults.forEach((result, index) => {
      const sources = ['Heimdall-rs', 'Dedaub', 'Internal'];
      
      if (result.status === 'fulfilled' && result.value) {
        analysis.decompilationSources.push(sources[index]);
        
        result.value.forEach((func: DetectedFunction) => {
          const existing = functions.get(func.fourByteSignature);
          if (!existing || func.confidence > existing.confidence) {
            functions.set(func.fourByteSignature, func);
          }
        });
      }
    });

    analysis.functions = Array.from(functions.values());
    analysis.confidence = this.calculateConfidence(analysis);
    
    // Check for target functions
    this.analyzeTargetFunctions(analysis);
    
    // Calculate extraction potential
    this.calculateExtractionPotential(analysis);

    logger.relic(`📊 Analysis complete: ${analysis.functions.length} functions detected (${analysis.confidence}% confidence)`);
    
    if (analysis.analysis.hasTargetFunctions) {
      logger.mark(`🎯 TARGET FUNCTIONS DETECTED: ${analysis.analysis.targetFunctions.join(', ')}`);
    }

    this.cache.set(cacheKey, analysis);
    return analysis;
  }

  /**
   * Fetch bytecode from chain
   */
  private async fetchBytecode(address: string, chainId: number): Promise<string> {
    const chain = CONFIG.chains.find(c => c.chainId === chainId);
    if (!chain?.rpcUrl) {
      throw new Error(`No RPC URL configured for chain ${chainId}`);
    }

    const provider = new ethers.JsonRpcProvider(chain.rpcUrl);
    const bytecode = await provider.getCode(address);
    
    if (bytecode === '0x') {
      throw new Error(`No bytecode found at address ${address}`);
    }

    return bytecode;
  }

  /**
   * Analyze with Heimdall-rs (if available)
   */
  private async analyzeWithHeimdall(bytecode: string): Promise<DetectedFunction[]> {
    try {
      // Check if heimdall is installed
      await execAsync('which heimdall');
      
      // Create temporary file for bytecode
      const tempFile = `/tmp/bytecode_${Date.now()}.hex`;
      require('fs').writeFileSync(tempFile, bytecode);
      
      // Run heimdall decompilation
      const { stdout } = await execAsync(`heimdall decompile ${tempFile} --include-sol --include-yul`, {
        timeout: 30000
      });
      
      // Parse heimdall output for function signatures
      const functions = this.parseHeimdallOutput(stdout);
      
      // Cleanup
      require('fs').unlinkSync(tempFile);
      
      logger.relic(`🔨 Heimdall-rs detected ${functions.length} functions`);
      return functions;
      
    } catch (error) {
      logger.debug(`Heimdall-rs analysis failed: ${error}`);
      return [];
    }
  }

  /**
   * Analyze with Dedaub API (if available)
   */
  private async analyzeWithDedaub(bytecode: string): Promise<DetectedFunction[]> {
    try {
      const response = await axios.post(this.DEDAUB_API, {
        bytecode: bytecode,
        include_functions: true
      }, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.functions) {
        const functions = this.parseDedaubOutput(response.data.functions);
        logger.relic(`🏛️ Dedaub detected ${functions.length} functions`);
        return functions;
      }

      return [];
    } catch (error) {
      logger.debug(`Dedaub analysis failed: ${error}`);
      return [];
    }
  }

  /**
   * Internal pattern matching for target functions
   */
  private async analyzeWithInternalPatterns(bytecode: string): Promise<DetectedFunction[]> {
    const functions: DetectedFunction[] = [];
    
    // Search for target function signatures in bytecode
    CONFIG.targetFunctions.forEach(targetFunc => {
      const signature = targetFunc.fourByteSignature.slice(2); // Remove 0x
      
      if (bytecode.toLowerCase().includes(signature.toLowerCase())) {
        functions.push({
          signature: targetFunc.signature,
          fourByteSignature: targetFunc.fourByteSignature,
          confidence: 85, // High confidence for direct signature match
          source: 'Internal Pattern Matching'
        });
      }
    });

    if (functions.length > 0) {
      logger.extract(`🎯 Internal pattern matching found ${functions.length} target functions`);
    }

    return functions;
  }

  /**
   * Parse Heimdall-rs output
   */
  private parseHeimdallOutput(output: string): DetectedFunction[] {
    const functions: DetectedFunction[] = [];
    
    // Parse function signatures from Heimdall output
    const functionRegex = /function\s+(\w+)\s*\([^)]*\)/g;
    let match;
    
    while ((match = functionRegex.exec(output)) !== null) {
      const functionName = match[1];
      const signature = match[0].replace('function ', '');
      
      try {
        const fourByteSignature = ethers.id(signature).slice(0, 10);
        
        functions.push({
          signature,
          fourByteSignature,
          confidence: 90,
          source: 'Heimdall-rs'
        });
      } catch (error) {
        // Skip invalid signatures
      }
    }

    return functions;
  }

  /**
   * Parse Dedaub output
   */
  private parseDedaubOutput(dedaubFunctions: any[]): DetectedFunction[] {
    const functions: DetectedFunction[] = [];
    
    dedaubFunctions.forEach(func => {
      if (func.signature && func.selector) {
        functions.push({
          signature: func.signature,
          fourByteSignature: func.selector,
          confidence: 95, // Dedaub is highly accurate
          source: 'Dedaub',
          parameters: func.parameters,
          isPayable: func.payable,
          isView: func.view
        });
      }
    });

    return functions;
  }

  /**
   * Calculate overall confidence
   */
  private calculateConfidence(analysis: BytecodeAnalysis): number {
    if (analysis.functions.length === 0) return 0;
    
    const avgConfidence = analysis.functions.reduce((sum, func) => sum + func.confidence, 0) / analysis.functions.length;
    const sourceBonus = analysis.decompilationSources.length * 10; // Bonus for multiple sources
    
    return Math.min(100, Math.round(avgConfidence + sourceBonus));
  }

  /**
   * Analyze for target functions
   */
  private analyzeTargetFunctions(analysis: BytecodeAnalysis): void {
    const targetSignatures = CONFIG.targetFunctions.map(f => f.fourByteSignature.toLowerCase());
    
    analysis.analysis.targetFunctions = analysis.functions
      .filter(func => targetSignatures.includes(func.fourByteSignature.toLowerCase()))
      .map(func => func.signature);
    
    analysis.analysis.hasTargetFunctions = analysis.analysis.targetFunctions.length > 0;
    
    // Set risk level based on target functions
    if (analysis.analysis.targetFunctions.length >= 3) {
      analysis.analysis.riskLevel = 'HIGH';
    } else if (analysis.analysis.targetFunctions.length >= 1) {
      analysis.analysis.riskLevel = 'MEDIUM';
    } else {
      analysis.analysis.riskLevel = 'LOW';
    }
  }

  /**
   * Calculate extraction potential
   */
  private calculateExtractionPotential(analysis: BytecodeAnalysis): void {
    let potential = 0;
    
    // Base potential from target functions
    potential += analysis.analysis.targetFunctions.length * 25;
    
    // Bonus for high-priority functions
    const highPriorityFunctions = ['claim()', 'withdraw()', 'exit()', 'emergencyWithdraw()'];
    const hasHighPriority = analysis.analysis.targetFunctions.some(func => 
      highPriorityFunctions.some(hpf => func.includes(hpf.split('(')[0]))
    );
    
    if (hasHighPriority) potential += 20;
    
    // Bonus for multiple decompilation sources
    potential += analysis.decompilationSources.length * 5;
    
    // Confidence bonus
    potential += Math.round(analysis.confidence * 0.1);
    
    analysis.analysis.extractionPotential = Math.min(100, potential);
  }

  /**
   * Get cached analysis
   */
  getCachedAnalysis(address: string, chainId: number): BytecodeAnalysis | null {
    return this.cache.get(`${address}:${chainId}`) || null;
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.cache.clear();
    logger.ghost('Bytecode analysis cache cleared');
  }
}

export const bytecodeAnalyzer = new BytecodeAnalyzer();
