import { ethers } from 'ethers';
import { ContractInfo, SimulationResult, ChainConfig } from './types';
import { FilterResult } from './filter';
import { CONFIG } from './config';
import { logger } from './logger';
import { db } from './database';
import { merkleProofGenerator } from './merkle';
import { performanceMonitor } from './metrics';

export class ContractSimulator {
  private providers: Map<number, ethers.JsonRpcProvider> = new Map();

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders(): void {
    for (const chain of CONFIG.chains) {
      if (chain.rpcUrl) {
        const provider = new ethers.JsonRpcProvider(chain.rpcUrl);
        this.providers.set(chain.chainId, provider);
        logger.debug(`Initialized provider for ${chain.name} (${chain.chainId})`);
      }
    }
  }

  async simulateContract(filterResult: FilterResult): Promise<SimulationResult[]> {
    const { contract, matchedFunctions } = filterResult;
    const results: SimulationResult[] = [];

    const provider = this.providers.get(contract.chainId);
    if (!provider) {
      logger.error(`No provider configured for chain ${contract.chainId}`);
      return results;
    }

    // Create contract instance
    const contractInstance = new ethers.Contract(contract.address, contract.abi, provider);

    for (const targetFunction of matchedFunctions) {
      try {
        // Skip if already simulated
        if (await db.hasBeenSimulated(contract.address, targetFunction.name)) {
          logger.debug(`Skipping already simulated function ${targetFunction.name} on ${contract.address}`);
          continue;
        }

        const result = await this.simulateFunction(contractInstance, targetFunction, contract);
        results.push(result);
        
        // Save simulation result
        await db.saveSimulation(result);
        
        logger.debug(`Simulated ${targetFunction.name} on ${contract.address}: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      } catch (error) {
        logger.error(`Failed to simulate ${targetFunction.name} on ${contract.address}:`, error);
        
        const errorResult: SimulationResult = {
          contractAddress: contract.address,
          functionName: targetFunction.name,
          signature: targetFunction.signature,
          success: false,
          returnData: '',
          gasEstimate: 0,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now()
        };
        
        results.push(errorResult);
        await db.saveSimulation(errorResult);
      }
    }

    return results;
  }

  private async simulateFunction(
    contractInstance: ethers.Contract,
    targetFunction: any,
    contract: ContractInfo
  ): Promise<SimulationResult> {
    const functionName = targetFunction.name;
    
    // Get function from ABI
    const abiFunction = contract.abi.find(
      item => item.type === 'function' && item.name === functionName
    );

    if (!abiFunction) {
      throw new Error(`Function ${functionName} not found in ABI`);
    }

    const result: SimulationResult = {
      contractAddress: contract.address,
      functionName: functionName,
      signature: targetFunction.signature,
      success: false,
      returnData: '',
      gasEstimate: 0,
      timestamp: Date.now()
    };

    const simId = `sim_${contract.address}_${functionName}_${Date.now()}`;

    try {
      performanceMonitor.startTiming(simId, 'simulation', {
        contract: contract.address,
        function: functionName
      });

      // Prepare function parameters
      const params = this.generateFunctionParameters(abiFunction);

      // Simulate the function call
      const simulationPromise = this.callFunction(contractInstance, functionName, params);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Simulation timeout')), CONFIG.simulationTimeout)
      );

      const callResult = await Promise.race([simulationPromise, timeoutPromise]);

      result.success = true;
      result.returnData = this.formatReturnData(callResult);
      performanceMonitor.endTiming(simId, true);
      
      // Estimate gas
      try {
        performanceMonitor.recordRpcCall(true);
        const gasEstimate = await contractInstance[functionName].estimateGas(...params);
        result.gasEstimate = Number(gasEstimate);
      } catch (gasError) {
        performanceMonitor.recordRpcCall(false);
        performanceMonitor.incrementCounter('gas_estimation_failures');
        logger.debug(`Gas estimation failed for ${functionName}:`, gasError);
        result.gasEstimate = 100000; // Default gas estimate
      }

      // Try to extract potential value
      result.potentialValue = this.extractPotentialValue(callResult, abiFunction);

      return result;
    } catch (error) {
      performanceMonitor.endTiming(simId, false);

      if (error instanceof Error && error.message.includes('timeout')) {
        performanceMonitor.incrementCounter('simulation_timeouts');
      }

      result.success = false;
      result.error = error instanceof Error ? error.message : 'Unknown error';
      return result;
    }
  }

  private async callFunction(
    contractInstance: ethers.Contract,
    functionName: string,
    params: any[]
  ): Promise<any> {
    // Try different call methods based on function characteristics
    
    // Method 1: Direct staticCall
    try {
      return await contractInstance[functionName].staticCall(...params);
    } catch (error) {
      logger.debug(`Static call failed for ${functionName}, trying call method`);
    }

    // Method 2: Regular call (view function)
    try {
      return await contractInstance[functionName](...params);
    } catch (error) {
      logger.debug(`Regular call failed for ${functionName}, trying with different parameters`);
    }

    // Method 3: Try with zero address if function might need an address parameter
    if (params.length === 0) {
      try {
        return await contractInstance[functionName](ethers.ZeroAddress);
      } catch (error) {
        logger.debug(`Zero address call failed for ${functionName}`);
      }
    }

    // Method 4: Try with current wallet address
    try {
      const wallet = new ethers.Wallet(CONFIG.privateKey);
      return await contractInstance[functionName](wallet.address, ...params);
    } catch (error) {
      logger.debug(`Wallet address call failed for ${functionName}`);
    }

    throw new Error(`All call methods failed for ${functionName}`);
  }

  private generateFunctionParameters(abiFunction: any): any[] {
    // Use sophisticated parameter generation
    const userAddress = this.getSimulationAddress();

    // Check if this function likely uses merkle proofs
    if (merkleProofGenerator.detectMerkleProofFunction(abiFunction.name)) {
      const paramSets = merkleProofGenerator.generateParametersFromABI(abiFunction, userAddress);
      return paramSets[0] || []; // Use first parameter set
    }

    // Generate parameters based on function signature
    const functionSignature = this.generateFunctionSignature(abiFunction);
    const paramSets = merkleProofGenerator.generateClaimParameters(functionSignature, userAddress);

    if (paramSets.length > 0) {
      return paramSets[0];
    }

    // Fallback to basic parameter generation
    return this.generateBasicParameters(abiFunction);
  }

  private generateBasicParameters(abiFunction: any): any[] {
    const params: any[] = [];

    for (const input of abiFunction.inputs || []) {
      switch (input.type) {
        case 'address':
          params.push(this.getSimulationAddress());
          break;
        case 'uint256':
        case 'uint':
          params.push('1000000000000000000'); // 1 ETH worth
          break;
        case 'bool':
          params.push(true);
          break;
        case 'bytes':
        case 'bytes32':
          params.push(ethers.ZeroHash);
          break;
        case 'string':
          params.push('test');
          break;
        default:
          if (input.type.startsWith('uint')) {
            params.push('1000000000000000000');
          } else if (input.type.startsWith('bytes')) {
            params.push('0x');
          } else if (input.type.endsWith('[]')) {
            params.push([]);
          } else {
            params.push(ethers.ZeroAddress);
          }
          break;
      }
    }

    return params;
  }

  private generateFunctionSignature(abiFunction: any): string {
    const inputs = abiFunction.inputs || [];
    const paramTypes = inputs.map((input: any) => input.type).join(',');
    return `${abiFunction.name}(${paramTypes})`;
  }

  private getSimulationAddress(): string {
    // Use a deterministic address for simulation
    return '******************************************'; // Random but consistent address
  }

  private formatReturnData(result: any): string {
    if (result === null || result === undefined) {
      return '';
    }
    
    try {
      if (typeof result === 'bigint') {
        return result.toString();
      }
      
      if (Array.isArray(result)) {
        return result.map(item => 
          typeof item === 'bigint' ? item.toString() : String(item)
        ).join(',');
      }
      
      return String(result);
    } catch (error) {
      logger.debug('Failed to format return data:', error);
      return '';
    }
  }

  private extractPotentialValue(result: any, abiFunction: any): string {
    // Try to extract potential ETH or token value from the result
    
    // Check if function returns a balance-like value
    if (typeof result === 'bigint' && result > 0) {
      return result.toString();
    }
    
    // Check if function returns multiple values where one might be a balance
    if (Array.isArray(result)) {
      for (const item of result) {
        if (typeof item === 'bigint' && item > 0) {
          return item.toString();
        }
      }
    }
    
    // Check function name patterns that might indicate value
    const functionName = abiFunction.name.toLowerCase();
    if (functionName.includes('balance') || 
        functionName.includes('amount') || 
        functionName.includes('value') ||
        functionName.includes('reward')) {
      if (typeof result === 'bigint') {
        return result.toString();
      }
    }
    
    return '0';
  }

  async batchSimulate(filterResults: FilterResult[]): Promise<SimulationResult[]> {
    const allResults: SimulationResult[] = [];
    
    // Process contracts in batches to avoid overwhelming the RPC
    const batchSize = 10;
    for (let i = 0; i < filterResults.length; i += batchSize) {
      const batch = filterResults.slice(i, i + batchSize);
      const batchPromises = batch.map(filterResult => this.simulateContract(filterResult));
      
      try {
        const batchResults = await Promise.allSettled(batchPromises);
        
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            allResults.push(...result.value);
          } else {
            logger.error('Batch simulation failed:', result.reason);
          }
        }
      } catch (error) {
        logger.error('Batch simulation error:', error);
      }
      
      // Small delay between batches
      if (i + batchSize < filterResults.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return allResults;
  }

  // Check if a contract has ETH or token balance
  async checkContractBalance(contractAddress: string, chainId: number): Promise<{
    ethBalance: string;
    hasTokens: boolean;
    tokenBalances: { address: string; symbol: string; balance: string }[];
  }> {
    const provider = this.providers.get(chainId);
    if (!provider) {
      return { ethBalance: '0', hasTokens: false, tokenBalances: [] };
    }

    try {
      // Check ETH balance
      const balance = await provider.getBalance(contractAddress);
      
      // Check for ERC20 token balances
      const tokenBalances = await this.checkERC20Balances(contractAddress, chainId);
      const hasTokens = tokenBalances.length > 0;
      
      return {
        ethBalance: balance.toString(),
        hasTokens,
        tokenBalances
      };
    } catch (error) {
      logger.debug(`Failed to check balance for ${contractAddress}:`, error);
      return { ethBalance: '0', hasTokens: false, tokenBalances: [] };
    }
  }

  // Check ERC20 token balances for a contract
  private async checkERC20Balances(contractAddress: string, chainId: number): Promise<{
    address: string;
    symbol: string;
    balance: string;
  }[]> {
    const provider = this.providers.get(chainId);
    if (!provider) {
      return [];
    }

    const tokenBalances: { address: string; symbol: string; balance: string }[] = [];
    
    // Common ERC20 tokens per chain
    const commonTokens: { [key: number]: string[] } = {
      1: [ // Ethereum
        '******************************************', // USDC
        '******************************************', // DAI
        '******************************************', // UNI
        '******************************************', // MATIC
        '******************************************', // WBTC
        '******************************************', // WETH
      ],
      42161: [ // Arbitrum
        '******************************************', // USDC
        '******************************************', // DAI
        '******************************************', // WETH
        '******************************************', // ARB
      ],
      8453: [ // Base
        '******************************************', // USDC
        '******************************************', // WETH
      ],
      10: [ // Optimism
        '******************************************', // USDT
        '******************************************', // WETH
        '******************************************', // DAI
      ],
      137: [ // Polygon
        '******************************************', // USDC
        '******************************************', // WETH
        '******************************************', // DAI
      ]
    };

    const tokensToCheck = commonTokens[chainId] || [];
    
    // Standard ERC20 ABI for balanceOf and symbol
    const erc20Abi = [
      'function balanceOf(address owner) view returns (uint256)',
      'function symbol() view returns (string)',
      'function decimals() view returns (uint8)'
    ];

    for (const tokenAddress of tokensToCheck) {
      try {
        const tokenContract = new ethers.Contract(tokenAddress, erc20Abi, provider);
        
        // Check balance
        const balance = await tokenContract.balanceOf(contractAddress);
        
        if (balance > 0) {
          try {
            const symbol = await tokenContract.symbol();
            const decimals = await tokenContract.decimals();
            
            tokenBalances.push({
              address: tokenAddress,
              symbol: symbol,
              balance: ethers.formatUnits(balance, decimals)
            });
          } catch (error) {
            // If we can't get symbol/decimals, still record the balance
            tokenBalances.push({
              address: tokenAddress,
              symbol: 'UNKNOWN',
              balance: balance.toString()
            });
          }
        }
      } catch (error) {
        logger.debug(`Failed to check token balance for ${tokenAddress}:`, error);
      }
    }

    // Also check for tokens by examining recent Transfer events TO this contract
    try {
      const transferTopic = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';
      const paddedAddress = ethers.zeroPadValue(contractAddress, 32);
      
      // Get recent Transfer events where this contract is the recipient
      const currentBlock = await provider.getBlockNumber();
      const fromBlock = Math.max(0, currentBlock - 10000); // Look back 10k blocks
      
      const logs = await provider.getLogs({
        fromBlock,
        toBlock: currentBlock,
        topics: [transferTopic, null, paddedAddress] // Transfer(from, to, value) where to = contractAddress
      });
      
      // Extract unique token addresses from the logs
      const discoveredTokens = new Set<string>();
      for (const log of logs) {
        if (log.address && !tokensToCheck.includes(log.address)) {
          discoveredTokens.add(log.address);
        }
      }
      
      // Check balances for discovered tokens
      for (const tokenAddress of discoveredTokens) {
        try {
          const tokenContract = new ethers.Contract(tokenAddress, erc20Abi, provider);
          const balance = await tokenContract.balanceOf(contractAddress);
          
          if (balance > 0) {
            try {
              const symbol = await tokenContract.symbol();
              const decimals = await tokenContract.decimals();
              
              tokenBalances.push({
                address: tokenAddress,
                symbol: symbol,
                balance: ethers.formatUnits(balance, decimals)
              });
            } catch (error) {
              tokenBalances.push({
                address: tokenAddress,
                symbol: 'DISCOVERED',
                balance: balance.toString()
              });
            }
          }
        } catch (error) {
          logger.debug(`Failed to check discovered token ${tokenAddress}:`, error);
        }
      }
    } catch (error) {
      logger.debug(`Failed to discover tokens for ${contractAddress}:`, error);
    }

    return tokenBalances;
  }

  // Advanced simulation with custom parameters
  async simulateWithCustomParams(
    contract: ContractInfo,
    functionName: string,
    params: any[]
  ): Promise<SimulationResult> {
    const provider = this.providers.get(contract.chainId);
    if (!provider) {
      throw new Error(`No provider for chain ${contract.chainId}`);
    }

    const contractInstance = new ethers.Contract(contract.address, contract.abi, provider);
    const abiFunction = contract.abi.find(
      item => item.type === 'function' && item.name === functionName
    );

    if (!abiFunction) {
      throw new Error(`Function ${functionName} not found in ABI`);
    }

    const result: SimulationResult = {
      contractAddress: contract.address,
      functionName: functionName,
      signature: `${functionName}(${params.map(() => 'unknown').join(',')})`,
      success: false,
      returnData: '',
      gasEstimate: 0,
      timestamp: Date.now()
    };

    try {
      const callResult = await contractInstance[functionName].staticCall(...params);
      result.success = true;
      result.returnData = this.formatReturnData(callResult);
      result.potentialValue = this.extractPotentialValue(callResult, abiFunction);

      const gasEstimate = await contractInstance[functionName].estimateGas(...params);
      result.gasEstimate = Number(gasEstimate);

      return result;
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      return result;
    }
  }

  getProvider(chainId: number): ethers.JsonRpcProvider | undefined {
    return this.providers.get(chainId);
  }
}

export const contractSimulator = new ContractSimulator();
