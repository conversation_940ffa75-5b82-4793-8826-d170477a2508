import chalk from 'chalk';
import { CONFIG } from './config';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

class Logger {
  private level: LogLevel;

  constructor() {
    this.level = this.parseLogLevel(CONFIG.logLevel);
  }

  private parseLogLevel(level: string): LogLevel {
    switch (level.toLowerCase()) {
      case 'error': return LogLevel.ERROR;
      case 'warn': return LogLevel.WARN;
      case 'info': return LogLevel.INFO;
      case 'debug': return LogLevel.DEBUG;
      default: return LogLevel.INFO;
    }
  }

  private timestamp(): string {
    return new Date().toISOString();
  }

  error(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.ERROR) {
      console.error(chalk.red(`[${this.timestamp()}] ERROR: ${message}`), ...args);
    }
  }

  warn(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.WARN) {
      console.warn(chalk.yellow(`[${this.timestamp()}] WARN: ${message}`), ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.INFO) {
      console.info(chalk.blue(`[${this.timestamp()}] INFO: ${message}`), ...args);
    }
  }

  debug(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.DEBUG) {
      console.debug(chalk.gray(`[${this.timestamp()}] DEBUG: ${message}`), ...args);
    }
  }

  success(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.INFO) {
      console.info(chalk.green(`[${this.timestamp()}] SUCCESS: ${message}`), ...args);
    }
  }

  exploit(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.INFO) {
      console.info(chalk.magenta.bold(`[${this.timestamp()}] EXPLOIT: ${message}`), ...args);
    }
  }

  transaction(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.INFO) {
      console.info(chalk.cyan(`[${this.timestamp()}] TX: ${message}`), ...args);
    }
  }

  // ARCHAEOLOGIST MINDSET: Cold, surgical logging

  // Silent extraction - no celebration, just facts
  extract(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.INFO) {
      console.info(chalk.green(`[${this.timestamp()}] EXTRACT: ${message}`), ...args);
    }
  }

  // Mark targets for future exploitation
  mark(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.INFO) {
      console.info(chalk.yellow(`[${this.timestamp()}] MARK: ${message}`), ...args);
    }
  }

  // Relic analysis - dissecting dead contracts
  relic(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.DEBUG) {
      console.debug(chalk.cyan(`[${this.timestamp()}] RELIC: ${message}`), ...args);
    }
  }

  // Ghost mode - silent operations
  ghost(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.DEBUG) {
      console.debug(chalk.gray(`[${this.timestamp()}] GHOST: ${message}`), ...args);
    }
  }

  // Dust collection - small but guaranteed value
  dust(message: string, ...args: any[]): void {
    if (this.level >= LogLevel.INFO) {
      console.info(chalk.dim.green(`[${this.timestamp()}] DUST: ${message}`), ...args);
    }
  }
}

export const logger = new Logger();
