import { SimulationResult, ExploitableContract } from './types';
import { logger } from './logger';
import { db } from './database';

export class Evaluator {
  evaluateSimulations(simulations: SimulationResult[]): ExploitableContract[] {
    const exploits: ExploitableContract[] = [];

    for (const result of simulations) {
      if (result.success && this.isProfitable(result)) {
        const potentialValue = parseFloat(result.potentialValue || '0');
        const exploit: ExploitableContract = {
          address: result.contractAddress,
          functionName: result.functionName,
          signature: result.signature,
          estimatedValue: result.potentialValue || '0',
          gasEstimate: result.gasEstimate,
          priority: 1, // Initial priority, further logic can refine this
          chainId: 1, // Placeholder, adjust according to your logic
          discovered: new Date(),
          executed: false
        };
        exploits.push(exploit);
        db.saveExploitableContract(exploit);
        logger.exploit(`Identified exploitable contract: ${result.contractAddress} with potential value ${result.potentialValue}`);
      }
    }

    return exploits;
  }

  private isProfitable(simulation: SimulationResult): boolean {
    // ARCHAEOLOGIST MODE: Proper profitability calculation
    if (!simulation.potentialValue || simulation.gasEstimate <= 0) {
      return false;
    }

    const potentialValue = parseFloat(simulation.potentialValue);

    // Realistic gas cost calculation (20 gwei gas price)
    const gasPrice = 20e9; // 20 gwei in wei
    const gasCostWei = simulation.gasEstimate * gasPrice;
    const gasCostEth = gasCostWei / 1e18; // Convert to ETH

    // ARCHAEOLOGIST THRESHOLD: Profit must be at least 2x gas cost
    const profitThreshold = gasCostEth * 2;

    logger.debug(`Profitability check: Value=${potentialValue} ETH, Gas=${gasCostEth} ETH, Threshold=${profitThreshold} ETH`);

    return potentialValue > profitThreshold;
  }

  evaluateBatch(simulationResults: SimulationResult[]): ExploitableContract[] {
    return simulationResults
      .filter(this.isProfitable)
      .map(result => this.createExploit(result));
  }

  private createExploit(result: SimulationResult): ExploitableContract {
    const potentialValue = parseFloat(result.potentialValue || '0');
    const newExploit: ExploitableContract = {
      address: result.contractAddress,
      functionName: result.functionName,
      signature: result.signature,
      estimatedValue: result.potentialValue || '0',
      gasEstimate: result.gasEstimate,
      priority: 1, // Default priority
      chainId: 1, // Default, replace with actual logic
      discovered: new Date(),
      executed: false
    };

    logger.exploit(`Found exploitable contract: ${newExploit.address}, potential value: ${newExploit.estimatedValue}`);
    return newExploit;
  }
}

export const evaluator = new Evaluator();

