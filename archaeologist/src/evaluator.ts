import { SimulationResult, ExploitableContract } from './types';
import { logger } from './logger';
import { db } from './database';
// 🏺 SURGICAL PRECISION INTEGRATION
import { economicEngine } from './economic-engine';
import { selfMonitor } from './self-monitor';

export class Evaluator {
  async evaluateSimulations(simulations: SimulationResult[]): Promise<ExploitableContract[]> {
    const exploits: ExploitableContract[] = [];

    logger.relic(`🔍 SURGICAL EVALUATION: Analyzing ${simulations.length} simulation results`);

    for (const result of simulations) {
      if (result.success) {
        // 🏺 SURGICAL PRECISION: Use economic engine for accurate profitability assessment
        const isViable = await this.isProfitableSurgical(result);

        if (isViable) {
          // Calculate priority based on surgical precision metrics
          const priority = this.calculateSurgicalPriority(result);

          const exploit: ExploitableContract = {
            address: result.contractAddress,
            functionName: result.functionName,
            signature: result.signature,
            estimatedValue: result.potentialValue || '0',
            gasEstimate: result.gasEstimate,
            priority,
            chainId: 1, // TODO: Extract from simulation context
            discovered: new Date(),
            executed: false
          };

          exploits.push(exploit);
          await db.saveExploitableContract(exploit);

          logger.extract(`🎯 VIABLE TARGET: ${result.contractAddress} - ${result.functionName} (Priority: ${priority})`);

          if (result.confidence) {
            logger.extract(`   Confidence: ${result.confidence}%`);
          }
          if (result.extractionPotential) {
            logger.extract(`   Extraction Potential: ${result.extractionPotential}%`);
          }
        } else {
          // 🏺 DISCOVERY MODE: Show what we're finding but rejecting
          if (process.env.DISCOVERY_MODE === 'true') {
            logger.relic(`🔍 DISCOVERY: Found target but not profitable: ${result.contractAddress} - ${result.functionName}`);
            if (result.potentialValue) {
              logger.relic(`   Potential Value: ${result.potentialValue} ETH`);
            }
            if (result.gasEstimate) {
              logger.relic(`   Gas Estimate: ${result.gasEstimate}`);
            }
          } else {
            logger.mark(`❌ Not profitable: ${result.contractAddress} - ${result.functionName}`);
          }
        }
      } else {
        logger.ghost(`⚪ Simulation failed: ${result.contractAddress} - ${result.functionName}`);
      }
    }

    if (exploits.length > 0) {
      logger.extract(`🏺 SURGICAL EVALUATION COMPLETE: ${exploits.length} viable targets identified`);
    } else {
      logger.ghost(`🔍 No viable targets found in current batch`);
    }

    return exploits;
  }

  private isProfitable(simulation: SimulationResult): boolean {
    // ARCHAEOLOGIST MODE: Proper profitability calculation
    if (!simulation.potentialValue || simulation.gasEstimate <= 0) {
      return false;
    }

    const potentialValue = parseFloat(simulation.potentialValue);

    // Realistic gas cost calculation (20 gwei gas price)
    const gasPrice = 20e9; // 20 gwei in wei
    const gasCostWei = simulation.gasEstimate * gasPrice;
    const gasCostEth = gasCostWei / 1e18; // Convert to ETH

    // ARCHAEOLOGIST THRESHOLD: Profit must be at least 2x gas cost
    const profitThreshold = gasCostEth * 2;

    logger.debug(`Profitability check: Value=${potentialValue} ETH, Gas=${gasCostEth} ETH, Threshold=${profitThreshold} ETH`);

    return potentialValue > profitThreshold;
  }

  /**
   * 🏺 SURGICAL PRECISION: Advanced profitability assessment using economic engine
   */
  private async isProfitableSurgical(simulation: SimulationResult): Promise<boolean> {
    try {
      if (!simulation.potentialValue || simulation.gasEstimate <= 0) {
        return false;
      }

      const estimatedValue = BigInt(Math.floor(parseFloat(simulation.potentialValue) * 1e18)); // Convert to wei
      const estimatedGas = BigInt(simulation.gasEstimate);

      // Build risk factors array
      const riskFactors: string[] = [];

      if (simulation.confidence && simulation.confidence < 80) {
        riskFactors.push('Low simulation confidence');
      }

      if (simulation.extractionPotential && simulation.extractionPotential < 50) {
        riskFactors.push('Low extraction potential');
      }

      // Use economic engine for precise analysis
      const economicAnalysis = await economicEngine.analyzeEconomics(
        1, // TODO: Get actual chain ID
        estimatedValue,
        estimatedGas,
        riskFactors
      );

      // Record analysis for monitoring
      selfMonitor.recordScan(economicAnalysis.profitability.meetsThreshold, 0);

      if (economicAnalysis.profitability.meetsThreshold) {
        logger.extract(`💰 PROFITABLE: ${simulation.contractAddress} - Net profit: ${economicAnalysis.profitability.netProfit.toString()} wei`);
        logger.extract(`   Risk Level: ${economicAnalysis.risk.level} (${economicAnalysis.risk.confidence}% confidence)`);
      }

      return economicAnalysis.profitability.meetsThreshold;

    } catch (error) {
      logger.error(`Economic analysis failed for ${simulation.contractAddress}: ${error}`);
      return false;
    }
  }

  /**
   * 🏺 SURGICAL PRECISION: Calculate priority based on multiple factors
   */
  private calculateSurgicalPriority(simulation: SimulationResult): number {
    let priority = 5; // Base priority (1 = highest, 5 = lowest)

    // Confidence factor
    if (simulation.confidence) {
      if (simulation.confidence >= 90) priority -= 2;
      else if (simulation.confidence >= 80) priority -= 1;
      else if (simulation.confidence < 60) priority += 1;
    }

    // Extraction potential factor
    if (simulation.extractionPotential) {
      if (simulation.extractionPotential >= 80) priority -= 1;
      else if (simulation.extractionPotential < 40) priority += 1;
    }

    // Function type priority (based on archaeologist targeting)
    const highPriorityFunctions = ['claim', 'withdraw', 'exit', 'emergencyWithdraw'];
    const functionName = simulation.functionName.toLowerCase();

    if (highPriorityFunctions.some(hpf => functionName.includes(hpf))) {
      priority -= 1;
    }

    // Ensure priority stays within bounds
    return Math.max(1, Math.min(5, priority));
  }

  evaluateBatch(simulationResults: SimulationResult[]): ExploitableContract[] {
    return simulationResults
      .filter(this.isProfitable)
      .map(result => this.createExploit(result));
  }

  private createExploit(result: SimulationResult): ExploitableContract {
    const potentialValue = parseFloat(result.potentialValue || '0');
    const newExploit: ExploitableContract = {
      address: result.contractAddress,
      functionName: result.functionName,
      signature: result.signature,
      estimatedValue: result.potentialValue || '0',
      gasEstimate: result.gasEstimate,
      priority: 1, // Default priority
      chainId: 1, // Default, replace with actual logic
      discovered: new Date(),
      executed: false
    };

    logger.exploit(`Found exploitable contract: ${newExploit.address}, potential value: ${newExploit.estimatedValue}`);
    return newExploit;
  }
}

export const evaluator = new Evaluator();

