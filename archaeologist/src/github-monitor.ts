import { logger } from './logger';
import { ContractInfo } from './types';
import { CONFIG } from './config';

export interface GitHubRelease {
  id: number;
  tag_name: string;
  name: string;
  body: string;
  published_at: string;
  html_url: string;
  assets: GitHubAsset[];
}

export interface GitHubAsset {
  name: string;
  browser_download_url: string;
  content_type: string;
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  created_at: string;
  updated_at: string;
  html_url: string;
  topics: string[];
}

export interface ProtocolDeployment {
  protocol: string;
  repository: string;
  release?: GitHubRelease;
  contractAddresses: string[];
  deploymentTime: Date;
  chains: number[];
  priority: number;
}

export class GitHubMonitor {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://api.github.com';
  private readonly monitoredProtocols: string[] = [
    // Major DeFi protocols that frequently deploy airdrops
    'Uniswap/v4-core',
    'aave/aave-v3-core',
    'compound-finance/compound-protocol',
    'makerdao/dss',
    'yearn/yearn-vaults',
    'sushiswap/sushiswap',
    'balancer/balancer-v2-monorepo',
    'curve-dao/curve-dao-contracts',
    'ethereum/EIPs',
    'OpenZeppelin/openzeppelin-contracts',
    // Layer 2 and new chains
    'ethereum-optimism/optimism',
    'OffchainLabs/arbitrum',
    'base-org/node',
    'polygon-hermez/zkevm-contracts',
    // Emerging protocols (high airdrop potential)
    'eigenlayer-middleware/eigenlayer-contracts',
    'celestiaorg/celestia-app',
    'starkware-libs/cairo',
    'matter-labs/era-contracts',
    // Airdrop-specific keywords to monitor
    'airdrop', 'claim', 'merkle', 'distribution', 'token-distribution'
  ];

  private readonly airdropKeywords = [
    'airdrop', 'claim', 'merkle', 'distribution', 'token',
    'governance', 'dao', 'voting', 'rewards', 'incentive',
    'retroactive', 'snapshot', 'eligibility', 'whitelist'
  ];

  constructor() {
    this.apiKey = process.env.GITHUB_API_KEY || '';
    if (!this.apiKey) {
      logger.warn('GitHub API key not configured - GitHub monitoring disabled');
    }
  }

  /**
   * Monitor GitHub for new releases from tracked protocols
   */
  async monitorProtocolReleases(): Promise<ProtocolDeployment[]> {
    if (!this.apiKey) {
      logger.warn('GitHub API key not configured');
      return [];
    }

    const deployments: ProtocolDeployment[] = [];
    logger.info(`🔍 Monitoring ${this.monitoredProtocols.length} protocols for new releases...`);

    for (const protocol of this.monitoredProtocols) {
      try {
        const releases = await this.fetchRecentReleases(protocol);
        
        for (const release of releases) {
          const deployment = await this.analyzeReleaseForAirdrop(protocol, release);
          if (deployment) {
            deployments.push(deployment);
            logger.extract(`🎯 POTENTIAL AIRDROP DETECTED: ${protocol} - ${release.name}`);
          }
        }

        // Rate limiting - GitHub API allows 5000 requests/hour
        await this.sleep(200);
      } catch (error) {
        logger.error(`Error monitoring protocol ${protocol}:`, error);
      }
    }

    return deployments;
  }

  /**
   * Search GitHub for new repositories with airdrop keywords
   */
  async searchNewAirdropRepositories(): Promise<GitHubRepository[]> {
    if (!this.apiKey) return [];

    const repositories: GitHubRepository[] = [];
    const searchQueries = [
      'airdrop created:>2024-12-01',
      'merkle claim created:>2024-12-01',
      'token distribution created:>2024-12-01',
      'governance token created:>2024-12-01'
    ];

    for (const query of searchQueries) {
      try {
        const repos = await this.searchRepositories(query);
        repositories.push(...repos);
        await this.sleep(1000); // More conservative rate limiting for search
      } catch (error) {
        logger.error(`Error searching repositories with query "${query}":`, error);
      }
    }

    return repositories;
  }

  /**
   * Fetch recent releases for a specific protocol
   */
  private async fetchRecentReleases(protocol: string): Promise<GitHubRelease[]> {
    const url = `${this.baseUrl}/repos/${protocol}/releases?per_page=5`;
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Contract-Archaeologist/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
    }

    const releases: GitHubRelease[] = await response.json();
    
    // Filter to releases from last 7 days
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    return releases.filter(release => 
      new Date(release.published_at) > sevenDaysAgo
    );
  }

  /**
   * Search repositories with specific query
   */
  private async searchRepositories(query: string): Promise<GitHubRepository[]> {
    const url = `${this.baseUrl}/search/repositories?q=${encodeURIComponent(query)}&sort=created&order=desc&per_page=20`;
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Contract-Archaeologist/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.items || [];
  }

  /**
   * Analyze a release to determine if it might contain airdrop contracts
   */
  private async analyzeReleaseForAirdrop(protocol: string, release: GitHubRelease): Promise<ProtocolDeployment | null> {
    const releaseText = `${release.name} ${release.body}`.toLowerCase();
    
    // Check for airdrop keywords
    const hasAirdropKeywords = this.airdropKeywords.some(keyword => 
      releaseText.includes(keyword)
    );

    if (!hasAirdropKeywords) {
      return null;
    }

    // Extract potential contract addresses from release notes
    const contractAddresses = this.extractContractAddresses(release.body);
    
    // Calculate priority based on protocol importance and keywords
    const priority = this.calculateAirdropPriority(protocol, releaseText);

    return {
      protocol,
      repository: protocol,
      release,
      contractAddresses,
      deploymentTime: new Date(release.published_at),
      chains: this.inferChainsFromRelease(releaseText),
      priority
    };
  }

  /**
   * Extract contract addresses from text using regex
   */
  private extractContractAddresses(text: string): string[] {
    const addressRegex = /0x[a-fA-F0-9]{40}/g;
    const matches = text.match(addressRegex) || [];
    
    // Remove duplicates and validate
    return [...new Set(matches)].filter(addr => 
      addr.length === 42 && addr.startsWith('0x')
    );
  }

  /**
   * Calculate priority score for airdrop potential
   */
  private calculateAirdropPriority(protocol: string, releaseText: string): number {
    let priority = 5; // Default priority

    // High-priority protocols
    const highPriorityProtocols = ['uniswap', 'aave', 'compound', 'eigenlayer'];
    if (highPriorityProtocols.some(p => protocol.toLowerCase().includes(p))) {
      priority = 1;
    }

    // High-priority keywords
    const highPriorityKeywords = ['airdrop', 'claim', 'distribution', 'retroactive'];
    if (highPriorityKeywords.some(keyword => releaseText.includes(keyword))) {
      priority = Math.min(priority, 2);
    }

    // Medium-priority keywords
    const mediumPriorityKeywords = ['governance', 'token', 'dao', 'rewards'];
    if (mediumPriorityKeywords.some(keyword => releaseText.includes(keyword))) {
      priority = Math.min(priority, 3);
    }

    return priority;
  }

  /**
   * Infer which chains might be involved based on release text
   */
  private inferChainsFromRelease(releaseText: string): number[] {
    const chains: number[] = [];
    
    if (releaseText.includes('ethereum') || releaseText.includes('mainnet')) {
      chains.push(1);
    }
    if (releaseText.includes('arbitrum')) {
      chains.push(42161);
    }
    if (releaseText.includes('base')) {
      chains.push(8453);
    }
    if (releaseText.includes('optimism')) {
      chains.push(10);
    }
    if (releaseText.includes('polygon')) {
      chains.push(137);
    }

    // Default to Ethereum if no specific chain mentioned
    return chains.length > 0 ? chains : [1];
  }

  /**
   * Get trending repositories that might contain airdrops
   */
  async getTrendingAirdropRepositories(): Promise<GitHubRepository[]> {
    if (!this.apiKey) return [];

    const queries = [
      'airdrop stars:>10 created:>2024-11-01',
      'merkle claim stars:>5 created:>2024-11-01',
      'token distribution language:Solidity created:>2024-11-01'
    ];

    const repositories: GitHubRepository[] = [];

    for (const query of queries) {
      try {
        const repos = await this.searchRepositories(query);
        repositories.push(...repos);
        await this.sleep(1000);
      } catch (error) {
        logger.error(`Error fetching trending repositories:`, error);
      }
    }

    return repositories;
  }

  /**
   * Monitor specific protocol for contract deployments
   */
  async monitorProtocolDeployments(protocol: string): Promise<string[]> {
    try {
      const releases = await this.fetchRecentReleases(protocol);
      const addresses: string[] = [];

      for (const release of releases) {
        const contractAddresses = this.extractContractAddresses(release.body);
        addresses.push(...contractAddresses);
      }

      return [...new Set(addresses)];
    } catch (error) {
      logger.error(`Error monitoring deployments for ${protocol}:`, error);
      return [];
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
