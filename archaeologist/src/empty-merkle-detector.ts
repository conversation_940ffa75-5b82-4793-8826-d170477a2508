import { ethers } from 'ethers';
import { logger } from './logger';
import { ContractInfo, ChainConfig } from './types';
import { CONFIG } from './config';

export interface MerkleRootAnalysis {
  contractAddress: string;
  chainId: number;
  merkleRoots: MerkleRootInfo[];
  isEmpty: boolean;
  isZero: boolean;
  hasProofs: boolean;
  claimAvailable: boolean;
  priority: number;
  confidence: number; // 0-100, how confident we are this is pre-proof
}

export interface MerkleRootInfo {
  variableName: string;
  currentValue: string;
  storageSlot?: number;
  isEmpty: boolean;
  isZero: boolean;
  lastUpdated?: Date;
}

export interface PreProofContract {
  contract: ContractInfo;
  analysis: MerkleRootAnalysis;
  timeWindow: number; // Hours since deployment
  actionRequired: boolean;
  estimatedTimeLeft: number; // Hours until proofs likely added
}

export class EmptyMerkleDetector {
  private readonly providers: Map<number, ethers.JsonRpcProvider> = new Map();

  // Common merkle root variable names and patterns
  private readonly merkleRootPatterns = [
    'merkleRoot',
    'merkle_root',
    'root',
    'merkleTreeRoot',
    'distributionRoot',
    'airdropRoot',
    'claimRoot',
    'proofRoot',
    '_merkleRoot',
    '_root'
  ];

  // Zero values that indicate empty merkle roots
  private readonly zeroValues = [
    '0x0000000000000000000000000000000000000000000000000000000000000000',
    '0x',
    '',
    '0'
  ];

  constructor() {
    this.initializeProviders();
  }

  /**
   * Initialize RPC providers for all chains
   */
  private initializeProviders(): void {
    for (const chain of CONFIG.chains) {
      if (chain.rpcUrl) {
        this.providers.set(chain.chainId, new ethers.JsonRpcProvider(chain.rpcUrl));
      }
    }
  }

  /**
   * Analyze contract for empty merkle roots
   */
  async analyzeContract(contract: ContractInfo): Promise<MerkleRootAnalysis | null> {
    const provider = this.providers.get(contract.chainId);
    if (!provider) {
      logger.warn(`No provider available for chain ${contract.chainId}`);
      return null;
    }

    try {
      logger.debug(`🔍 Analyzing ${contract.address} for empty merkle roots...`);

      const merkleRoots = await this.findMerkleRoots(contract, provider);
      
      if (merkleRoots.length === 0) {
        return null; // No merkle roots found
      }

      const isEmpty = merkleRoots.every(root => root.isEmpty);
      const isZero = merkleRoots.every(root => root.isZero);
      const hasProofs = merkleRoots.some(root => !root.isEmpty && !root.isZero);
      const claimAvailable = await this.testClaimAvailability(contract, provider);

      const priority = this.calculatePriority(contract, isEmpty, isZero, claimAvailable);
      const confidence = this.calculateConfidence(contract, merkleRoots, claimAvailable);

      const analysis: MerkleRootAnalysis = {
        contractAddress: contract.address,
        chainId: contract.chainId,
        merkleRoots,
        isEmpty,
        isZero,
        hasProofs,
        claimAvailable,
        priority,
        confidence
      };

      if (isEmpty || isZero) {
        logger.extract(`🎯 EMPTY MERKLE ROOT DETECTED: ${contract.address} (confidence: ${confidence}%)`);
      }

      return analysis;
    } catch (error) {
      logger.error(`Error analyzing contract ${contract.address}:`, error);
      return null;
    }
  }

  /**
   * Find merkle root variables in contract
   */
  private async findMerkleRoots(contract: ContractInfo, provider: ethers.JsonRpcProvider): Promise<MerkleRootInfo[]> {
    const merkleRoots: MerkleRootInfo[] = [];

    // Method 1: Parse ABI for merkle root getters
    if (contract.abi) {
      try {
        const abi = JSON.parse(contract.abi);
        const merkleGetters = abi.filter((item: any) => 
          item.type === 'function' && 
          item.stateMutability === 'view' &&
          this.merkleRootPatterns.some(pattern => 
            item.name?.toLowerCase().includes(pattern.toLowerCase())
          )
        );

        for (const getter of merkleGetters) {
          try {
            const contractInstance = new ethers.Contract(contract.address, abi, provider);
            const value = await contractInstance[getter.name]();
            
            merkleRoots.push({
              variableName: getter.name,
              currentValue: value.toString(),
              isEmpty: this.isEmptyValue(value.toString()),
              isZero: this.isZeroValue(value.toString())
            });
          } catch (error) {
            logger.debug(`Error calling ${getter.name} on ${contract.address}`);
          }
        }
      } catch (error) {
        logger.debug(`Error parsing ABI for ${contract.address}`);
      }
    }

    // Method 2: Check common storage slots for merkle roots
    const commonSlots = await this.checkCommonMerkleSlots(contract.address, provider);
    merkleRoots.push(...commonSlots);

    // Method 3: Parse source code for merkle root variables
    if (contract.sourceCode) {
      const sourceRoots = this.parseSourceCodeForMerkleRoots(contract.sourceCode, contract.address, provider);
      merkleRoots.push(...await sourceRoots);
    }

    return merkleRoots;
  }

  /**
   * Check common storage slots where merkle roots are typically stored
   */
  private async checkCommonMerkleSlots(address: string, provider: ethers.JsonRpcProvider): Promise<MerkleRootInfo[]> {
    const merkleRoots: MerkleRootInfo[] = [];
    
    // Common storage slots for merkle roots (0-10)
    for (let slot = 0; slot <= 10; slot++) {
      try {
        const storageValue = await provider.getStorage(address, slot);
        
        // Only consider non-zero values that might be merkle roots
        if (storageValue !== '0x0000000000000000000000000000000000000000000000000000000000000000') {
          // Check if this looks like a merkle root (32 bytes, not an address)
          if (storageValue.length === 66 && !this.looksLikeAddress(storageValue)) {
            merkleRoots.push({
              variableName: `storage_slot_${slot}`,
              currentValue: storageValue,
              storageSlot: slot,
              isEmpty: this.isEmptyValue(storageValue),
              isZero: this.isZeroValue(storageValue)
            });
          }
        }
      } catch (error) {
        // Storage slot doesn't exist or can't be read
      }
    }

    return merkleRoots;
  }

  /**
   * Parse source code for merkle root variable declarations
   */
  private async parseSourceCodeForMerkleRoots(
    sourceCode: string, 
    address: string, 
    provider: ethers.JsonRpcProvider
  ): Promise<MerkleRootInfo[]> {
    const merkleRoots: MerkleRootInfo[] = [];
    
    // Look for merkle root variable declarations
    for (const pattern of this.merkleRootPatterns) {
      const regex = new RegExp(`bytes32\\s+(?:public\\s+)?${pattern}`, 'gi');
      const matches = sourceCode.match(regex);
      
      if (matches) {
        // Try to read the value (this would require more sophisticated parsing)
        merkleRoots.push({
          variableName: pattern,
          currentValue: '0x0000000000000000000000000000000000000000000000000000000000000000',
          isEmpty: true,
          isZero: true
        });
      }
    }

    return merkleRoots;
  }

  /**
   * Test if claim functions are available (even with empty merkle root)
   */
  private async testClaimAvailability(contract: ContractInfo, provider: ethers.JsonRpcProvider): Promise<boolean> {
    if (!contract.abi) return false;

    try {
      const abi = JSON.parse(contract.abi);
      const claimFunctions = abi.filter((item: any) => 
        item.type === 'function' && 
        (item.name?.includes('claim') || item.name?.includes('withdraw'))
      );

      if (claimFunctions.length === 0) return false;

      // Try to call claim functions with test parameters
      const contractInstance = new ethers.Contract(contract.address, abi, provider);
      
      for (const claimFunc of claimFunctions) {
        try {
          // Generate test parameters based on function signature
          const testParams = this.generateTestClaimParameters(claimFunc);
          
          // Use staticCall to test without sending transaction
          await contractInstance[claimFunc.name].staticCall(...testParams);
          
          // If we get here without reverting, claim might be available
          return true;
        } catch (error) {
          // Expected to fail, but we're testing if the function exists and is callable
          const errorMessage = error.toString().toLowerCase();
          
          // If error is about invalid proof/amount, function is available
          if (errorMessage.includes('invalid') || 
              errorMessage.includes('proof') || 
              errorMessage.includes('amount')) {
            return true;
          }
        }
      }
    } catch (error) {
      logger.debug(`Error testing claim availability for ${contract.address}`);
    }

    return false;
  }

  /**
   * Generate test parameters for claim functions
   */
  private generateTestClaimParameters(claimFunction: any): any[] {
    const params: any[] = [];
    
    for (const input of claimFunction.inputs || []) {
      switch (input.type) {
        case 'address':
          params.push('******************************************');
          break;
        case 'uint256':
          params.push('1000000000000000000'); // 1 ETH
          break;
        case 'bytes32[]':
          params.push(['0x0000000000000000000000000000000000000000000000000000000000000001']);
          break;
        case 'bytes32':
          params.push('0x0000000000000000000000000000000000000000000000000000000000000001');
          break;
        default:
          params.push('0');
      }
    }
    
    return params;
  }

  /**
   * Calculate priority based on analysis
   */
  private calculatePriority(
    contract: ContractInfo, 
    isEmpty: boolean, 
    isZero: boolean, 
    claimAvailable: boolean
  ): number {
    let priority = 5; // Default low priority

    // High priority if merkle root is empty/zero
    if (isEmpty || isZero) {
      priority = 2;
    }

    // Highest priority if claim is available with empty merkle root
    if ((isEmpty || isZero) && claimAvailable) {
      priority = 1;
    }

    // Bonus for recent deployment
    const ageHours = (Date.now() / 1000 - contract.timestamp) / 3600;
    if (ageHours <= 24) {
      priority = Math.max(1, priority - 1);
    }

    return priority;
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(
    contract: ContractInfo, 
    merkleRoots: MerkleRootInfo[], 
    claimAvailable: boolean
  ): number {
    let confidence = 0;

    // Base confidence for finding merkle roots
    confidence += merkleRoots.length * 20;

    // High confidence if all roots are empty/zero
    if (merkleRoots.every(root => root.isEmpty || root.isZero)) {
      confidence += 40;
    }

    // High confidence if claim functions exist
    if (claimAvailable) {
      confidence += 30;
    }

    // Bonus for airdrop keywords in contract
    const contractText = `${contract.name} ${contract.sourceCode || ''}`.toLowerCase();
    const airdropKeywords = ['airdrop', 'claim', 'merkle', 'distribution'];
    const foundKeywords = airdropKeywords.filter(k => contractText.includes(k));
    confidence += foundKeywords.length * 5;

    return Math.min(confidence, 100);
  }

  /**
   * Check if value is empty
   */
  private isEmptyValue(value: string): boolean {
    return this.zeroValues.includes(value.toLowerCase());
  }

  /**
   * Check if value is zero
   */
  private isZeroValue(value: string): boolean {
    return value === '0x0000000000000000000000000000000000000000000000000000000000000000' ||
           value === '0x' ||
           value === '' ||
           value === '0';
  }

  /**
   * Check if value looks like an address
   */
  private looksLikeAddress(value: string): boolean {
    return value.length === 42 && value.startsWith('0x') && 
           value.slice(2, 26) === '000000000000000000000000';
  }

  /**
   * Find all pre-proof contracts
   */
  async findPreProofContracts(contracts: ContractInfo[]): Promise<PreProofContract[]> {
    const preProofContracts: PreProofContract[] = [];
    
    logger.info(`🔍 Analyzing ${contracts.length} contracts for empty merkle roots...`);

    for (const contract of contracts) {
      const analysis = await this.analyzeContract(contract);
      
      if (analysis && (analysis.isEmpty || analysis.isZero) && analysis.confidence >= 50) {
        const timeWindow = (Date.now() / 1000 - contract.timestamp) / 3600;
        const actionRequired = analysis.claimAvailable && timeWindow <= 48;
        const estimatedTimeLeft = Math.max(0, 72 - timeWindow); // Assume proofs added within 72 hours

        preProofContracts.push({
          contract,
          analysis,
          timeWindow,
          actionRequired,
          estimatedTimeLeft
        });
      }
    }

    // Sort by priority and time window
    preProofContracts.sort((a, b) => {
      if (a.analysis.priority !== b.analysis.priority) {
        return a.analysis.priority - b.analysis.priority;
      }
      return a.timeWindow - b.timeWindow; // Earlier deployments first
    });

    if (preProofContracts.length > 0) {
      logger.extract(`🎯 Found ${preProofContracts.length} pre-proof contracts`);
    }

    return preProofContracts;
  }

  /**
   * Monitor contract for merkle root changes
   */
  async monitorMerkleRootChanges(
    contractAddress: string, 
    chainId: number,
    callback: (analysis: MerkleRootAnalysis) => void
  ): Promise<void> {
    const provider = this.providers.get(chainId);
    if (!provider) return;

    logger.info(`🔄 Monitoring ${contractAddress} for merkle root changes...`);

    // Set up event listener for storage changes (if supported)
    // For now, we'll poll every 5 minutes
    const pollInterval = 5 * 60 * 1000; // 5 minutes

    const poll = async () => {
      try {
        // Create minimal contract info for analysis
        const contract: ContractInfo = {
          address: contractAddress,
          name: 'MonitoredContract',
          abi: '',
          sourceCode: '',
          compiler: '',
          txHash: '',
          blockNumber: 0,
          timestamp: 0,
          chainId
        };

        const analysis = await this.analyzeContract(contract);
        if (analysis) {
          callback(analysis);
        }
      } catch (error) {
        logger.error(`Error monitoring ${contractAddress}:`, error);
      }
    };

    // Initial check
    await poll();
    
    // Set up polling
    setInterval(poll, pollInterval);
  }
}
