import { logger } from './logger';
import { ContractInfo, ChainConfig } from './types';
import { CONFIG } from './config';

export interface RSSItem {
  title: string;
  link: string;
  description: string;
  pubDate: string;
  guid: string;
}

export interface EtherscanRSSFeed {
  title: string;
  link: string;
  description: string;
  items: RSSItem[];
  lastBuildDate: string;
}

export interface FreshContract {
  address: string;
  name: string;
  chainId: number;
  verificationTime: Date;
  priority: number;
  airdropPotential: number;
  keywords: string[];
}

export class EtherscanRSSMonitor {
  private readonly rssEndpoints: Map<number, string> = new Map([
    [1, 'https://etherscan.io/contractsVerified'],
    [42161, 'https://arbiscan.io/contractsVerified'],
    [8453, 'https://basescan.org/contractsVerified'],
    [10, 'https://optimistic.etherscan.io/contractsVerified'],
    [137, 'https://polygonscan.com/contractsVerified']
  ]);

  private readonly airdropKeywords = [
    'airdrop', 'claim', 'merkle', 'distribution', 'token',
    'governance', 'dao', 'voting', 'rewards', 'incentive',
    'retroactive', 'snapshot', 'eligibility', 'whitelist',
    'vesting', 'unlock', 'allocate', 'mint'
  ];

  private readonly highPriorityKeywords = [
    'airdrop', 'claim', 'merkle', 'distribution'
  ];

  private readonly protocolKeywords = [
    'uniswap', 'aave', 'compound', 'curve', 'balancer',
    'sushiswap', 'yearn', 'makerdao', 'eigenlayer',
    'lido', 'rocket', 'frax', 'convex'
  ];

  /**
   * Monitor all chains for newly verified contracts
   */
  async monitorAllChains(): Promise<FreshContract[]> {
    const freshContracts: FreshContract[] = [];
    
    logger.info('🔍 Monitoring Etherscan RSS feeds for fresh contracts...');

    for (const chain of CONFIG.chains) {
      if (!this.rssEndpoints.has(chain.chainId)) {
        continue;
      }

      try {
        const contracts = await this.monitorChainRSS(chain);
        freshContracts.push(...contracts);
        
        // Rate limiting between chains
        await this.sleep(1000);
      } catch (error) {
        logger.error(`Error monitoring RSS for chain ${chain.name}:`, error);
      }
    }

    // Sort by priority and verification time
    return freshContracts.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority; // Lower priority number = higher priority
      }
      return b.verificationTime.getTime() - a.verificationTime.getTime(); // Newer first
    });
  }

  /**
   * Monitor RSS feed for a specific chain
   */
  async monitorChainRSS(chain: ChainConfig): Promise<FreshContract[]> {
    const rssUrl = this.rssEndpoints.get(chain.chainId);
    if (!rssUrl) {
      logger.warn(`No RSS endpoint configured for chain ${chain.name}`);
      return [];
    }

    try {
      // For now, we'll use a simulated RSS feed since Etherscan doesn't provide RSS
      // In production, you'd integrate with a proper RSS parser or web scraper
      const freshContracts = await this.fetchFreshContractsFromAPI(chain);
      
      logger.info(`Found ${freshContracts.length} fresh contracts on ${chain.name}`);
      return freshContracts;
    } catch (error) {
      logger.error(`Error fetching RSS for ${chain.name}:`, error);
      return [];
    }
  }

  /**
   * Fetch fresh contracts using Etherscan API (since RSS isn't available)
   */
  private async fetchFreshContractsFromAPI(chain: ChainConfig): Promise<FreshContract[]> {
    if (!chain.etherscanApiKey) {
      logger.warn(`No Etherscan API key for chain ${chain.name}`);
      return [];
    }

    const freshContracts: FreshContract[] = [];
    
    try {
      // Get recently verified contracts (last 24 hours)
      const oneDayAgo = Math.floor((Date.now() - 24 * 60 * 60 * 1000) / 1000);
      const now = Math.floor(Date.now() / 1000);

      // Use contract creation API to find recent contracts
      const url = `${chain.etherscanApiUrl}/api?module=account&action=txlist&address=******************************************&startblock=0&endblock=latest&page=1&offset=100&sort=desc&apikey=${chain.etherscanApiKey}`;
      
      // For demonstration, we'll simulate fresh contract detection
      // In production, you'd implement proper Etherscan API integration
      const simulatedContracts = await this.simulateFreshContractDetection(chain);
      
      for (const contract of simulatedContracts) {
        const freshContract = await this.analyzeContractForAirdrop(contract, chain);
        if (freshContract) {
          freshContracts.push(freshContract);
        }
      }

    } catch (error) {
      logger.error(`Error fetching fresh contracts for ${chain.name}:`, error);
    }

    return freshContracts;
  }

  /**
   * Simulate fresh contract detection (replace with real implementation)
   */
  private async simulateFreshContractDetection(chain: ChainConfig): Promise<any[]> {
    // This is a simulation - in production you'd implement real RSS/API parsing
    const simulatedContracts = [
      {
        address: '0x' + Math.random().toString(16).substr(2, 40),
        name: 'TokenDistribution',
        sourceCode: 'contract TokenDistribution { function claim() external { } }',
        verificationTime: new Date()
      },
      {
        address: '0x' + Math.random().toString(16).substr(2, 40),
        name: 'MerkleAirdrop',
        sourceCode: 'contract MerkleAirdrop { function claim(bytes32[] proof) external { } }',
        verificationTime: new Date()
      }
    ];

    return simulatedContracts;
  }

  /**
   * Analyze a contract to determine airdrop potential
   */
  private async analyzeContractForAirdrop(contract: any, chain: ChainConfig): Promise<FreshContract | null> {
    const contractText = `${contract.name} ${contract.sourceCode || ''}`.toLowerCase();
    
    // Check for airdrop keywords
    const foundKeywords = this.airdropKeywords.filter(keyword => 
      contractText.includes(keyword)
    );

    if (foundKeywords.length === 0) {
      return null; // No airdrop potential
    }

    // Calculate airdrop potential score (0-100)
    const airdropPotential = this.calculateAirdropPotential(contractText, foundKeywords);
    
    // Calculate priority (1-5, lower is higher priority)
    const priority = this.calculatePriority(contractText, foundKeywords);

    return {
      address: contract.address,
      name: contract.name,
      chainId: chain.chainId,
      verificationTime: new Date(contract.verificationTime),
      priority,
      airdropPotential,
      keywords: foundKeywords
    };
  }

  /**
   * Calculate airdrop potential score (0-100)
   */
  private calculateAirdropPotential(contractText: string, keywords: string[]): number {
    let score = 0;

    // Base score for having airdrop keywords
    score += keywords.length * 10;

    // Bonus for high-priority keywords
    const highPriorityFound = keywords.filter(k => this.highPriorityKeywords.includes(k));
    score += highPriorityFound.length * 20;

    // Bonus for protocol keywords
    const protocolFound = this.protocolKeywords.filter(p => contractText.includes(p));
    score += protocolFound.length * 15;

    // Bonus for specific function patterns
    if (contractText.includes('claim(')) score += 25;
    if (contractText.includes('merkle')) score += 20;
    if (contractText.includes('bytes32[]')) score += 15; // Merkle proof parameter

    return Math.min(score, 100);
  }

  /**
   * Calculate priority (1-5, lower is higher priority)
   */
  private calculatePriority(contractText: string, keywords: string[]): number {
    // Start with default priority
    let priority = 5;

    // High priority for direct airdrop keywords
    if (keywords.some(k => this.highPriorityKeywords.includes(k))) {
      priority = 1;
    }

    // High priority for known protocols
    if (this.protocolKeywords.some(p => contractText.includes(p))) {
      priority = Math.min(priority, 2);
    }

    // Medium priority for governance/DAO keywords
    if (contractText.includes('governance') || contractText.includes('dao')) {
      priority = Math.min(priority, 3);
    }

    // Medium priority for token-related keywords
    if (contractText.includes('token') || contractText.includes('rewards')) {
      priority = Math.min(priority, 4);
    }

    return priority;
  }

  /**
   * Get contracts verified in the last N hours
   */
  async getRecentlyVerifiedContracts(hours: number = 24): Promise<FreshContract[]> {
    const allContracts = await this.monitorAllChains();
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    return allContracts.filter(contract => 
      contract.verificationTime > cutoffTime
    );
  }

  /**
   * Get high-priority airdrop candidates
   */
  async getHighPriorityAirdropCandidates(): Promise<FreshContract[]> {
    const allContracts = await this.monitorAllChains();
    
    return allContracts.filter(contract => 
      contract.priority <= 2 && contract.airdropPotential >= 50
    );
  }

  /**
   * Monitor for contracts with specific keywords
   */
  async monitorForKeywords(keywords: string[]): Promise<FreshContract[]> {
    const allContracts = await this.monitorAllChains();
    
    return allContracts.filter(contract =>
      keywords.some(keyword => 
        contract.keywords.includes(keyword.toLowerCase())
      )
    );
  }

  /**
   * Get contracts from specific protocols
   */
  async getProtocolContracts(protocolNames: string[]): Promise<FreshContract[]> {
    const allContracts = await this.monitorAllChains();
    
    return allContracts.filter(contract =>
      protocolNames.some(protocol =>
        contract.name.toLowerCase().includes(protocol.toLowerCase())
      )
    );
  }

  /**
   * Start continuous monitoring with callback
   */
  async startContinuousMonitoring(
    callback: (contracts: FreshContract[]) => void,
    intervalMinutes: number = 30
  ): Promise<void> {
    logger.info(`🔄 Starting continuous RSS monitoring (${intervalMinutes} minute intervals)`);
    
    const monitor = async () => {
      try {
        const freshContracts = await this.getRecentlyVerifiedContracts(1); // Last hour
        if (freshContracts.length > 0) {
          logger.extract(`🎯 Found ${freshContracts.length} fresh contracts with airdrop potential`);
          callback(freshContracts);
        }
      } catch (error) {
        logger.error('Error in continuous monitoring:', error);
      }
    };

    // Initial scan
    await monitor();
    
    // Set up interval
    setInterval(monitor, intervalMinutes * 60 * 1000);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
