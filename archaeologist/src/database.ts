import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { ContractInfo, SimulationResult, ExploitableContract, ExecutionResult, ScanStatus } from './types';
import { CONFIG } from './config';
import { logger } from './logger';
import * as fs from 'fs';
import * as path from 'path';

export class Database {
  private db: sqlite3.Database;
  private ready: boolean = false;

  constructor() {
    // Ensure data directory exists
    const dataDir = path.dirname(CONFIG.dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    this.db = new sqlite3.Database(CONFIG.dbPath, (err) => {
      if (err) {
        logger.error('Database connection failed:', err);
        throw err;
      }
      logger.info('Database connected successfully');
    });

    this.initializeTables();
  }

  private async initializeTables(): Promise<void> {
    const createTables = [
      `CREATE TABLE IF NOT EXISTS contracts (
        address TEXT PRIMARY KEY,
        name TEXT,
        abi TEXT,
        source_code TEXT,
        compiler TEXT,
        tx_hash TEXT,
        block_number INTEGER,
        timestamp INTEGER,
        chain_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      `CREATE TABLE IF NOT EXISTS simulations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contract_address TEXT,
        function_name TEXT,
        signature TEXT,
        success BOOLEAN,
        return_data TEXT,
        gas_estimate INTEGER,
        error TEXT,
        potential_value TEXT,
        timestamp INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contract_address) REFERENCES contracts(address)
      )`,
      
      `CREATE TABLE IF NOT EXISTS exploitable_contracts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        address TEXT,
        function_name TEXT,
        signature TEXT,
        estimated_value TEXT,
        gas_estimate INTEGER,
        priority INTEGER,
        chain_id INTEGER,
        discovered DATETIME,
        executed BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      `CREATE TABLE IF NOT EXISTS executions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contract_address TEXT,
        function_name TEXT,
        tx_hash TEXT,
        success BOOLEAN,
        gas_used INTEGER,
        value TEXT,
        timestamp INTEGER,
        error TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      `CREATE TABLE IF NOT EXISTS fuzzing_results (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contract_address TEXT,
        strategy TEXT,
        parameters TEXT,
        success BOOLEAN,
        return_data TEXT,
        gas_estimate INTEGER,
        risk_score INTEGER,
        potential_exploit BOOLEAN,
        timestamp INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contract_address) REFERENCES contracts(address)
      )`,
      
      `CREATE TABLE IF NOT EXISTS scan_status (
        chain_id INTEGER PRIMARY KEY,
        last_processed_block INTEGER,
        last_scan_time DATETIME,
        contracts_scanned INTEGER DEFAULT 0,
        exploits_found INTEGER DEFAULT 0,
        total_value TEXT DEFAULT '0',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 🏺 DUST COLLECTION: Create extractions table for compound accumulation
      `CREATE TABLE IF NOT EXISTS extractions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contract_address TEXT NOT NULL,
        function_name TEXT NOT NULL,
        extracted_value TEXT NOT NULL,
        gas_cost TEXT NOT NULL,
        net_profit TEXT NOT NULL,
        tx_hash TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 🏺 DUST COLLECTION: Create target_funding table for compound tracking
      `CREATE TABLE IF NOT EXISTS target_funding (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        description TEXT NOT NULL,
        amount TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 🏺 DUST COLLECTION: Batch optimization tracking
      `CREATE TABLE IF NOT EXISTS batch_optimizations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        batch_size INTEGER NOT NULL,
        total_value TEXT NOT NULL,
        total_gas INTEGER NOT NULL,
        net_profit TEXT NOT NULL,
        gas_saved INTEGER NOT NULL,
        efficiency_ratio REAL NOT NULL,
        tx_hash TEXT,
        chain_id INTEGER NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 🏺 DUST COLLECTION: Performance metrics
      `CREATE TABLE IF NOT EXISTS performance_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        metric_type TEXT NOT NULL,
        metric_value REAL NOT NULL,
        metric_data TEXT,
        chain_id INTEGER,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      `CREATE INDEX IF NOT EXISTS idx_contracts_chain ON contracts(chain_id)`,
      `CREATE INDEX IF NOT EXISTS idx_contracts_block ON contracts(block_number)`,
      `CREATE INDEX IF NOT EXISTS idx_simulations_contract ON simulations(contract_address)`,
      `CREATE INDEX IF NOT EXISTS idx_simulations_success ON simulations(success)`,
      `CREATE INDEX IF NOT EXISTS idx_exploitable_executed ON exploitable_contracts(executed)`,
      `CREATE INDEX IF NOT EXISTS idx_exploitable_priority ON exploitable_contracts(priority)`,
      `CREATE INDEX IF NOT EXISTS idx_executions_success ON executions(success)`
    ];

    for (const sql of createTables) {
      await this.run(sql);
    }
    
    this.ready = true;
    logger.info('Database tables initialized');
  }

  private run(sql: string, params: any[] = []): Promise<sqlite3.RunResult> {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) reject(err);
        else resolve(this);
      });
    });
  }

  private get(sql: string, params: any[] = []): Promise<any> {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  private all(sql: string, params: any[] = []): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  async saveContract(contract: ContractInfo): Promise<void> {
    const sql = `INSERT OR REPLACE INTO contracts 
      (address, name, abi, source_code, compiler, tx_hash, block_number, timestamp, chain_id) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    await this.run(sql, [
      contract.address,
      contract.name,
      JSON.stringify(contract.abi),
      contract.sourceCode,
      contract.compiler,
      contract.txHash,
      contract.blockNumber,
      contract.timestamp,
      contract.chainId
    ]);
  }

  async saveSimulation(simulation: SimulationResult): Promise<void> {
    const sql = `INSERT INTO simulations 
      (contract_address, function_name, signature, success, return_data, gas_estimate, error, potential_value, timestamp) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    await this.run(sql, [
      simulation.contractAddress,
      simulation.functionName,
      simulation.signature,
      simulation.success,
      simulation.returnData,
      simulation.gasEstimate,
      simulation.error,
      simulation.potentialValue,
      simulation.timestamp
    ]);
  }

  async saveExploitableContract(exploit: ExploitableContract): Promise<void> {
    const sql = `INSERT INTO exploitable_contracts 
      (address, function_name, signature, estimated_value, gas_estimate, priority, chain_id, discovered) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
    
    await this.run(sql, [
      exploit.address,
      exploit.functionName,
      exploit.signature,
      exploit.estimatedValue,
      exploit.gasEstimate,
      exploit.priority,
      exploit.chainId,
      exploit.discovered.toISOString()
    ]);
  }

  async saveExecution(execution: ExecutionResult): Promise<void> {
    const sql = `INSERT INTO executions 
      (contract_address, function_name, tx_hash, success, gas_used, value, timestamp, error) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
    
    await this.run(sql, [
      execution.contractAddress,
      execution.functionName,
      execution.txHash,
      execution.success,
      execution.gasUsed,
      execution.value,
      execution.timestamp,
      execution.error
    ]);
  }

  async saveFuzzingResult(fuzzingResult: {
    contractAddress: string;
    strategy: string;
    parameters: string;
    success: boolean;
    returnData: string;
    gasEstimate: number;
    riskScore: number;
    potentialExploit: boolean;
    timestamp: number;
  }): Promise<void> {
    const sql = `INSERT INTO fuzzing_results 
      (contract_address, strategy, parameters, success, return_data, gas_estimate, risk_score, potential_exploit, timestamp) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    await this.run(sql, [
      fuzzingResult.contractAddress,
      fuzzingResult.strategy,
      fuzzingResult.parameters,
      fuzzingResult.success,
      fuzzingResult.returnData,
      fuzzingResult.gasEstimate,
      fuzzingResult.riskScore,
      fuzzingResult.potentialExploit,
      fuzzingResult.timestamp
    ]);
  }

  async getFuzzingResults(contractAddress?: string, limit: number = 100): Promise<any[]> {
    let sql = `SELECT * FROM fuzzing_results`;
    const params: any[] = [];
    
    if (contractAddress) {
      sql += ` WHERE contract_address = ?`;
      params.push(contractAddress);
    }
    
    sql += ` ORDER BY risk_score DESC, timestamp DESC LIMIT ?`;
    params.push(limit);
    
    return await this.all(sql, params);
  }

  async markExploitExecuted(address: string, functionName: string): Promise<void> {
    const sql = `UPDATE exploitable_contracts SET executed = TRUE WHERE address = ? AND function_name = ?`;
    await this.run(sql, [address, functionName]);
  }

  async updateScanStatus(status: ScanStatus): Promise<void> {
    const sql = `INSERT OR REPLACE INTO scan_status 
      (chain_id, last_processed_block, last_scan_time, contracts_scanned, exploits_found, total_value, updated_at) 
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;
    
    await this.run(sql, [
      status.chainId,
      status.lastProcessedBlock,
      status.lastScanTime.toISOString(),
      status.contractsScanned,
      status.exploitsFound,
      status.totalValue
    ]);
  }

  async getScanStatus(chainId: number): Promise<ScanStatus | null> {
    const sql = `SELECT * FROM scan_status WHERE chain_id = ?`;
    const row = await this.get(sql, [chainId]);
    
    if (!row) return null;
    
    return {
      chainId: row.chain_id,
      lastProcessedBlock: row.last_processed_block,
      lastScanTime: new Date(row.last_scan_time),
      contractsScanned: row.contracts_scanned,
      exploitsFound: row.exploits_found,
      totalValue: row.total_value
    };
  }

  async getUnexecutedExploits(): Promise<ExploitableContract[]> {
    const sql = `SELECT * FROM exploitable_contracts WHERE executed = FALSE ORDER BY priority ASC, estimated_value DESC`;
    const rows = await this.all(sql);
    
    return rows.map(row => ({
      address: row.address,
      functionName: row.function_name,
      signature: row.signature,
      estimatedValue: row.estimated_value,
      gasEstimate: row.gas_estimate,
      priority: row.priority,
      chainId: row.chain_id,
      discovered: new Date(row.discovered),
      executed: row.executed
    }));
  }

  async hasBeenSimulated(contractAddress: string, functionName: string): Promise<boolean> {
    const sql = `SELECT COUNT(*) as count FROM simulations WHERE contract_address = ? AND function_name = ?`;
    const row = await this.get(sql, [contractAddress, functionName]);
    return row.count > 0;
  }

  async getContractsByChain(chainId: number, limit: number = 100): Promise<ContractInfo[]> {
    const sql = `SELECT * FROM contracts WHERE chain_id = ? ORDER BY block_number DESC LIMIT ?`;
    const rows = await this.all(sql, [chainId, limit]);
    
    return rows.map(row => ({
      address: row.address,
      name: row.name,
      abi: JSON.parse(row.abi),
      sourceCode: row.source_code,
      compiler: row.compiler,
      txHash: row.tx_hash,
      blockNumber: row.block_number,
      timestamp: row.timestamp,
      chainId: row.chain_id
    }));
  }

  async getStats(): Promise<any> {
    const stats = await Promise.all([
      this.get('SELECT COUNT(*) as total FROM contracts'),
      this.get('SELECT COUNT(*) as total FROM simulations WHERE success = TRUE'),
      this.get('SELECT COUNT(*) as total FROM exploitable_contracts'),
      this.get('SELECT COUNT(*) as total FROM executions WHERE success = TRUE'),
      this.get('SELECT SUM(CAST(value as REAL)) as total FROM executions WHERE success = TRUE')
    ]);

    return {
      totalContracts: stats[0].total,
      successfulSimulations: stats[1].total,
      exploitableContracts: stats[2].total,
      successfulExecutions: stats[3].total,
      totalValueExtracted: stats[4].total || 0
    };
  }

  // 🏺 DUST COLLECTION: Save successful extraction for compound accumulation
  async saveExtraction(extraction: {
    contractAddress: string;
    functionName: string;
    extractedValue: bigint;
    gasCost: bigint;
    netProfit: bigint;
    txHash: string;
    timestamp: Date;
  }): Promise<void> {
    const sql = `INSERT INTO extractions
      (contract_address, function_name, extracted_value, gas_cost, net_profit, tx_hash, timestamp)
      VALUES (?, ?, ?, ?, ?, ?, ?)`;

    await this.run(sql, [
      extraction.contractAddress,
      extraction.functionName,
      extraction.extractedValue.toString(),
      extraction.gasCost.toString(),
      extraction.netProfit.toString(),
      extraction.txHash,
      extraction.timestamp.toISOString()
    ]);
  }

  // 🏺 DUST COLLECTION: Get all extractions for accumulation analysis
  async getExtractions(): Promise<Array<{
    contractAddress: string;
    functionName: string;
    extractedValue: bigint;
    gasCost: bigint;
    netProfit: bigint;
    txHash: string;
    timestamp: Date;
  }>> {
    const sql = `SELECT * FROM extractions ORDER BY timestamp DESC`;
    const rows = await this.all(sql);

    return rows.map(row => ({
      contractAddress: row.contract_address,
      functionName: row.function_name,
      extractedValue: BigInt(row.extracted_value),
      gasCost: BigInt(row.gas_cost),
      netProfit: BigInt(row.net_profit),
      txHash: row.tx_hash,
      timestamp: new Date(row.timestamp)
    }));
  }

  // 🏺 DUST COLLECTION: Save target funding for compound tracking
  async saveTargetFunding(funding: {
    type: string;
    description: string;
    amount: bigint;
    timestamp: Date;
  }): Promise<void> {
    const sql = `INSERT INTO target_funding (type, description, amount, timestamp) VALUES (?, ?, ?, ?)`;

    await this.run(sql, [
      funding.type,
      funding.description,
      funding.amount.toString(),
      funding.timestamp.toISOString()
    ]);
  }

  // 🏺 DUST COLLECTION: Get target funding records
  async getTargetFunding(): Promise<any[]> {
    return this.all('SELECT * FROM target_funding ORDER BY timestamp DESC');
  }

  // 🏺 DUST COLLECTION: Save batch optimization record
  async saveBatchOptimization(optimization: {
    batchSize: number;
    totalValue: string;
    totalGas: number;
    netProfit: string;
    gasSaved: number;
    efficiencyRatio: number;
    txHash?: string;
    chainId: number;
  }): Promise<void> {
    await this.run(
      `INSERT INTO batch_optimizations
       (batch_size, total_value, total_gas, net_profit, gas_saved, efficiency_ratio, tx_hash, chain_id)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        optimization.batchSize,
        optimization.totalValue,
        optimization.totalGas,
        optimization.netProfit,
        optimization.gasSaved,
        optimization.efficiencyRatio,
        optimization.txHash || null,
        optimization.chainId
      ]
    );
  }

  // 🏺 DUST COLLECTION: Save performance metric
  async savePerformanceMetric(metric: {
    type: string;
    value: number;
    data?: string;
    chainId?: number;
  }): Promise<void> {
    await this.run(
      `INSERT INTO performance_metrics (metric_type, metric_value, metric_data, chain_id)
       VALUES (?, ?, ?, ?)`,
      [metric.type, metric.value, metric.data || null, metric.chainId || null]
    );
  }

  async close(): Promise<void> {
    return new Promise((resolve) => {
      this.db.close((err) => {
        if (err) {
          logger.error('Database close error:', err);
        }
        resolve();
      });
    });
  }
}

export const db = new Database();
