import { logger } from './logger';
import { ContractInfo, ChainConfig } from './types';
import { Database } from './database';
import { CONFIG } from './config';

export interface TrackedProtocol {
  id: string;
  name: string;
  description: string;
  githubRepo?: string;
  website?: string;
  knownAddresses: Map<number, string[]>; // chainId -> addresses
  airdropHistory: AirdropEvent[];
  priority: number; // 1-5, lower = higher priority
  lastChecked: Date;
  isActive: boolean;
}

export interface AirdropEvent {
  protocol: string;
  eventType: 'airdrop' | 'claim' | 'distribution' | 'governance';
  contractAddress: string;
  chainId: number;
  timestamp: Date;
  description: string;
  claimable: boolean;
  estimatedValue?: string;
}

export interface ProtocolDeploymentAlert {
  protocol: TrackedProtocol;
  newContracts: ContractInfo[];
  alertType: 'new_deployment' | 'airdrop_potential' | 'claim_available';
  priority: number;
  timestamp: Date;
  actionRequired: boolean;
}

export class ProtocolTracker {
  private readonly database: Database;
  private readonly trackedProtocols: Map<string, TrackedProtocol> = new Map();

  constructor() {
    this.database = new Database();
    this.initializeTrackedProtocols();
  }

  /**
   * Initialize list of protocols to track for airdrop potential
   */
  private initializeTrackedProtocols(): void {
    const protocols: TrackedProtocol[] = [
      {
        id: 'uniswap',
        name: 'Uniswap',
        description: 'Leading DEX protocol with history of major airdrops',
        githubRepo: 'Uniswap/v4-core',
        website: 'https://uniswap.org',
        knownAddresses: new Map([
          [1, ['0x1f9840a85d5af5bf1d1762f925bdaddc4201f984']], // UNI token
          [42161, ['0xfa7f8980b0f1e64a2062791cc3b0871572f1f7f0']], // Arbitrum
          [8453, ['0x4200000000000000000000000000000000000006']] // Base
        ]),
        airdropHistory: [
          {
            protocol: 'uniswap',
            eventType: 'airdrop',
            contractAddress: '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984',
            chainId: 1,
            timestamp: new Date('2020-09-17'),
            description: 'UNI governance token airdrop',
            claimable: false
          }
        ],
        priority: 1,
        lastChecked: new Date(),
        isActive: true
      },
      {
        id: 'eigenlayer',
        name: 'EigenLayer',
        description: 'Restaking protocol with high airdrop potential',
        githubRepo: 'Layr-Labs/eigenlayer-contracts',
        website: 'https://eigenlayer.xyz',
        knownAddresses: new Map([
          [1, ['0x858646372cc42e1a627fce94aa7a7033e7cf075a']] // Strategy Manager
        ]),
        airdropHistory: [],
        priority: 1,
        lastChecked: new Date(),
        isActive: true
      },
      {
        id: 'aave',
        name: 'Aave',
        description: 'Leading lending protocol',
        githubRepo: 'aave/aave-v3-core',
        website: 'https://aave.com',
        knownAddresses: new Map([
          [1, ['0x7d2768de32b0b80b7a3454c06bdac94a69ddc7a9']], // Lending Pool
          [42161, ['0x794a61358d6845594f94dc1db02a252b5b4814ad']], // Arbitrum
          [137, ['0x8dff5e27ea6b7ac08ebfdf9eb090f32ee9a30fcf']] // Polygon
        ]),
        airdropHistory: [],
        priority: 2,
        lastChecked: new Date(),
        isActive: true
      },
      {
        id: 'compound',
        name: 'Compound',
        description: 'Decentralized lending protocol',
        githubRepo: 'compound-finance/compound-protocol',
        website: 'https://compound.finance',
        knownAddresses: new Map([
          [1, ['0x3d9819210a31b4961b30ef54be2aed79b9c9cd3b']] // Comptroller
        ]),
        airdropHistory: [
          {
            protocol: 'compound',
            eventType: 'airdrop',
            contractAddress: '0xc00e94cb662c3520282e6f5717214004a7f26888',
            chainId: 1,
            timestamp: new Date('2020-06-15'),
            description: 'COMP governance token distribution',
            claimable: false
          }
        ],
        priority: 2,
        lastChecked: new Date(),
        isActive: true
      },
      {
        id: 'arbitrum',
        name: 'Arbitrum',
        description: 'Layer 2 scaling solution',
        githubRepo: 'OffchainLabs/arbitrum',
        website: 'https://arbitrum.io',
        knownAddresses: new Map([
          [42161, ['0x912ce59144191c1204e64559fe8253a0e49e6548']] // ARB token
        ]),
        airdropHistory: [
          {
            protocol: 'arbitrum',
            eventType: 'airdrop',
            contractAddress: '******************************************',
            chainId: 42161,
            timestamp: new Date('2023-03-23'),
            description: 'ARB governance token airdrop',
            claimable: false
          }
        ],
        priority: 1,
        lastChecked: new Date(),
        isActive: true
      },
      {
        id: 'optimism',
        name: 'Optimism',
        description: 'Layer 2 scaling solution',
        githubRepo: 'ethereum-optimism/optimism',
        website: 'https://optimism.io',
        knownAddresses: new Map([
          [10, ['******************************************']] // OP token
        ]),
        airdropHistory: [
          {
            protocol: 'optimism',
            eventType: 'airdrop',
            contractAddress: '******************************************',
            chainId: 10,
            timestamp: new Date('2022-05-31'),
            description: 'OP governance token airdrop #1',
            claimable: false
          }
        ],
        priority: 1,
        lastChecked: new Date(),
        isActive: true
      }
    ];

    // Add protocols to tracking map
    for (const protocol of protocols) {
      this.trackedProtocols.set(protocol.id, protocol);
    }

    logger.info(`📋 Initialized tracking for ${protocols.length} high-priority protocols`);
  }

  /**
   * Check all tracked protocols for new deployments
   */
  async checkAllProtocolsForDeployments(): Promise<ProtocolDeploymentAlert[]> {
    const alerts: ProtocolDeploymentAlert[] = [];
    
    logger.info('🔍 Checking all tracked protocols for new deployments...');

    for (const [protocolId, protocol] of this.trackedProtocols) {
      if (!protocol.isActive) continue;

      try {
        const protocolAlerts = await this.checkProtocolForDeployments(protocol);
        alerts.push(...protocolAlerts);
        
        // Update last checked time
        protocol.lastChecked = new Date();
        
        // Rate limiting
        await this.sleep(500);
      } catch (error) {
        logger.error(`Error checking protocol ${protocolId}:`, error);
      }
    }

    // Sort alerts by priority
    alerts.sort((a, b) => a.priority - b.priority);

    if (alerts.length > 0) {
      logger.extract(`🚨 Found ${alerts.length} protocol deployment alerts`);
    }

    return alerts;
  }

  /**
   * Check specific protocol for new deployments
   */
  async checkProtocolForDeployments(protocol: TrackedProtocol): Promise<ProtocolDeploymentAlert[]> {
    const alerts: ProtocolDeploymentAlert[] = [];
    
    // Check each chain for new contracts from this protocol
    for (const chain of CONFIG.chains) {
      try {
        const newContracts = await this.findNewProtocolContracts(protocol, chain);
        
        if (newContracts.length > 0) {
          const alert: ProtocolDeploymentAlert = {
            protocol,
            newContracts,
            alertType: this.determineAlertType(newContracts),
            priority: protocol.priority,
            timestamp: new Date(),
            actionRequired: this.requiresImmediateAction(newContracts)
          };
          
          alerts.push(alert);
          
          logger.extract(`🎯 ${protocol.name}: Found ${newContracts.length} new contracts on ${chain.name}`);
        }
      } catch (error) {
        logger.debug(`Error checking ${protocol.name} on ${chain.name}:`, error);
      }
    }

    return alerts;
  }

  /**
   * Find new contracts deployed by a protocol
   */
  private async findNewProtocolContracts(protocol: TrackedProtocol, chain: ChainConfig): Promise<ContractInfo[]> {
    const newContracts: ContractInfo[] = [];
    
    // Get known addresses for this chain
    const knownAddresses = protocol.knownAddresses.get(chain.chainId) || [];
    
    // Check for contracts deployed by known addresses (factory pattern)
    for (const address of knownAddresses) {
      try {
        // This would involve checking transaction history of known addresses
        // For now, we'll simulate finding new contracts
        const contracts = await this.simulateProtocolContractDiscovery(protocol, chain);
        newContracts.push(...contracts);
      } catch (error) {
        logger.debug(`Error checking address ${address} for new contracts`);
      }
    }

    // Filter to only recent contracts (last 7 days)
    const sevenDaysAgo = Math.floor((Date.now() - 7 * 24 * 60 * 60 * 1000) / 1000);
    return newContracts.filter(contract => contract.timestamp > sevenDaysAgo);
  }

  /**
   * Simulate protocol contract discovery (replace with real implementation)
   */
  private async simulateProtocolContractDiscovery(protocol: TrackedProtocol, chain: ChainConfig): Promise<ContractInfo[]> {
    // This is a simulation - in production you'd implement real contract discovery
    const contracts: ContractInfo[] = [];
    
    // Simulate finding a new airdrop contract for high-priority protocols
    if (protocol.priority <= 2 && Math.random() < 0.1) { // 10% chance for demo
      contracts.push({
        address: '0x' + Math.random().toString(16).substr(2, 40),
        name: `${protocol.name}Airdrop`,
        abi: JSON.stringify([
          {
            "type": "function",
            "name": "claim",
            "inputs": [
              {"name": "amount", "type": "uint256"},
              {"name": "proof", "type": "bytes32[]"}
            ]
          }
        ]),
        sourceCode: `contract ${protocol.name}Airdrop { function claim(uint256 amount, bytes32[] proof) external { } }`,
        compiler: 'v0.8.19+commit.7dd6d404',
        txHash: '0x' + Math.random().toString(16).substr(2, 64),
        blockNumber: 18000000 + Math.floor(Math.random() * 1000),
        timestamp: Math.floor(Date.now() / 1000) - Math.floor(Math.random() * 86400), // Last 24 hours
        chainId: chain.chainId
      });
    }

    return contracts;
  }

  /**
   * Determine alert type based on contract analysis
   */
  private determineAlertType(contracts: ContractInfo[]): 'new_deployment' | 'airdrop_potential' | 'claim_available' {
    // Check if any contract has claim functions
    const hasClaimFunctions = contracts.some(contract => {
      try {
        const abi = JSON.parse(contract.abi || '[]');
        return abi.some((item: any) => 
          item.type === 'function' && 
          (item.name?.includes('claim') || item.name?.includes('withdraw'))
        );
      } catch {
        return false;
      }
    });

    if (hasClaimFunctions) {
      return 'claim_available';
    }

    // Check for airdrop keywords
    const hasAirdropKeywords = contracts.some(contract => {
      const text = `${contract.name} ${contract.sourceCode || ''}`.toLowerCase();
      return ['airdrop', 'distribution', 'merkle', 'claim'].some(keyword => text.includes(keyword));
    });

    if (hasAirdropKeywords) {
      return 'airdrop_potential';
    }

    return 'new_deployment';
  }

  /**
   * Check if contracts require immediate action
   */
  private requiresImmediateAction(contracts: ContractInfo[]): boolean {
    // Immediate action required if:
    // 1. Contract has claim functions
    // 2. Contract was deployed very recently (< 6 hours)
    
    const hasClaimFunctions = contracts.some(contract => {
      try {
        const abi = JSON.parse(contract.abi || '[]');
        return abi.some((item: any) => 
          item.type === 'function' && item.name?.includes('claim')
        );
      } catch {
        return false;
      }
    });

    const isVeryRecent = contracts.some(contract => {
      const ageHours = (Date.now() / 1000 - contract.timestamp) / 3600;
      return ageHours < 6;
    });

    return hasClaimFunctions && isVeryRecent;
  }

  /**
   * Add new protocol to tracking
   */
  addProtocol(protocol: TrackedProtocol): void {
    this.trackedProtocols.set(protocol.id, protocol);
    logger.info(`📋 Added ${protocol.name} to protocol tracking`);
  }

  /**
   * Get protocol by ID
   */
  getProtocol(protocolId: string): TrackedProtocol | undefined {
    return this.trackedProtocols.get(protocolId);
  }

  /**
   * Get all tracked protocols
   */
  getAllProtocols(): TrackedProtocol[] {
    return Array.from(this.trackedProtocols.values());
  }

  /**
   * Get high-priority protocols
   */
  getHighPriorityProtocols(): TrackedProtocol[] {
    return Array.from(this.trackedProtocols.values())
      .filter(p => p.priority <= 2 && p.isActive)
      .sort((a, b) => a.priority - b.priority);
  }

  /**
   * Record airdrop event for protocol
   */
  recordAirdropEvent(protocolId: string, event: AirdropEvent): void {
    const protocol = this.trackedProtocols.get(protocolId);
    if (protocol) {
      protocol.airdropHistory.push(event);
      logger.extract(`📝 Recorded airdrop event for ${protocol.name}: ${event.description}`);
    }
  }

  /**
   * Start continuous protocol monitoring
   */
  async startContinuousMonitoring(
    callback: (alerts: ProtocolDeploymentAlert[]) => void,
    intervalMinutes: number = 30
  ): Promise<void> {
    logger.extract(`🔄 Starting continuous protocol monitoring (${intervalMinutes} minute intervals)`);
    
    const monitor = async () => {
      try {
        const alerts = await this.checkAllProtocolsForDeployments();
        if (alerts.length > 0) {
          callback(alerts);
        }
      } catch (error) {
        logger.error('Error in continuous protocol monitoring:', error);
      }
    };

    // Initial check
    await monitor();
    
    // Set up interval
    setInterval(monitor, intervalMinutes * 60 * 1000);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
