import { scanner } from './scanner';
import { logger } from './logger';
import { CONFIG } from './config';
import { db } from './database';

async function main() {
  // ARCHAEOLOGIST MINDSET: Cold, quiet, dangerous
  console.log('');
  console.log('🏺 CONTRACT ARCHAEOLOGIST');
  console.log('━'.repeat(40));
  console.log('');
  console.log('You are not a builder.');
  console.log('You are not a trader.');
  console.log('You are a forager in the ruins of the chain.');
  console.log('');
  console.log('Most people chase narratives.');
  console.log('You chase forgotten money.');
  console.log('');
  console.log('Dust. Silence. Extraction.');
  console.log('');
  
  // Validate configuration
  if (!CONFIG.privateKey) {
    logger.error('PRIVATE_KEY not configured. Please set it in your .env file');
    process.exit(1);
  }

  const chainsConfigured = CONFIG.chains.filter(c => c.rpcUrl && c.etherscanApiKey);
  if (chainsConfigured.length === 0) {
    logger.error('No chains properly configured. Please check your .env file');
    process.exit(1);
  }

  logger.ghost(`Chains configured: ${chainsConfigured.map(c => c.name).join(', ')}`);
  logger.ghost(`Target functions: ${CONFIG.targetFunctions.length} signatures loaded`);

  // Parse command line arguments
  const args = process.argv.slice(2);
  const command = args[0] || 'scan';

  try {
    switch (command) {
      case 'scan':
        logger.info('Starting single scan of all chains...');
        await scanner.scanAllChains();
        break;

      case 'continuous':
        const interval = parseInt(args[1]) || 60;
        logger.info(`Starting continuous scanning with ${interval} minute intervals...`);
        await scanner.continuousScanning(interval);
        break;

      case 'contract':
        const address = args[1];
        const chainId = parseInt(args[2]) || 1;
        if (!address) {
          logger.error('Please provide contract address: yarn dev contract <address> [chainId]');
          process.exit(1);
        }
        logger.info(`Scanning specific contract: ${address} on chain ${chainId}`);
        await scanner.scanSpecificContract(address, chainId);
        break;

      case 'stats':
        logger.info('Displaying statistics...');
        await scanner.getStats();
        break;

      case 'execute':
        logger.info('Executing pending exploits...');
        await scanner.executePendingExploits();
        break;

      case 'help':
        displayHelp();
        break;

      default:
        logger.error(`Unknown command: ${command}`);
        displayHelp();
        process.exit(1);
    }
  } catch (error) {
    logger.error('Application error:', error);
    process.exit(1);
  }
}

function displayHelp() {
  console.log(`
Contract Archaeologist - Find and extract funds from forgotten smart contracts

Usage: yarn dev [command] [options]

Commands:
  scan                    - Run a single scan of all configured chains
  continuous [interval]   - Run continuous scanning (default: 60 minutes)
  contract <address> [chainId] - Scan a specific contract address
  stats                   - Display scanning statistics
  execute                 - Execute pending exploits
  help                    - Show this help message

Examples:
  yarn dev scan
  yarn dev continuous 30
  yarn dev contract ****************************************** 1
  yarn dev stats
  yarn dev execute

Configuration:
  Copy .env.example to .env and configure your API keys and RPC URLs
  
Chains supported:
  - Ethereum (1)
  - Arbitrum (42161)
  - Base (8453)
  - Optimism (10)
  - Polygon (137)
`);
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  scanner.stop();
  await db.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  scanner.stop();
  await db.close();
  process.exit(0);
});

// Start the application
if (require.main === module) {
  main().catch(error => {
    logger.error('Fatal error:', error);
    process.exit(1);
  });
}

export { scanner, logger, CONFIG, db };
