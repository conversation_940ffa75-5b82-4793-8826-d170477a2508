import { ethers } from 'ethers';
import { ContractInfo, SimulationResult, ChainConfig } from './types';
import { CONFIG } from './config';
import { logger } from './logger';
import { db } from './database';
import { contractSimulator } from './simulator';

export interface FuzzingStrategy {
  name: string;
  description: string;
  generateParameters(abiFunction: any): any[][];
  priority: number;
}

export interface FuzzingResult {
  strategy: string;
  parameters: any[];
  result: SimulationResult;
  interestingBehavior: boolean;
  potentialExploit: boolean;
  riskScore: number;
}

export class ContractFuzzer {
  private strategies: FuzzingStrategy[] = [];
  private providers: Map<number, ethers.JsonRpcProvider> = new Map();

  constructor() {
    this.initializeStrategies();
    this.initializeProviders();
  }

  private initializeProviders(): void {
    for (const chain of CONFIG.chains) {
      if (chain.rpcUrl) {
        const provider = new ethers.JsonRpcProvider(chain.rpcUrl);
        this.providers.set(chain.chainId, provider);
      }
    }
  }

  private initializeStrategies(): void {
    this.strategies = [
      new BoundaryValueStrategy(),
      new OverflowUnderflowStrategy(),
      new ReentrancyStrategy(),
      new AccessControlStrategy(),
      new StateManipulationStrategy(),
      new TokenAmountStrategy(),
      new AddressStrategy(),
      new TimestampStrategy(),
      new ArrayStrategy(),
      new SlippageStrategy(),
      new GasLimitStrategy(),
      new MerkleProofStrategy(),
      new SignatureStrategy(),
      new PricingStrategy(),
      new RoundingStrategy()
    ];

    // Sort strategies by priority (lower is higher priority)
    this.strategies.sort((a, b) => a.priority - b.priority);
  }

  async fuzzContract(
    contract: ContractInfo,
    targetFunctions: string[],
    maxIterations: number = 100
  ): Promise<FuzzingResult[]> {
    const results: FuzzingResult[] = [];
    
    logger.info(`Starting fuzzing for contract ${contract.address} with ${targetFunctions.length} functions`);

    for (const functionName of targetFunctions) {
      const functionResults = await this.fuzzFunction(contract, functionName, maxIterations);
      results.push(...functionResults);
    }

    // Analyze results for interesting patterns
    const interestingResults = this.analyzeResults(results);
    
    // Save fuzzing results to database
    await this.saveFuzzingResults(contract.address, interestingResults);

    logger.info(`Fuzzing completed for ${contract.address}. Found ${interestingResults.length} interesting results`);
    return interestingResults;
  }

  private async fuzzFunction(
    contract: ContractInfo,
    functionName: string,
    maxIterations: number
  ): Promise<FuzzingResult[]> {
    const abiFunction = contract.abi.find(
      item => item.type === 'function' && item.name === functionName
    );

    if (!abiFunction) {
      logger.warn(`Function ${functionName} not found in ABI for ${contract.address}`);
      return [];
    }

    const results: FuzzingResult[] = [];
    const provider = this.providers.get(contract.chainId);
    
    if (!provider) {
      logger.error(`No provider for chain ${contract.chainId}`);
      return [];
    }

    const contractInstance = new ethers.Contract(contract.address, contract.abi, provider);

    // Generate test cases using all strategies
    for (const strategy of this.strategies) {
      try {
        logger.debug(`Applying ${strategy.name} strategy to ${functionName}`);
        
        const parameterSets = strategy.generateParameters(abiFunction);
        const iterationsForStrategy = Math.min(maxIterations / this.strategies.length, parameterSets.length);
        
        for (let i = 0; i < iterationsForStrategy; i++) {
          const parameters = parameterSets[i] || parameterSets[i % parameterSets.length];
          
          try {
            const simulationResult = await this.simulateWithParameters(
              contractInstance,
              functionName,
              parameters,
              contract,
              abiFunction
            );

            const fuzzingResult: FuzzingResult = {
              strategy: strategy.name,
              parameters: parameters,
              result: simulationResult,
              interestingBehavior: this.isInterestingBehavior(simulationResult),
              potentialExploit: this.isPotentialExploit(simulationResult),
              riskScore: this.calculateRiskScore(simulationResult, strategy.name)
            };

            results.push(fuzzingResult);

            // Log interesting findings immediately
            if (fuzzingResult.interestingBehavior) {
              logger.info(`🔍 Interesting behavior found: ${strategy.name} on ${functionName}`);
            }
            
            if (fuzzingResult.potentialExploit) {
              logger.warn(`⚠️ Potential exploit found: ${strategy.name} on ${functionName}`);
            }

          } catch (error) {
            // Some failures are expected in fuzzing
            logger.debug(`Fuzzing failed for ${strategy.name} on ${functionName}:`, error);
          }
        }
      } catch (error) {
        logger.error(`Strategy ${strategy.name} failed:`, error);
      }
    }

    return results;
  }

  private async simulateWithParameters(
    contractInstance: ethers.Contract,
    functionName: string,
    parameters: any[],
    contract: ContractInfo,
    abiFunction: any
  ): Promise<SimulationResult> {
    const result: SimulationResult = {
      contractAddress: contract.address,
      functionName: functionName,
      signature: `${functionName}(${parameters.map(() => 'unknown').join(',')})`,
      success: false,
      returnData: '',
      gasEstimate: 0,
      timestamp: Date.now()
    };

    try {
      // Try different call methods
      let callResult: any;
      
      // Method 1: Static call
      try {
        callResult = await contractInstance[functionName].staticCall(...parameters);
        result.success = true;
      } catch (staticError) {
        // Method 2: Estimate gas (sometimes reveals more info)
        try {
          const gasEstimate = await contractInstance[functionName].estimateGas(...parameters);
          result.gasEstimate = Number(gasEstimate);
          result.success = true;
          callResult = { gasEstimate };
        } catch (gasError) {
          // Method 3: Call data encoding (to check if function exists)
          try {
            contractInstance.interface.encodeFunctionData(functionName, parameters);
            result.success = true;
            callResult = { encoded: true };
          } catch (encodeError) {
            throw new Error(`All call methods failed: ${encodeError.message}`);
          }
        }
      }

      result.returnData = this.formatReturnData(callResult);
      result.potentialValue = this.extractPotentialValue(callResult, abiFunction);

      return result;
    } catch (error) {
      result.success = false;
      result.error = error instanceof Error ? error.message : 'Unknown error';
      return result;
    }
  }

  private formatReturnData(result: any): string {
    if (result === null || result === undefined) {
      return '';
    }
    
    try {
      if (typeof result === 'bigint') {
        return result.toString();
      }
      
      if (Array.isArray(result)) {
        return result.map(item => 
          typeof item === 'bigint' ? item.toString() : String(item)
        ).join(',');
      }
      
      return JSON.stringify(result);
    } catch (error) {
      return String(result);
    }
  }

  private extractPotentialValue(result: any, abiFunction: any): string {
    if (typeof result === 'bigint' && result > 0) {
      return result.toString();
    }
    
    if (Array.isArray(result)) {
      for (const item of result) {
        if (typeof item === 'bigint' && item > 0) {
          return item.toString();
        }
      }
    }
    
    return '0';
  }

  private isInterestingBehavior(result: SimulationResult): boolean {
    // Check for interesting patterns
    if (!result.success) return false;
    
    // Large return values
    if (result.potentialValue && parseFloat(result.potentialValue) > 0) {
      return true;
    }
    
    // High gas usage
    if (result.gasEstimate > 1000000) {
      return true;
    }
    
    // Specific error patterns that might indicate vulnerabilities
    if (result.error) {
      const interestingErrors = [
        'insufficient balance',
        'transfer failed',
        'unauthorized',
        'reentrancy',
        'overflow',
        'underflow'
      ];
      
      return interestingErrors.some(pattern => 
        result.error!.toLowerCase().includes(pattern)
      );
    }
    
    return false;
  }

  private isPotentialExploit(result: SimulationResult): boolean {
    if (!result.success) return false;

    // Validate against false positive patterns first
    if (this.isFalsePositive(result)) {
      return false;
    }

    // High value extraction (but not unrealistic amounts)
    if (result.potentialValue) {
      const value = parseFloat(result.potentialValue);
      if (value > 1000 && value < 1e18) { // Reasonable range
        return true;
      }
    }

    // Check for exploit patterns in return data
    if (result.returnData) {
      const returnData = result.returnData.toLowerCase();
      const exploitPatterns = [
        'true', // Successful claim/withdraw
        'balance', // Balance manipulation
        'transfer', // Successful transfer
      ];

      return exploitPatterns.some(pattern => returnData.includes(pattern));
    }

    return false;
  }

  private isFalsePositive(result: SimulationResult): boolean {
    // Check for common false positive patterns

    // Unrealistic values (likely overflow/underflow artifacts)
    if (result.potentialValue) {
      const value = parseFloat(result.potentialValue);
      if (value > 1e18 || value === Number.MAX_SAFE_INTEGER) {
        return true;
      }
    }

    // Gas estimates that are too high (likely reverts)
    if (result.gasEstimate > 10000000) {
      return true;
    }

    // Common revert patterns that aren't exploits
    if (result.error) {
      const falsePositiveErrors = [
        'execution reverted',
        'invalid opcode',
        'out of gas',
        'stack underflow',
        'stack overflow',
        'invalid jump destination',
        'function selector not found'
      ];

      return falsePositiveErrors.some(pattern =>
        result.error!.toLowerCase().includes(pattern)
      );
    }

    // Return data that indicates normal operation, not exploits
    if (result.returnData) {
      const returnData = result.returnData.toLowerCase();
      const normalPatterns = [
        '0x', // Empty return
        'false', // Failed operation
        '0', // Zero value
      ];

      return normalPatterns.some(pattern => returnData === pattern);
    }

    return false;
  }

  private calculateRiskScore(result: SimulationResult, strategyName: string): number {
    let score = 0;
    
    // Base score for successful simulation
    if (result.success) score += 1;
    
    // Value-based scoring
    if (result.potentialValue) {
      const value = parseFloat(result.potentialValue);
      if (value > 0) score += 2;
      if (value > 100) score += 3;
      if (value > 1000) score += 5;
    }
    
    // Gas-based scoring
    if (result.gasEstimate > 500000) score += 2;
    if (result.gasEstimate > 1000000) score += 4;
    
    // Strategy-based scoring
    const highRiskStrategies = [
      'OverflowUnderflowStrategy',
      'ReentrancyStrategy',
      'AccessControlStrategy'
    ];
    
    if (highRiskStrategies.includes(strategyName)) {
      score += 3;
    }
    
    return Math.min(score, 10); // Cap at 10
  }

  private analyzeResults(results: FuzzingResult[]): FuzzingResult[] {
    // First pass: Remove obvious false positives
    const validated = results.filter(r => !this.isFalsePositive(r.result));

    // Second pass: Filter for interesting results with validation
    const interesting = validated.filter(r => {
      // Must have either interesting behavior or potential exploit
      if (!r.interestingBehavior && !r.potentialExploit) return false;

      // Additional validation for potential exploits
      if (r.potentialExploit) {
        return this.validatePotentialExploit(r);
      }

      // For interesting behavior, check risk score threshold
      return r.riskScore > 3;
    });

    // Sort by risk score (descending)
    interesting.sort((a, b) => b.riskScore - a.riskScore);

    return interesting;
  }

  private validatePotentialExploit(result: FuzzingResult): boolean {
    // Cross-validate potential exploits with multiple checks

    // Must have reasonable gas estimate
    if (result.result.gasEstimate > 5000000) {
      return false;
    }

    // Must have realistic value extraction
    if (result.result.potentialValue) {
      const value = parseFloat(result.result.potentialValue);
      if (value <= 0 || value > 1e15) { // Between 0 and 1000 ETH equivalent
        return false;
      }
    }

    // Strategy-specific validation
    if (result.strategy === 'OverflowUnderflowStrategy') {
      // Overflow/underflow should show specific patterns
      return this.validateOverflowResult(result);
    }

    if (result.strategy === 'ReentrancyStrategy') {
      // Reentrancy should show state changes
      return this.validateReentrancyResult(result);
    }

    return true;
  }

  private validateOverflowResult(result: FuzzingResult): boolean {
    // Check if the result shows actual overflow/underflow behavior
    const returnData = result.result.returnData?.toLowerCase() || '';

    // Should not be a simple revert
    if (result.result.error?.includes('revert')) {
      return false;
    }

    // Should show numeric manipulation
    return returnData.includes('balance') || returnData.includes('amount');
  }

  private validateReentrancyResult(result: FuzzingResult): boolean {
    // Check if the result shows actual reentrancy behavior

    // Should have higher gas usage (multiple calls)
    if (result.result.gasEstimate < 100000) {
      return false;
    }

    // Should not be a simple access control revert
    if (result.result.error?.includes('unauthorized') ||
        result.result.error?.includes('access denied')) {
      return false;
    }

    return true;
  }

  private async saveFuzzingResults(contractAddress: string, results: FuzzingResult[]): Promise<void> {
    // Save to database for analysis
    for (const result of results) {
      await db.saveFuzzingResult({
        contractAddress,
        strategy: result.strategy,
        parameters: JSON.stringify(result.parameters),
        success: result.result.success,
        returnData: result.result.returnData,
        gasEstimate: result.result.gasEstimate,
        riskScore: result.riskScore,
        potentialExploit: result.potentialExploit,
        timestamp: Date.now()
      });
    }
  }

  // Batch fuzzing for multiple contracts
  async batchFuzz(
    contracts: ContractInfo[],
    maxIterationsPerContract: number = 50
  ): Promise<Map<string, FuzzingResult[]>> {
    const results = new Map<string, FuzzingResult[]>();
    
    logger.info(`Starting batch fuzzing for ${contracts.length} contracts`);
    
    for (const contract of contracts) {
      try {
        // Extract function names from ABI
        const targetFunctions = contract.abi
          .filter(item => item.type === 'function' && item.stateMutability !== 'view')
          .map(item => item.name);
        
        if (targetFunctions.length === 0) {
          continue;
        }
        
        const contractResults = await this.fuzzContract(
          contract,
          targetFunctions,
          maxIterationsPerContract
        );
        
        if (contractResults.length > 0) {
          results.set(contract.address, contractResults);
        }
        
        // Small delay to avoid overwhelming the network
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        logger.error(`Fuzzing failed for contract ${contract.address}:`, error);
      }
    }
    
    return results;
  }
}

export const contractFuzzer = new ContractFuzzer();
