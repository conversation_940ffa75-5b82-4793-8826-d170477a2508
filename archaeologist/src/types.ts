export interface ChainConfig {
  chainId: number;
  name: string;
  rpcUrl: string;
  etherscanApiUrl: string;
  etherscanApiKey: string;
}

export interface TargetFunction {
  name: string;
  signature: string;
  fourByteSignature: string;
  description: string;
  priority: number;
}

export interface ContractInfo {
  address: string;
  name: string;
  abi: any[];
  sourceCode: string;
  compiler: string;
  txHash: string;
  blockNumber: number;
  timestamp: number;
  chainId: number;
}

export interface SimulationResult {
  contractAddress: string;
  functionName: string;
  signature: string;
  success: boolean;
  returnData: string;
  gasEstimate: number;
  error?: string;
  potentialValue?: string;
  timestamp: number;
  // 🏺 SURGICAL PRECISION: Enhanced simulation data
  confidence?: number;
  extractionPotential?: number;
  precisionResults?: any[];
}

export interface ExploitableContract {
  address: string;
  functionName: string;
  signature: string;
  estimatedValue: string;
  gasEstimate: number;
  priority: number;
  chainId: number;
  discovered: Date;
  executed: boolean;
}

export interface ExecutionResult {
  contractAddress: string;
  functionName: string;
  txHash: string;
  success: boolean;
  gasUsed: number;
  value: string;
  timestamp: number;
  error?: string;
}

export interface ScanStatus {
  chainId: number;
  lastProcessedBlock: number;
  lastScanTime: Date;
  contractsScanned: number;
  exploitsFound: number;
  totalValue: string;
}

export interface Config {
  chains: ChainConfig[];
  targetFunctions: TargetFunction[];
  scanBatchSize: number;
  simulationTimeout: number;
  maxGasPrice: bigint;
  privateKey: string;
  dbPath: string;
  logLevel: string;
  flashbotsRpcUrl?: string;
}
