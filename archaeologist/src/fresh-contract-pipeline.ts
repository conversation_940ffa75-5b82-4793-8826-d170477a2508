import { logger } from './logger';
import { ContractInfo, ChainConfig } from './types';
import { GitHubMonitor, ProtocolDeployment } from './github-monitor';
import { EtherscanRSSMonitor, FreshContract } from './etherscan-rss';
import { ContractFetcher } from './fetcher';
import { ContractFilter } from './filter';
import { Database } from './database';
import { CONFIG } from './config';

export interface FreshContractCandidate {
  contract: ContractInfo;
  freshness: number; // 0-100, higher = fresher
  airdropPotential: number; // 0-100, higher = more likely airdrop
  priority: number; // 1-5, lower = higher priority
  source: 'github' | 'etherscan' | 'block-monitor';
  deploymentTime: Date;
  verificationTime?: Date;
  keywords: string[];
  protocolMatch?: string;
  githubRelease?: string;
}

export interface TimingWindow {
  name: string;
  startHours: number;
  endHours: number;
  priority: number;
  description: string;
}

export class FreshContractPipeline {
  private readonly githubMonitor: GitHubMonitor;
  private readonly etherscanMonitor: EtherscanRSSMonitor;
  private readonly contractFetcher: ContractFetcher;
  private readonly contractFilter: ContractFilter;
  private readonly database: Database;

  // Critical timing windows for airdrop hunting
  private readonly timingWindows: TimingWindow[] = [
    {
      name: 'GOLDEN_WINDOW',
      startHours: 0,
      endHours: 24,
      priority: 1,
      description: 'First 24 hours - highest alpha potential'
    },
    {
      name: 'SILVER_WINDOW', 
      startHours: 24,
      endHours: 48,
      priority: 2,
      description: '24-48 hours - still early, good potential'
    },
    {
      name: 'BRONZE_WINDOW',
      startHours: 48,
      endHours: 168, // 7 days
      priority: 3,
      description: '2-7 days - moderate potential'
    },
    {
      name: 'STALE_WINDOW',
      startHours: 168,
      endHours: 720, // 30 days
      priority: 4,
      description: '1-4 weeks - low potential'
    }
  ];

  constructor() {
    this.githubMonitor = new GitHubMonitor();
    this.etherscanMonitor = new EtherscanRSSMonitor();
    this.contractFetcher = new ContractFetcher();
    this.contractFilter = new ContractFilter();
    this.database = new Database();
  }

  /**
   * Main pipeline: Discover and prioritize fresh contracts with airdrop potential
   */
  async discoverFreshAirdropCandidates(): Promise<FreshContractCandidate[]> {
    logger.extract('🎯 INITIATING FRESH CONTRACT DISCOVERY PIPELINE...');
    
    const candidates: FreshContractCandidate[] = [];

    // Step 1: GitHub monitoring for protocol deployments
    logger.info('📡 Monitoring GitHub for fresh protocol deployments...');
    const githubCandidates = await this.processGitHubDeployments();
    candidates.push(...githubCandidates);

    // Step 2: Etherscan RSS monitoring for verified contracts
    logger.info('📡 Monitoring Etherscan RSS for fresh verifications...');
    const etherscanCandidates = await this.processEtherscanContracts();
    candidates.push(...etherscanCandidates);

    // Step 3: Block monitoring for unverified fresh contracts
    logger.info('📡 Monitoring recent blocks for unverified contracts...');
    const blockCandidates = await this.processRecentBlocks();
    candidates.push(...blockCandidates);

    // Step 4: Prioritize and filter candidates
    const prioritizedCandidates = this.prioritizeCandidates(candidates);

    logger.extract(`🏺 DISCOVERY COMPLETE: Found ${prioritizedCandidates.length} fresh airdrop candidates`);
    
    return prioritizedCandidates;
  }

  /**
   * Process GitHub deployments for airdrop potential
   */
  private async processGitHubDeployments(): Promise<FreshContractCandidate[]> {
    const candidates: FreshContractCandidate[] = [];
    
    try {
      const deployments = await this.githubMonitor.monitorProtocolReleases();
      
      for (const deployment of deployments) {
        for (const address of deployment.contractAddresses) {
          // Try to fetch contract details for each address
          for (const chainId of deployment.chains) {
            const chain = CONFIG.chains.find(c => c.chainId === chainId);
            if (!chain) continue;

            try {
              const contract = await this.contractFetcher.fetchContractDetails(chain, address);
              if (contract) {
                const candidate = await this.createCandidateFromGitHub(contract, deployment);
                candidates.push(candidate);
              }
            } catch (error) {
              logger.debug(`Could not fetch contract ${address} on chain ${chainId}`);
            }
          }
        }
      }
    } catch (error) {
      logger.error('Error processing GitHub deployments:', error);
    }

    return candidates;
  }

  /**
   * Process Etherscan RSS contracts
   */
  private async processEtherscanContracts(): Promise<FreshContractCandidate[]> {
    const candidates: FreshContractCandidate[] = [];
    
    try {
      const freshContracts = await this.etherscanMonitor.getRecentlyVerifiedContracts(48); // Last 48 hours
      
      for (const freshContract of freshContracts) {
        const chain = CONFIG.chains.find(c => c.chainId === freshContract.chainId);
        if (!chain) continue;

        try {
          const contract = await this.contractFetcher.fetchContractDetails(chain, freshContract.address);
          if (contract) {
            const candidate = await this.createCandidateFromEtherscan(contract, freshContract);
            candidates.push(candidate);
          }
        } catch (error) {
          logger.debug(`Could not fetch contract details for ${freshContract.address}`);
        }
      }
    } catch (error) {
      logger.error('Error processing Etherscan contracts:', error);
    }

    return candidates;
  }

  /**
   * Process recent blocks for unverified contracts
   */
  private async processRecentBlocks(): Promise<FreshContractCandidate[]> {
    const candidates: FreshContractCandidate[] = [];
    
    try {
      for (const chain of CONFIG.chains) {
        if (!chain.rpcUrl) continue;

        // Get contracts from last 24 hours
        const currentBlock = await this.getCurrentBlockNumber(chain);
        const blocksPerDay = Math.floor(86400 / 12); // Assuming 12 second blocks
        const startBlock = Math.max(0, currentBlock - blocksPerDay);

        const contracts = await this.contractFetcher.fetchContractsByBlockRange(
          chain, 
          startBlock, 
          currentBlock
        );

        for (const contract of contracts) {
          const candidate = await this.createCandidateFromBlock(contract);
          candidates.push(candidate);
        }
      }
    } catch (error) {
      logger.error('Error processing recent blocks:', error);
    }

    return candidates;
  }

  /**
   * Create candidate from GitHub deployment
   */
  private async createCandidateFromGitHub(
    contract: ContractInfo, 
    deployment: ProtocolDeployment
  ): Promise<FreshContractCandidate> {
    const deploymentTime = deployment.deploymentTime;
    const freshness = this.calculateFreshness(deploymentTime);
    const timingWindow = this.getTimingWindow(deploymentTime);

    return {
      contract,
      freshness,
      airdropPotential: 85, // High potential from GitHub releases
      priority: Math.min(deployment.priority, timingWindow.priority),
      source: 'github',
      deploymentTime,
      keywords: ['github', 'release', deployment.protocol],
      protocolMatch: deployment.protocol,
      githubRelease: deployment.release?.name
    };
  }

  /**
   * Create candidate from Etherscan RSS
   */
  private async createCandidateFromEtherscan(
    contract: ContractInfo,
    freshContract: FreshContract
  ): Promise<FreshContractCandidate> {
    const verificationTime = freshContract.verificationTime;
    const freshness = this.calculateFreshness(verificationTime);
    const timingWindow = this.getTimingWindow(verificationTime);

    return {
      contract,
      freshness,
      airdropPotential: freshContract.airdropPotential,
      priority: Math.min(freshContract.priority, timingWindow.priority),
      source: 'etherscan',
      deploymentTime: verificationTime, // Use verification as proxy for deployment
      verificationTime,
      keywords: freshContract.keywords
    };
  }

  /**
   * Create candidate from block monitoring
   */
  private async createCandidateFromBlock(contract: ContractInfo): Promise<FreshContractCandidate> {
    const deploymentTime = new Date(contract.timestamp * 1000);
    const freshness = this.calculateFreshness(deploymentTime);
    const timingWindow = this.getTimingWindow(deploymentTime);
    
    // Analyze contract for airdrop potential
    const airdropPotential = await this.analyzeContractAirdropPotential(contract);

    return {
      contract,
      freshness,
      airdropPotential,
      priority: timingWindow.priority,
      source: 'block-monitor',
      deploymentTime,
      keywords: this.extractKeywordsFromContract(contract)
    };
  }

  /**
   * Calculate freshness score (0-100, higher = fresher)
   */
  private calculateFreshness(deploymentTime: Date): number {
    const ageHours = (Date.now() - deploymentTime.getTime()) / (1000 * 60 * 60);
    
    if (ageHours <= 1) return 100;
    if (ageHours <= 6) return 90;
    if (ageHours <= 24) return 80;
    if (ageHours <= 48) return 60;
    if (ageHours <= 168) return 40; // 1 week
    if (ageHours <= 720) return 20; // 1 month
    
    return 10;
  }

  /**
   * Get timing window for a deployment time
   */
  private getTimingWindow(deploymentTime: Date): TimingWindow {
    const ageHours = (Date.now() - deploymentTime.getTime()) / (1000 * 60 * 60);
    
    for (const window of this.timingWindows) {
      if (ageHours >= window.startHours && ageHours < window.endHours) {
        return window;
      }
    }
    
    return this.timingWindows[this.timingWindows.length - 1]; // Default to stale window
  }

  /**
   * Analyze contract for airdrop potential
   */
  private async analyzeContractAirdropPotential(contract: ContractInfo): Promise<number> {
    let score = 0;
    
    const contractText = `${contract.name} ${contract.sourceCode || ''}`.toLowerCase();
    
    // Check for airdrop keywords
    const airdropKeywords = ['airdrop', 'claim', 'merkle', 'distribution', 'token'];
    const foundKeywords = airdropKeywords.filter(k => contractText.includes(k));
    score += foundKeywords.length * 20;

    // Check for claim functions in ABI
    if (contract.abi) {
      try {
        const abi = JSON.parse(contract.abi);
        const claimFunctions = abi.filter((item: any) => 
          item.type === 'function' && 
          (item.name?.includes('claim') || item.name?.includes('withdraw'))
        );
        score += claimFunctions.length * 15;
      } catch (error) {
        // Invalid ABI, skip
      }
    }

    // Bonus for recent deployment
    const ageHours = (Date.now() - contract.timestamp * 1000) / (1000 * 60 * 60);
    if (ageHours <= 24) score += 25;
    else if (ageHours <= 48) score += 15;

    return Math.min(score, 100);
  }

  /**
   * Extract keywords from contract
   */
  private extractKeywordsFromContract(contract: ContractInfo): string[] {
    const keywords: string[] = [];
    const text = `${contract.name} ${contract.sourceCode || ''}`.toLowerCase();
    
    const keywordList = [
      'airdrop', 'claim', 'merkle', 'distribution', 'token',
      'governance', 'dao', 'voting', 'rewards', 'vesting'
    ];
    
    for (const keyword of keywordList) {
      if (text.includes(keyword)) {
        keywords.push(keyword);
      }
    }
    
    return keywords;
  }

  /**
   * Prioritize candidates based on multiple factors
   */
  private prioritizeCandidates(candidates: FreshContractCandidate[]): FreshContractCandidate[] {
    return candidates
      .filter(c => c.airdropPotential >= 30) // Minimum threshold
      .sort((a, b) => {
        // Primary sort: priority (lower = better)
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        
        // Secondary sort: airdrop potential (higher = better)
        if (a.airdropPotential !== b.airdropPotential) {
          return b.airdropPotential - a.airdropPotential;
        }
        
        // Tertiary sort: freshness (higher = better)
        return b.freshness - a.freshness;
      });
  }

  /**
   * Get candidates in golden window (first 24 hours)
   */
  async getGoldenWindowCandidates(): Promise<FreshContractCandidate[]> {
    const allCandidates = await this.discoverFreshAirdropCandidates();
    return allCandidates.filter(c => c.freshness >= 80); // Last 24 hours
  }

  /**
   * Get high-priority candidates across all windows
   */
  async getHighPriorityCandidates(): Promise<FreshContractCandidate[]> {
    const allCandidates = await this.discoverFreshAirdropCandidates();
    return allCandidates.filter(c => c.priority <= 2 && c.airdropPotential >= 60);
  }

  /**
   * Start continuous fresh contract monitoring
   */
  async startContinuousMonitoring(
    callback: (candidates: FreshContractCandidate[]) => void,
    intervalMinutes: number = 15
  ): Promise<void> {
    logger.extract(`🔄 Starting continuous fresh contract monitoring (${intervalMinutes} minute intervals)`);
    
    const monitor = async () => {
      try {
        const candidates = await this.getGoldenWindowCandidates();
        if (candidates.length > 0) {
          logger.extract(`🎯 Found ${candidates.length} golden window candidates`);
          callback(candidates);
        }
      } catch (error) {
        logger.error('Error in continuous fresh contract monitoring:', error);
      }
    };

    // Initial scan
    await monitor();
    
    // Set up interval
    setInterval(monitor, intervalMinutes * 60 * 1000);
  }

  private async getCurrentBlockNumber(chain: ChainConfig): Promise<number> {
    // Implementation would use ethers.js to get current block number
    // For now, return a placeholder
    return 18000000;
  }
}
