import { ethers } from 'ethers';
import { ChainConfig, TargetFunction } from './types';
import { TARGET_FUNCTIONS, CHAIN_CONFIGS } from './config';
import { logger } from './logger';

export interface DecompiledFunction {
  selector: string;
  signature?: string;
  name?: string;
  inputs?: string[];
  confidence: number;
}

export interface DecompilationResult {
  address: string;
  chainId: number;
  functions: DecompiledFunction[];
  hasTargetFunctions: boolean;
  matchedTargetFunctions: TargetFunction[];
  bytecodeHash: string;
}

export class BytecodeDecompiler {
  private providers: Map<number, ethers.JsonRpcProvider> = new Map();
  private functionSignatureDatabase: Map<string, string[]> = new Map();

  constructor(chains: ChainConfig[]) {
    this.initializeProviders(chains);
    this.initializeFunctionDatabase();
  }

  private initializeProviders(chains: ChainConfig[]): void {
    for (const chain of chains) {
      if (chain.rpcUrl) {
        const provider = new ethers.JsonRpcProvider(chain.rpcUrl);
        this.providers.set(chain.chainId, provider);
        logger.debug(`Initialized RPC provider for ${chain.name} (${chain.chainId}): ${chain.rpcUrl}`);
      } else {
        logger.warn(`No RPC URL configured for ${chain.name} (${chain.chainId})`);
      }
    }
    logger.info(`Initialized ${this.providers.size} RPC providers for bytecode decompilation`);
  }

  private initializeFunctionDatabase(): void {
    // Initialize with known target function signatures
    for (const targetFunc of TARGET_FUNCTIONS) {
      const selector = targetFunc.fourByteSignature;
      if (!this.functionSignatureDatabase.has(selector)) {
        this.functionSignatureDatabase.set(selector, []);
      }
      this.functionSignatureDatabase.get(selector)!.push(targetFunc.signature);
    }

    // Add common function signatures
    const commonSignatures = [
      { selector: '0xa9059cbb', signatures: ['transfer(address,uint256)'] },
      { selector: '0x23b872dd', signatures: ['transferFrom(address,address,uint256)'] },
      { selector: '0x095ea7b3', signatures: ['approve(address,uint256)'] },
      { selector: '0x70a08231', signatures: ['balanceOf(address)'] },
      { selector: '0x18160ddd', signatures: ['totalSupply()'] },
      { selector: '0x06fdde03', signatures: ['name()'] },
      { selector: '0x95d89b41', signatures: ['symbol()'] },
      { selector: '0x313ce567', signatures: ['decimals()'] },
      { selector: '0x8da5cb5b', signatures: ['owner()'] },
      { selector: '0xf2fde38b', signatures: ['transferOwnership(address)'] },
      { selector: '0x715018a6', signatures: ['renounceOwnership()'] },
      { selector: '0x5c975abb', signatures: ['paused()'] },
      { selector: '0x8456cb59', signatures: ['pause()'] },
      { selector: '0x3f4ba83a', signatures: ['unpause()'] },
    ];

    for (const { selector, signatures } of commonSignatures) {
      if (!this.functionSignatureDatabase.has(selector)) {
        this.functionSignatureDatabase.set(selector, []);
      }
      this.functionSignatureDatabase.get(selector)!.push(...signatures);
    }
  }

  async decompileContract(address: string, chainId: number): Promise<DecompilationResult | null> {
    const provider = this.providers.get(chainId);
    if (!provider) {
      logger.error(`No provider for chain ${chainId}`);
      return null;
    }

    try {
      // Get contract bytecode
      const bytecode = await provider.getCode(address);
      
      if (bytecode === '0x' || bytecode.length <= 2) {
        logger.debug(`No bytecode found for ${address}`);
        return null;
      }

      logger.info(`Decompiling contract ${address} on chain ${chainId}`);

      // Extract function selectors from bytecode
      const functions = this.extractFunctionSelectors(bytecode);
      
      // Match against target functions
      const matchedTargetFunctions = this.matchTargetFunctions(functions);
      
      const result: DecompilationResult = {
        address,
        chainId,
        functions,
        hasTargetFunctions: matchedTargetFunctions.length > 0,
        matchedTargetFunctions,
        bytecodeHash: ethers.keccak256(bytecode)
      };

      logger.info(`Decompiled ${address}: found ${functions.length} functions, ${matchedTargetFunctions.length} target matches`);
      
      return result;
    } catch (error) {
      logger.error(`Failed to decompile ${address}:`, error);
      return null;
    }
  }

  private extractFunctionSelectors(bytecode: string): DecompiledFunction[] {
    const functions: DecompiledFunction[] = [];
    const selectors = new Set<string>();

    // Remove 0x prefix
    const code = bytecode.slice(2);
    
    // Look for PUSH4 instructions followed by function selectors
    // PUSH4 is 0x63, followed by 4 bytes
    for (let i = 0; i < code.length - 8; i += 2) {
      const opcode = code.slice(i, i + 2);
      
      if (opcode === '63') { // PUSH4
        const selector = '0x' + code.slice(i + 2, i + 10);
        
        if (this.isValidSelector(selector)) {
          selectors.add(selector);
        }
      }
    }

    // Look for direct selector comparisons (common pattern)
    // EQ instruction (0x14) often follows selector loading
    for (let i = 0; i < code.length - 16; i += 2) {
      const instruction = code.slice(i, i + 16);
      
      // Pattern: PUSH4 selector DUP1 PUSH4 selector EQ
      if (instruction.startsWith('63') && instruction.includes('14')) {
        const selector = '0x' + instruction.slice(2, 10);
        
        if (this.isValidSelector(selector)) {
          selectors.add(selector);
        }
      }
    }

    // Convert selectors to function objects
    for (const selector of selectors) {
      const signatures = this.functionSignatureDatabase.get(selector) || [];
      const confidence = signatures.length > 0 ? 0.9 : 0.3;
      
      functions.push({
        selector,
        signature: signatures[0], // Use first known signature
        name: signatures[0] ? this.extractFunctionName(signatures[0]) : undefined,
        inputs: signatures[0] ? this.extractInputTypes(signatures[0]) : undefined,
        confidence
      });
    }

    return functions;
  }

  private isValidSelector(selector: string): boolean {
    // Basic validation for function selectors
    if (selector.length !== 10) return false;
    if (!selector.startsWith('0x')) return false;
    
    // Check if it's a valid hex string
    const hex = selector.slice(2);
    return /^[0-9a-fA-F]{8}$/.test(hex);
  }

  private extractFunctionName(signature: string): string {
    const match = signature.match(/^([a-zA-Z_][a-zA-Z0-9_]*)\(/);
    return match ? match[1] : 'unknown';
  }

  private extractInputTypes(signature: string): string[] {
    const match = signature.match(/\(([^)]*)\)/);
    if (!match || !match[1]) return [];
    
    return match[1].split(',').map(type => type.trim()).filter(type => type.length > 0);
  }

  private matchTargetFunctions(functions: DecompiledFunction[]): TargetFunction[] {
    const matches: TargetFunction[] = [];
    
    for (const func of functions) {
      for (const targetFunc of TARGET_FUNCTIONS) {
        if (func.selector === targetFunc.fourByteSignature) {
          matches.push(targetFunc);
          break;
        }
      }
    }
    
    return matches;
  }

  async batchDecompile(addresses: string[], chainId: number): Promise<DecompilationResult[]> {
    const results: DecompilationResult[] = [];
    
    for (const address of addresses) {
      try {
        const result = await this.decompileContract(address, chainId);
        if (result) {
          results.push(result);
        }
        
        // Small delay to avoid overwhelming the RPC
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        logger.error(`Failed to decompile ${address}:`, error);
      }
    }
    
    return results;
  }

  // Enhanced function signature detection using common patterns
  private detectFunctionPatterns(bytecode: string): DecompiledFunction[] {
    const functions: DecompiledFunction[] = [];
    const code = bytecode.slice(2);
    
    // Look for common function dispatch patterns
    const patterns = [
      // Standard function dispatcher pattern
      /63([0-9a-fA-F]{8})80600e6000396000f3/g,
      // Proxy pattern
      /63([0-9a-fA-F]{8})14[0-9a-fA-F]{2,}/g,
      // Direct comparison pattern
      /63([0-9a-fA-F]{8})811461/g
    ];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(code)) !== null) {
        const selector = '0x' + match[1];
        
        if (this.isValidSelector(selector)) {
          const signatures = this.functionSignatureDatabase.get(selector) || [];
          
          functions.push({
            selector,
            signature: signatures[0],
            name: signatures[0] ? this.extractFunctionName(signatures[0]) : undefined,
            confidence: signatures.length > 0 ? 0.8 : 0.4
          });
        }
      }
    }
    
    return functions;
  }
}

export const bytecodeDecompiler = new BytecodeDecompiler(CHAIN_CONFIGS);
