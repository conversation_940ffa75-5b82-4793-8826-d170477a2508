/**
 * 🏺 SURGICAL PRECISION: Self-Monitoring System
 * 
 * This module provides internal validation and self-correction mechanisms.
 * Detects and fixes issues automatically to maintain surgical precision.
 * 
 * Features:
 * 1. Continuous system health monitoring
 * 2. Automatic error detection and correction
 * 3. Performance metrics tracking
 * 4. Configuration validation
 * 5. Self-healing mechanisms
 * 
 * NO GUESSES. SURGICAL PRECISION.
 */

import { ethers } from 'ethers';
import { logger } from './logger';
import { CONFIG } from './config';
import { signatureVerifier } from './signature-verifier';
import { economicEngine } from './economic-engine';

export interface SystemHealth {
  overall: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'FAILED';
  components: ComponentHealth[];
  metrics: SystemMetrics;
  issues: SystemIssue[];
  recommendations: string[];
  lastCheck: number;
}

export interface ComponentHealth {
  name: string;
  status: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'FAILED';
  metrics: { [key: string]: any };
  issues: string[];
  lastCheck: number;
}

export interface SystemMetrics {
  uptime: number;
  totalScans: number;
  successRate: number;
  avgResponseTime: number;
  errorRate: number;
  memoryUsage: number;
  apiCallsPerMinute: number;
}

export interface SystemIssue {
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  component: string;
  description: string;
  autoFixable: boolean;
  fixAttempted: boolean;
  timestamp: number;
}

class SelfMonitor {
  private healthHistory: SystemHealth[] = [];
  private metrics: SystemMetrics;
  private startTime: number;
  private scanCount = 0;
  private errorCount = 0;
  private responseTimes: number[] = [];
  private apiCalls: { timestamp: number }[] = [];

  constructor() {
    this.startTime = Date.now();
    this.metrics = this.initializeMetrics();
    this.startContinuousMonitoring();
  }

  /**
   * Initialize system metrics
   */
  private initializeMetrics(): SystemMetrics {
    return {
      uptime: 0,
      totalScans: 0,
      successRate: 100,
      avgResponseTime: 0,
      errorRate: 0,
      memoryUsage: 0,
      apiCallsPerMinute: 0
    };
  }

  /**
   * SURGICAL PRECISION: Comprehensive system health check
   */
  async performHealthCheck(): Promise<SystemHealth> {
    logger.ghost('🔍 Performing comprehensive system health check...');

    const components: ComponentHealth[] = [];
    const issues: SystemIssue[] = [];
    const recommendations: string[] = [];

    // Check all system components
    components.push(await this.checkConfigurationHealth());
    components.push(await this.checkNetworkHealth());
    components.push(await this.checkSignatureVerification());
    components.push(await this.checkEconomicEngine());
    components.push(await this.checkMemoryHealth());
    components.push(await this.checkAPIHealth());

    // Collect all issues
    components.forEach(component => {
      component.issues.forEach(issue => {
        issues.push({
          severity: this.determineSeverity(component.status),
          component: component.name,
          description: issue,
          autoFixable: this.isAutoFixable(issue),
          fixAttempted: false,
          timestamp: Date.now()
        });
      });
    });

    // Update metrics
    this.updateMetrics();

    // Determine overall health
    const overallHealth = this.determineOverallHealth(components);

    // Generate recommendations
    recommendations.push(...this.generateRecommendations(components, issues));

    const health: SystemHealth = {
      overall: overallHealth,
      components,
      metrics: this.metrics,
      issues,
      recommendations,
      lastCheck: Date.now()
    };

    // Attempt auto-fixes
    await this.attemptAutoFixes(health);

    // Log health status
    this.logHealthStatus(health);

    // Store in history
    this.healthHistory.push(health);
    if (this.healthHistory.length > 100) {
      this.healthHistory.shift(); // Keep last 100 checks
    }

    return health;
  }

  /**
   * Check configuration health
   */
  private async checkConfigurationHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: { [key: string]: any } = {};

    try {
      // Verify target functions
      const verification = await signatureVerifier.verifyAllTargetFunctions(CONFIG.targetFunctions);
      metrics.totalFunctions = verification.summary.total;
      metrics.verifiedFunctions = verification.summary.verified;
      metrics.failedFunctions = verification.summary.failed;
      metrics.avgConfidence = verification.summary.confidence;

      if (verification.summary.failed > 0) {
        issues.push(`${verification.summary.failed} function signatures failed verification`);
      }

      // Check chain configurations
      const validChains = CONFIG.chains.filter(c => c.rpcUrl && c.etherscanApiKey);
      metrics.totalChains = CONFIG.chains.length;
      metrics.validChains = validChains.length;

      if (validChains.length < CONFIG.chains.length) {
        issues.push(`${CONFIG.chains.length - validChains.length} chains have invalid configuration`);
      }

      // Check environment variables
      const requiredEnvVars = ['ETHERSCAN_API_KEY', 'ETHEREUM_RPC_URL'];
      const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
      
      if (missingVars.length > 0) {
        issues.push(`Missing environment variables: ${missingVars.join(', ')}`);
      }

    } catch (error) {
      issues.push(`Configuration check failed: ${error}`);
    }

    return {
      name: 'Configuration',
      status: issues.length === 0 ? 'HEALTHY' : issues.length <= 2 ? 'WARNING' : 'CRITICAL',
      metrics,
      issues,
      lastCheck: Date.now()
    };
  }

  /**
   * Check network connectivity health
   */
  private async checkNetworkHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: { [key: string]: any } = {};

    try {
      const networkTests = await Promise.allSettled(
        CONFIG.chains.map(async (chain) => {
          if (!chain.rpcUrl) return { chain: chain.name, status: 'SKIPPED' };

          const startTime = Date.now();
          const provider = new ethers.JsonRpcProvider(chain.rpcUrl);
          
          try {
            const blockNumber = await provider.getBlockNumber();
            const responseTime = Date.now() - startTime;
            
            return {
              chain: chain.name,
              status: 'HEALTHY',
              blockNumber,
              responseTime
            };
          } catch (error) {
            return {
              chain: chain.name,
              status: 'FAILED',
              error: error instanceof Error ? error.message : 'Unknown error'
            };
          }
        })
      );

      let healthyChains = 0;
      let totalResponseTime = 0;
      let responseCount = 0;

      networkTests.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const test = result.value;
          if (test.status === 'HEALTHY') {
            healthyChains++;
            if (test.responseTime) {
              totalResponseTime += test.responseTime;
              responseCount++;
            }
          } else if (test.status === 'FAILED') {
            issues.push(`${test.chain}: ${test.error}`);
          }
        }
      });

      metrics.totalChains = CONFIG.chains.length;
      metrics.healthyChains = healthyChains;
      metrics.avgResponseTime = responseCount > 0 ? Math.round(totalResponseTime / responseCount) : 0;

    } catch (error) {
      issues.push(`Network health check failed: ${error}`);
    }

    return {
      name: 'Network',
      status: issues.length === 0 ? 'HEALTHY' : issues.length <= 2 ? 'WARNING' : 'CRITICAL',
      metrics,
      issues,
      lastCheck: Date.now()
    };
  }

  /**
   * Check signature verification system
   */
  private async checkSignatureVerification(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: { [key: string]: any } = {};

    try {
      // Test signature verification with known good signature
      const testSignature = 'claim()';
      const verification = await signatureVerifier.verifySignature(testSignature, '0x4e71d92d');
      
      metrics.testVerificationSuccess = verification.isVerified;
      metrics.testConfidence = verification.confidence;
      metrics.testSources = verification.sources.length;

      if (!verification.isVerified) {
        issues.push('Signature verification system failed test');
      }

      if (verification.confidence < 80) {
        issues.push('Low confidence in signature verification');
      }

    } catch (error) {
      issues.push(`Signature verification check failed: ${error}`);
    }

    return {
      name: 'Signature Verification',
      status: issues.length === 0 ? 'HEALTHY' : 'CRITICAL',
      metrics,
      issues,
      lastCheck: Date.now()
    };
  }

  /**
   * Check economic engine
   */
  private async checkEconomicEngine(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: { [key: string]: any } = {};

    try {
      // Test economic analysis with sample data
      const testAnalysis = await economicEngine.analyzeEconomics(
        1, // Ethereum
        ethers.parseEther('1'), // 1 ETH value
        100000n, // 100k gas
        []
      );

      metrics.gasPriceSource = testAnalysis.gasPrice.source;
      metrics.gasPriceAge = Date.now() - testAnalysis.gasPrice.timestamp;
      metrics.profitCalculationSuccess = testAnalysis.profitability.netProfit >= 0n;

      if (testAnalysis.gasPrice.source === 'Fallback') {
        issues.push('Using fallback gas prices - all external sources failed');
      }

      if (metrics.gasPriceAge > 60000) { // Older than 1 minute
        issues.push('Gas price data is stale');
      }

    } catch (error) {
      issues.push(`Economic engine check failed: ${error}`);
    }

    return {
      name: 'Economic Engine',
      status: issues.length === 0 ? 'HEALTHY' : issues.length <= 1 ? 'WARNING' : 'CRITICAL',
      metrics,
      issues,
      lastCheck: Date.now()
    };
  }

  /**
   * Check memory health
   */
  private async checkMemoryHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: { [key: string]: any } = {};

    try {
      const memUsage = process.memoryUsage();
      
      metrics.heapUsed = Math.round(memUsage.heapUsed / 1024 / 1024); // MB
      metrics.heapTotal = Math.round(memUsage.heapTotal / 1024 / 1024); // MB
      metrics.external = Math.round(memUsage.external / 1024 / 1024); // MB
      metrics.rss = Math.round(memUsage.rss / 1024 / 1024); // MB

      // Check for memory issues
      if (metrics.heapUsed > 500) { // More than 500MB
        issues.push('High heap memory usage');
      }

      if (metrics.rss > 1000) { // More than 1GB
        issues.push('High RSS memory usage');
      }

      this.metrics.memoryUsage = metrics.heapUsed;

    } catch (error) {
      issues.push(`Memory health check failed: ${error}`);
    }

    return {
      name: 'Memory',
      status: issues.length === 0 ? 'HEALTHY' : issues.length <= 1 ? 'WARNING' : 'CRITICAL',
      metrics,
      issues,
      lastCheck: Date.now()
    };
  }

  /**
   * Check API health
   */
  private async checkAPIHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: { [key: string]: any } = {};

    try {
      // Calculate API calls per minute
      const oneMinuteAgo = Date.now() - 60000;
      const recentCalls = this.apiCalls.filter(call => call.timestamp > oneMinuteAgo);
      
      metrics.apiCallsPerMinute = recentCalls.length;
      metrics.totalApiCalls = this.apiCalls.length;

      // Check rate limits
      if (metrics.apiCallsPerMinute > 100) { // Etherscan limit is typically 5/sec = 300/min
        issues.push('High API call rate - approaching rate limits');
      }

      this.metrics.apiCallsPerMinute = metrics.apiCallsPerMinute;

    } catch (error) {
      issues.push(`API health check failed: ${error}`);
    }

    return {
      name: 'API',
      status: issues.length === 0 ? 'HEALTHY' : 'WARNING',
      metrics,
      issues,
      lastCheck: Date.now()
    };
  }

  /**
   * Update system metrics
   */
  private updateMetrics(): void {
    this.metrics.uptime = Date.now() - this.startTime;
    this.metrics.totalScans = this.scanCount;
    this.metrics.successRate = this.scanCount > 0 ? 
      Math.round(((this.scanCount - this.errorCount) / this.scanCount) * 100) : 100;
    this.metrics.avgResponseTime = this.responseTimes.length > 0 ?
      Math.round(this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length) : 0;
    this.metrics.errorRate = this.scanCount > 0 ?
      Math.round((this.errorCount / this.scanCount) * 100) : 0;
  }

  /**
   * Determine overall system health
   */
  private determineOverallHealth(components: ComponentHealth[]): 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'FAILED' {
    const criticalComponents = components.filter(c => c.status === 'CRITICAL' || c.status === 'FAILED');
    const warningComponents = components.filter(c => c.status === 'WARNING');

    if (criticalComponents.length > 0) {
      return criticalComponents.some(c => c.status === 'FAILED') ? 'FAILED' : 'CRITICAL';
    }

    if (warningComponents.length > 2) {
      return 'CRITICAL';
    }

    if (warningComponents.length > 0) {
      return 'WARNING';
    }

    return 'HEALTHY';
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(components: ComponentHealth[], issues: SystemIssue[]): string[] {
    const recommendations: string[] = [];

    // Critical issues first
    const criticalIssues = issues.filter(i => i.severity === 'CRITICAL');
    if (criticalIssues.length > 0) {
      recommendations.push('Address critical issues immediately to restore system functionality');
    }

    // Configuration recommendations
    const configComponent = components.find(c => c.name === 'Configuration');
    if (configComponent && configComponent.status !== 'HEALTHY') {
      recommendations.push('Review and fix configuration issues');
    }

    // Network recommendations
    const networkComponent = components.find(c => c.name === 'Network');
    if (networkComponent && networkComponent.status !== 'HEALTHY') {
      recommendations.push('Check network connectivity and RPC endpoints');
    }

    // Performance recommendations
    if (this.metrics.errorRate > 10) {
      recommendations.push('High error rate detected - investigate system stability');
    }

    if (this.metrics.avgResponseTime > 5000) {
      recommendations.push('Slow response times - consider optimizing or scaling');
    }

    return recommendations;
  }

  /**
   * Determine issue severity
   */
  private determineSeverity(status: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    switch (status) {
      case 'FAILED': return 'CRITICAL';
      case 'CRITICAL': return 'CRITICAL';
      case 'WARNING': return 'MEDIUM';
      default: return 'LOW';
    }
  }

  /**
   * Check if issue is auto-fixable
   */
  private isAutoFixable(issue: string): boolean {
    const autoFixablePatterns = [
      'cache',
      'stale',
      'memory',
      'rate limit'
    ];

    return autoFixablePatterns.some(pattern => 
      issue.toLowerCase().includes(pattern)
    );
  }

  /**
   * Attempt automatic fixes
   */
  private async attemptAutoFixes(health: SystemHealth): Promise<void> {
    const autoFixableIssues = health.issues.filter(i => i.autoFixable && !i.fixAttempted);

    for (const issue of autoFixableIssues) {
      try {
        logger.ghost(`🔧 Attempting auto-fix: ${issue.description}`);

        if (issue.description.includes('cache')) {
          // Clear caches
          signatureVerifier.clearCache();
          economicEngine.clearCache();
          logger.extract('✅ Caches cleared');
        }

        if (issue.description.includes('memory')) {
          // Force garbage collection if available
          if (global.gc) {
            global.gc();
            logger.extract('✅ Garbage collection triggered');
          }
        }

        if (issue.description.includes('rate limit')) {
          // Implement rate limiting delay
          await new Promise(resolve => setTimeout(resolve, 5000));
          logger.extract('✅ Rate limiting delay applied');
        }

        issue.fixAttempted = true;

      } catch (error) {
        logger.mark(`❌ Auto-fix failed for: ${issue.description} - ${error}`);
      }
    }
  }

  /**
   * Log health status
   */
  private logHealthStatus(health: SystemHealth): void {
    const { overall, components, metrics, issues } = health;

    if (overall === 'HEALTHY') {
      logger.extract(`🎯 SYSTEM HEALTH: ${overall} - All systems operational`);
    } else {
      logger.mark(`⚠️ SYSTEM HEALTH: ${overall} - ${issues.length} issues detected`);
    }

    logger.ghost(`📊 Metrics: ${metrics.totalScans} scans, ${metrics.successRate}% success rate, ${metrics.avgResponseTime}ms avg response`);

    // Log critical issues
    const criticalIssues = issues.filter(i => i.severity === 'CRITICAL');
    if (criticalIssues.length > 0) {
      logger.mark(`🚨 CRITICAL ISSUES:`);
      criticalIssues.forEach(issue => {
        logger.mark(`   ${issue.component}: ${issue.description}`);
      });
    }
  }

  /**
   * Start continuous monitoring
   */
  private startContinuousMonitoring(): void {
    // Perform health check every 5 minutes
    setInterval(() => {
      this.performHealthCheck().catch(error => {
        logger.error(`Health check failed: ${error}`);
      });
    }, 5 * 60 * 1000);

    logger.ghost('🔍 Continuous monitoring started');
  }

  /**
   * Record scan metrics
   */
  recordScan(success: boolean, responseTime: number): void {
    this.scanCount++;
    if (!success) this.errorCount++;
    
    this.responseTimes.push(responseTime);
    if (this.responseTimes.length > 100) {
      this.responseTimes.shift(); // Keep last 100 response times
    }
  }

  /**
   * Record API call
   */
  recordApiCall(): void {
    this.apiCalls.push({ timestamp: Date.now() });
    
    // Clean old API calls (older than 1 hour)
    const oneHourAgo = Date.now() - 3600000;
    this.apiCalls = this.apiCalls.filter(call => call.timestamp > oneHourAgo);
  }

  /**
   * Get current system health
   */
  getCurrentHealth(): SystemHealth | null {
    return this.healthHistory.length > 0 ? 
      this.healthHistory[this.healthHistory.length - 1] : null;
  }

  /**
   * Get health history
   */
  getHealthHistory(): SystemHealth[] {
    return [...this.healthHistory];
  }
}

export const selfMonitor = new SelfMonitor();
