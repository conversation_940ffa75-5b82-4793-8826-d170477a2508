import { logger } from './logger';
import { ContractInfo, ChainConfig } from './types';
import { EmptyMerkleDetector, MerkleRootAnalysis } from './empty-merkle-detector';
import { CONFIG } from './config';

export interface FrontendIndicator {
  type: 'website' | 'github' | 'social' | 'docs' | 'app';
  url: string;
  status: 'active' | 'inactive' | 'not_found';
  lastChecked: Date;
}

export interface PreFrontendAnalysis {
  contractAddress: string;
  chainId: number;
  deploymentTime: Date;
  frontendStatus: 'none' | 'development' | 'live';
  frontendIndicators: FrontendIndicator[];
  claimFunctionStatus: 'available' | 'restricted' | 'not_found';
  merkleRootStatus: 'empty' | 'populated' | 'not_applicable';
  timeWindow: 'golden' | 'silver' | 'bronze' | 'expired';
  actionPriority: number; // 1-5, lower = higher priority
  confidence: number; // 0-100, confidence this is pre-frontend
  estimatedFrontendLaunch: Date | null;
}

export interface PreFrontendContract {
  contract: ContractInfo;
  analysis: PreFrontendAnalysis;
  merkleAnalysis?: MerkleRootAnalysis;
  actionRequired: boolean;
  timeRemaining: number; // Hours until likely frontend launch
}

export class PreFrontendAnalyzer {
  private readonly merkleDetector: EmptyMerkleDetector;
  
  // Common patterns that indicate frontend development
  private readonly frontendPatterns = [
    // Website patterns
    { pattern: /https?:\/\/(?:www\.)?([^\/]+)/, type: 'website' as const },
    { pattern: /app\.([^\/]+)/, type: 'app' as const },
    { pattern: /claim\.([^\/]+)/, type: 'app' as const },
    
    // GitHub patterns
    { pattern: /github\.com\/([^\/]+\/[^\/]+)/, type: 'github' as const },
    
    // Documentation patterns
    { pattern: /docs\.([^\/]+)/, type: 'docs' as const },
    { pattern: /gitbook\.io/, type: 'docs' as const },
    
    // Social patterns
    { pattern: /twitter\.com\/([^\/]+)/, type: 'social' as const },
    { pattern: /t\.me\/([^\/]+)/, type: 'social' as const },
    { pattern: /discord\.gg\/([^\/]+)/, type: 'social' as const }
  ];

  // Time windows for pre-frontend analysis
  private readonly timeWindows = {
    golden: { hours: 24, priority: 1, description: 'First 24 hours - maximum alpha' },
    silver: { hours: 72, priority: 2, description: '24-72 hours - high alpha' },
    bronze: { hours: 168, priority: 3, description: '3-7 days - moderate alpha' },
    expired: { hours: Infinity, priority: 5, description: 'Over 1 week - low alpha' }
  };

  constructor() {
    this.merkleDetector = new EmptyMerkleDetector();
  }

  /**
   * Analyze contract to determine if it's pre-frontend
   */
  async analyzeContract(contract: ContractInfo): Promise<PreFrontendAnalysis> {
    logger.debug(`🔍 Analyzing ${contract.address} for pre-frontend status...`);

    const deploymentTime = new Date(contract.timestamp * 1000);
    const frontendIndicators = await this.findFrontendIndicators(contract);
    const frontendStatus = this.determineFrontendStatus(frontendIndicators);
    const claimFunctionStatus = await this.analyzeClaimFunctions(contract);
    const merkleRootStatus = await this.analyzeMerkleRootStatus(contract);
    const timeWindow = this.getTimeWindow(deploymentTime);
    const actionPriority = this.calculateActionPriority(
      frontendStatus, 
      claimFunctionStatus, 
      merkleRootStatus, 
      timeWindow
    );
    const confidence = this.calculateConfidence(
      contract, 
      frontendStatus, 
      claimFunctionStatus, 
      merkleRootStatus, 
      timeWindow
    );
    const estimatedFrontendLaunch = this.estimateFrontendLaunch(
      deploymentTime, 
      frontendStatus, 
      frontendIndicators
    );

    const analysis: PreFrontendAnalysis = {
      contractAddress: contract.address,
      chainId: contract.chainId,
      deploymentTime,
      frontendStatus,
      frontendIndicators,
      claimFunctionStatus,
      merkleRootStatus,
      timeWindow,
      actionPriority,
      confidence,
      estimatedFrontendLaunch
    };

    if (confidence >= 70 && actionPriority <= 2) {
      logger.extract(`🎯 PRE-FRONTEND CONTRACT DETECTED: ${contract.address} (confidence: ${confidence}%)`);
    }

    return analysis;
  }

  /**
   * Find indicators of frontend development
   */
  private async findFrontendIndicators(contract: ContractInfo): Promise<FrontendIndicator[]> {
    const indicators: FrontendIndicator[] = [];
    
    // Search in contract source code and comments
    const searchText = `${contract.name} ${contract.sourceCode || ''}`;
    
    for (const { pattern, type } of this.frontendPatterns) {
      const matches = searchText.match(pattern);
      if (matches) {
        for (const match of matches) {
          const url = this.extractUrlFromMatch(match, type);
          if (url) {
            const status = await this.checkUrlStatus(url);
            indicators.push({
              type,
              url,
              status,
              lastChecked: new Date()
            });
          }
        }
      }
    }

    // Check for common frontend domains based on contract name
    const contractName = contract.name.toLowerCase();
    const commonDomains = [
      `${contractName}.com`,
      `${contractName}.io`,
      `${contractName}.xyz`,
      `app.${contractName}.com`,
      `claim.${contractName}.com`
    ];

    for (const domain of commonDomains) {
      const url = `https://${domain}`;
      const status = await this.checkUrlStatus(url);
      if (status !== 'not_found') {
        indicators.push({
          type: 'website',
          url,
          status,
          lastChecked: new Date()
        });
      }
    }

    return indicators;
  }

  /**
   * Extract URL from regex match
   */
  private extractUrlFromMatch(match: string, type: string): string | null {
    if (type === 'website' || type === 'app' || type === 'docs') {
      return match.startsWith('http') ? match : `https://${match}`;
    }
    
    if (type === 'github') {
      return match.startsWith('http') ? match : `https://${match}`;
    }
    
    if (type === 'social') {
      return match.startsWith('http') ? match : `https://${match}`;
    }
    
    return null;
  }

  /**
   * Check if URL is accessible
   */
  private async checkUrlStatus(url: string): Promise<'active' | 'inactive' | 'not_found'> {
    try {
      // For demo purposes, we'll simulate URL checking
      // In production, you'd implement actual HTTP requests with proper error handling
      
      // Simulate different response scenarios
      const random = Math.random();
      if (random < 0.1) return 'active';    // 10% chance of active frontend
      if (random < 0.3) return 'inactive';  // 20% chance of inactive/development
      return 'not_found';                   // 70% chance of not found
      
    } catch (error) {
      return 'not_found';
    }
  }

  /**
   * Determine overall frontend status
   */
  private determineFrontendStatus(indicators: FrontendIndicator[]): 'none' | 'development' | 'live' {
    if (indicators.length === 0) {
      return 'none';
    }

    const activeIndicators = indicators.filter(i => i.status === 'active');
    const inactiveIndicators = indicators.filter(i => i.status === 'inactive');

    if (activeIndicators.length > 0) {
      // Check if it's a full frontend or just placeholder
      const hasAppOrClaim = activeIndicators.some(i => 
        i.type === 'app' || i.url.includes('claim') || i.url.includes('app')
      );
      
      return hasAppOrClaim ? 'live' : 'development';
    }

    if (inactiveIndicators.length > 0) {
      return 'development';
    }

    return 'none';
  }

  /**
   * Analyze claim function availability
   */
  private async analyzeClaimFunctions(contract: ContractInfo): Promise<'available' | 'restricted' | 'not_found'> {
    if (!contract.abi) {
      return 'not_found';
    }

    try {
      const abi = JSON.parse(contract.abi);
      const claimFunctions = abi.filter((item: any) => 
        item.type === 'function' && 
        (item.name?.includes('claim') || item.name?.includes('withdraw'))
      );

      if (claimFunctions.length === 0) {
        return 'not_found';
      }

      // Analyze function modifiers and requirements
      for (const func of claimFunctions) {
        // Check for common restriction patterns in function name or inputs
        const funcString = JSON.stringify(func).toLowerCase();
        
        if (funcString.includes('onlyowner') || 
            funcString.includes('onlyadmin') || 
            funcString.includes('paused')) {
          return 'restricted';
        }
      }

      return 'available';
    } catch (error) {
      return 'not_found';
    }
  }

  /**
   * Analyze merkle root status
   */
  private async analyzeMerkleRootStatus(contract: ContractInfo): Promise<'empty' | 'populated' | 'not_applicable'> {
    try {
      const merkleAnalysis = await this.merkleDetector.analyzeContract(contract);
      
      if (!merkleAnalysis || merkleAnalysis.merkleRoots.length === 0) {
        return 'not_applicable';
      }

      if (merkleAnalysis.isEmpty || merkleAnalysis.isZero) {
        return 'empty';
      }

      return 'populated';
    } catch (error) {
      return 'not_applicable';
    }
  }

  /**
   * Get time window based on deployment time
   */
  private getTimeWindow(deploymentTime: Date): 'golden' | 'silver' | 'bronze' | 'expired' {
    const ageHours = (Date.now() - deploymentTime.getTime()) / (1000 * 60 * 60);
    
    if (ageHours <= this.timeWindows.golden.hours) return 'golden';
    if (ageHours <= this.timeWindows.silver.hours) return 'silver';
    if (ageHours <= this.timeWindows.bronze.hours) return 'bronze';
    return 'expired';
  }

  /**
   * Calculate action priority
   */
  private calculateActionPriority(
    frontendStatus: string,
    claimFunctionStatus: string,
    merkleRootStatus: string,
    timeWindow: string
  ): number {
    let priority = 5; // Default low priority

    // High priority for no frontend + available claims
    if (frontendStatus === 'none' && claimFunctionStatus === 'available') {
      priority = 1;
    }

    // High priority for empty merkle roots
    if (merkleRootStatus === 'empty') {
      priority = Math.min(priority, 2);
    }

    // Adjust based on time window
    const windowPriority = this.timeWindows[timeWindow as keyof typeof this.timeWindows].priority;
    priority = Math.min(priority, windowPriority);

    return priority;
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(
    contract: ContractInfo,
    frontendStatus: string,
    claimFunctionStatus: string,
    merkleRootStatus: string,
    timeWindow: string
  ): number {
    let confidence = 0;

    // Base confidence for no frontend
    if (frontendStatus === 'none') {
      confidence += 40;
    } else if (frontendStatus === 'development') {
      confidence += 25;
    }

    // High confidence for available claim functions
    if (claimFunctionStatus === 'available') {
      confidence += 30;
    }

    // High confidence for empty merkle roots
    if (merkleRootStatus === 'empty') {
      confidence += 25;
    }

    // Bonus for golden/silver time windows
    if (timeWindow === 'golden') {
      confidence += 15;
    } else if (timeWindow === 'silver') {
      confidence += 10;
    }

    // Bonus for airdrop keywords
    const contractText = `${contract.name} ${contract.sourceCode || ''}`.toLowerCase();
    const airdropKeywords = ['airdrop', 'claim', 'merkle', 'distribution'];
    const foundKeywords = airdropKeywords.filter(k => contractText.includes(k));
    confidence += foundKeywords.length * 5;

    return Math.min(confidence, 100);
  }

  /**
   * Estimate when frontend might launch
   */
  private estimateFrontendLaunch(
    deploymentTime: Date,
    frontendStatus: string,
    indicators: FrontendIndicator[]
  ): Date | null {
    const ageHours = (Date.now() - deploymentTime.getTime()) / (1000 * 60 * 60);
    
    if (frontendStatus === 'live') {
      return deploymentTime; // Already live
    }

    if (frontendStatus === 'development') {
      // Estimate 24-72 hours from now
      return new Date(Date.now() + (24 + Math.random() * 48) * 60 * 60 * 1000);
    }

    if (frontendStatus === 'none') {
      // Estimate based on typical patterns
      if (ageHours < 24) {
        // Very fresh, might launch in 1-3 days
        return new Date(Date.now() + (24 + Math.random() * 48) * 60 * 60 * 1000);
      } else if (ageHours < 72) {
        // Older, might launch in 1-7 days
        return new Date(Date.now() + (24 + Math.random() * 144) * 60 * 60 * 1000);
      }
    }

    return null; // Can't estimate
  }

  /**
   * Find all pre-frontend contracts
   */
  async findPreFrontendContracts(contracts: ContractInfo[]): Promise<PreFrontendContract[]> {
    const preFrontendContracts: PreFrontendContract[] = [];
    
    logger.info(`🔍 Analyzing ${contracts.length} contracts for pre-frontend status...`);

    for (const contract of contracts) {
      const analysis = await this.analyzeContract(contract);
      
      // Only include high-confidence, high-priority contracts
      if (analysis.confidence >= 60 && analysis.actionPriority <= 3) {
        const merkleAnalysis = await this.merkleDetector.analyzeContract(contract);
        const actionRequired = analysis.actionPriority <= 2 && analysis.timeWindow !== 'expired';
        const timeRemaining = this.calculateTimeRemaining(analysis);

        preFrontendContracts.push({
          contract,
          analysis,
          merkleAnalysis: merkleAnalysis || undefined,
          actionRequired,
          timeRemaining
        });
      }
    }

    // Sort by priority and time window
    preFrontendContracts.sort((a, b) => {
      if (a.analysis.actionPriority !== b.analysis.actionPriority) {
        return a.analysis.actionPriority - b.analysis.actionPriority;
      }
      return a.timeRemaining - b.timeRemaining; // Less time remaining = higher priority
    });

    if (preFrontendContracts.length > 0) {
      logger.extract(`🎯 Found ${preFrontendContracts.length} pre-frontend contracts`);
    }

    return preFrontendContracts;
  }

  /**
   * Calculate time remaining until likely frontend launch
   */
  private calculateTimeRemaining(analysis: PreFrontendAnalysis): number {
    if (analysis.estimatedFrontendLaunch) {
      const timeRemaining = (analysis.estimatedFrontendLaunch.getTime() - Date.now()) / (1000 * 60 * 60);
      return Math.max(0, timeRemaining);
    }

    // Default estimates based on time window
    const ageHours = (Date.now() - analysis.deploymentTime.getTime()) / (1000 * 60 * 60);
    
    if (analysis.timeWindow === 'golden') {
      return Math.max(0, 48 - ageHours); // Assume 48 hour window
    } else if (analysis.timeWindow === 'silver') {
      return Math.max(0, 72 - ageHours); // Assume 72 hour window
    } else if (analysis.timeWindow === 'bronze') {
      return Math.max(0, 168 - ageHours); // Assume 1 week window
    }

    return 0;
  }

  /**
   * Get contracts in golden window (highest priority)
   */
  async getGoldenWindowContracts(contracts: ContractInfo[]): Promise<PreFrontendContract[]> {
    const allContracts = await this.findPreFrontendContracts(contracts);
    return allContracts.filter(c => c.analysis.timeWindow === 'golden');
  }

  /**
   * Get contracts requiring immediate action
   */
  async getImmediateActionContracts(contracts: ContractInfo[]): Promise<PreFrontendContract[]> {
    const allContracts = await this.findPreFrontendContracts(contracts);
    return allContracts.filter(c => c.actionRequired && c.timeRemaining <= 12); // Next 12 hours
  }
}
