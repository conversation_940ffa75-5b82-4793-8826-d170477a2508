import { ethers } from 'ethers';
import { logger } from './logger';

/**
 * 🏺 DUST COLLECTION: Batch Optimization System
 * Combines multiple small extractions into single transactions for gas efficiency
 */

export interface BatchableExtraction {
  contractAddress: string;
  functionName: string;
  parameters: any[];
  estimatedValue: bigint;
  gasEstimate: number;
  chainId: number;
}

export interface BatchResult {
  totalValue: bigint;
  totalGas: number;
  netProfit: bigint;
  extractions: BatchableExtraction[];
  multicallData: string;
}

export class BatchOptimizer {
  private readonly MULTICALL_ADDRESS = '******************************************'; // Multicall3
  private readonly MIN_BATCH_SIZE = 2;
  private readonly MAX_BATCH_SIZE = 10;
  private readonly GAS_OVERHEAD_PER_CALL = 5000; // Additional gas per batched call

  /**
   * 🏺 DUST COLLECTION: Optimize extractions by batching compatible ones
   */
  async optimizeExtractions(extractions: BatchableExtraction[]): Promise<BatchResult[]> {
    logger.relic('🔄 Optimizing extractions for batch processing...');
    
    // Group by chain ID first
    const chainGroups = this.groupByChain(extractions);
    const optimizedBatches: BatchResult[] = [];

    for (const [chainId, chainExtractions] of chainGroups) {
      logger.debug(`Processing ${chainExtractions.length} extractions on chain ${chainId}`);
      
      // Sort by gas efficiency (value/gas ratio)
      const sortedExtractions = chainExtractions.sort((a, b) => {
        const ratioA = Number(a.estimatedValue) / a.gasEstimate;
        const ratioB = Number(b.estimatedValue) / b.gasEstimate;
        return ratioB - ratioA; // Descending order
      });

      // Create optimal batches
      const batches = this.createOptimalBatches(sortedExtractions);
      optimizedBatches.push(...batches);
    }

    logger.extract(`✅ Created ${optimizedBatches.length} optimized batches from ${extractions.length} extractions`);
    return optimizedBatches;
  }

  /**
   * 🏺 DUST COLLECTION: Group extractions by blockchain
   */
  private groupByChain(extractions: BatchableExtraction[]): Map<number, BatchableExtraction[]> {
    const groups = new Map<number, BatchableExtraction[]>();
    
    for (const extraction of extractions) {
      if (!groups.has(extraction.chainId)) {
        groups.set(extraction.chainId, []);
      }
      groups.get(extraction.chainId)!.push(extraction);
    }
    
    return groups;
  }

  /**
   * 🏺 DUST COLLECTION: Create optimal batches using greedy algorithm
   */
  private createOptimalBatches(extractions: BatchableExtraction[]): BatchResult[] {
    const batches: BatchResult[] = [];
    const remaining = [...extractions];

    while (remaining.length >= this.MIN_BATCH_SIZE) {
      const batch = this.createSingleBatch(remaining);
      if (batch) {
        batches.push(batch);
        // Remove batched extractions from remaining
        for (const extraction of batch.extractions) {
          const index = remaining.findIndex(e => 
            e.contractAddress === extraction.contractAddress && 
            e.functionName === extraction.functionName
          );
          if (index >= 0) {
            remaining.splice(index, 1);
          }
        }
      } else {
        break; // No more profitable batches possible
      }
    }

    // Handle remaining single extractions if they're still profitable
    for (const extraction of remaining) {
      if (this.isSingleExtractionProfitable(extraction)) {
        batches.push(this.createSingleExtractionBatch(extraction));
      }
    }

    return batches;
  }

  /**
   * 🏺 DUST COLLECTION: Create a single optimized batch
   */
  private createSingleBatch(extractions: BatchableExtraction[]): BatchResult | null {
    let bestBatch: BatchResult | null = null;
    let bestProfit = 0n;

    // Try different batch sizes
    for (let size = this.MIN_BATCH_SIZE; size <= Math.min(this.MAX_BATCH_SIZE, extractions.length); size++) {
      const batch = this.createBatchOfSize(extractions.slice(0, size));
      if (batch && batch.netProfit > bestProfit) {
        bestBatch = batch;
        bestProfit = batch.netProfit;
      }
    }

    return bestBatch;
  }

  /**
   * 🏺 DUST COLLECTION: Create batch of specific size
   */
  private createBatchOfSize(extractions: BatchableExtraction[]): BatchResult | null {
    if (extractions.length === 0) return null;

    const totalValue = extractions.reduce((sum, e) => sum + e.estimatedValue, 0n);
    const totalGas = extractions.reduce((sum, e) => sum + e.gasEstimate, 0) + 
                    (this.GAS_OVERHEAD_PER_CALL * extractions.length);

    // Calculate gas cost (20 gwei)
    const gasPrice = 20e9; // 20 gwei
    const gasCostWei = BigInt(totalGas) * BigInt(gasPrice);
    const netProfit = totalValue - gasCostWei;

    // Check if batch is profitable (1.1x threshold)
    const profitThreshold = gasCostWei * 11n / 10n; // 1.1x
    if (totalValue < profitThreshold) {
      return null;
    }

    // Generate multicall data
    const multicallData = this.generateMulticallData(extractions);

    return {
      totalValue,
      totalGas,
      netProfit,
      extractions,
      multicallData
    };
  }

  /**
   * 🏺 DUST COLLECTION: Generate multicall transaction data
   */
  private generateMulticallData(extractions: BatchableExtraction[]): string {
    const calls = extractions.map(extraction => {
      // Encode function call
      const iface = new ethers.Interface([
        `function ${extraction.functionName}(${this.getParameterTypes(extraction.parameters)})`
      ]);
      
      const calldata = iface.encodeFunctionData(extraction.functionName, extraction.parameters);
      
      return {
        target: extraction.contractAddress,
        callData: calldata
      };
    });

    // Encode multicall
    const multicallInterface = new ethers.Interface([
      'function aggregate(tuple(address target, bytes callData)[] calls) returns (uint256 blockNumber, bytes[] returnData)'
    ]);

    return multicallInterface.encodeFunctionData('aggregate', [calls]);
  }

  /**
   * 🏺 DUST COLLECTION: Get parameter types for function encoding
   */
  private getParameterTypes(parameters: any[]): string {
    return parameters.map(param => {
      if (typeof param === 'bigint' || typeof param === 'number') {
        return 'uint256';
      } else if (typeof param === 'string' && param.startsWith('0x') && param.length === 42) {
        return 'address';
      } else if (typeof param === 'boolean') {
        return 'bool';
      } else {
        return 'bytes';
      }
    }).join(',');
  }

  /**
   * 🏺 DUST COLLECTION: Check if single extraction is profitable
   */
  private isSingleExtractionProfitable(extraction: BatchableExtraction): boolean {
    const gasPrice = 20e9; // 20 gwei
    const gasCostWei = BigInt(extraction.gasEstimate) * BigInt(gasPrice);
    const profitThreshold = gasCostWei * 11n / 10n; // 1.1x
    
    return extraction.estimatedValue >= profitThreshold;
  }

  /**
   * 🏺 DUST COLLECTION: Create batch for single extraction
   */
  private createSingleExtractionBatch(extraction: BatchableExtraction): BatchResult {
    const gasPrice = 20e9; // 20 gwei
    const gasCostWei = BigInt(extraction.gasEstimate) * BigInt(gasPrice);
    
    return {
      totalValue: extraction.estimatedValue,
      totalGas: extraction.gasEstimate,
      netProfit: extraction.estimatedValue - gasCostWei,
      extractions: [extraction],
      multicallData: '' // Single calls don't need multicall
    };
  }

  /**
   * 🏺 DUST COLLECTION: Log batch optimization results
   */
  logBatchResults(batches: BatchResult[]): void {
    const totalExtractions = batches.reduce((sum, batch) => sum + batch.extractions.length, 0);
    const totalValue = batches.reduce((sum, batch) => sum + batch.totalValue, 0n);
    const totalProfit = batches.reduce((sum, batch) => sum + batch.netProfit, 0n);

    logger.extract(`🏺 BATCH OPTIMIZATION RESULTS:`);
    logger.extract(`   📦 ${batches.length} optimized batches created`);
    logger.extract(`   🎯 ${totalExtractions} total extractions`);
    logger.extract(`   💰 ${ethers.formatEther(totalValue)} ETH total value`);
    logger.extract(`   📈 ${ethers.formatEther(totalProfit)} ETH net profit`);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      logger.extract(`   Batch ${i + 1}: ${batch.extractions.length} calls, ${ethers.formatEther(batch.netProfit)} ETH profit`);
    }
  }
}
