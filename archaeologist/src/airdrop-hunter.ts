import { logger } from './logger';
import { ContractInfo } from './types';
import { FreshContractPipeline, FreshContractCandidate } from './fresh-contract-pipeline';
import { EmptyMerkleDetector, PreProofContract } from './empty-merkle-detector';
import { PreFrontendAnalyzer, PreFrontendContract } from './pre-frontend-analyzer';
import { EarlyClaimEngine, EarlyClaimResult } from './early-claim-engine';
import { ProtocolTracker, ProtocolDeploymentAlert } from './protocol-tracker';
import { Database } from './database';

export interface AirdropOpportunity {
  id: string;
  contract: ContractInfo;
  opportunityType: 'pre_proof' | 'pre_frontend' | 'fresh_deployment' | 'protocol_alert';
  priority: number; // 1-5, lower = higher priority
  confidence: number; // 0-100, higher = more confident
  timeWindow: 'golden' | 'silver' | 'bronze' | 'expired';
  timeRemaining: number; // Hours until opportunity likely expires
  actionRequired: boolean;
  estimatedValue: string; // ETH
  riskLevel: 'low' | 'medium' | 'high';
  discoveredAt: Date;
  lastUpdated: Date;
}

export interface AirdropHuntResult {
  totalOpportunities: number;
  goldenWindowOpportunities: number;
  immediateActionRequired: number;
  successfulClaims: number;
  totalClaimedValue: string;
  gasUsed: number;
  profitability: number;
  opportunities: AirdropOpportunity[];
  claimResults: EarlyClaimResult[];
}

export interface TimingPriority {
  timeWindow: string;
  priority: number;
  multiplier: number;
  description: string;
}

export class AirdropHunter {
  private readonly freshContractPipeline: FreshContractPipeline;
  private readonly emptyMerkleDetector: EmptyMerkleDetector;
  private readonly preFrontendAnalyzer: PreFrontendAnalyzer;
  private readonly earlyClaimEngine: EarlyClaimEngine;
  private readonly protocolTracker: ProtocolTracker;
  private readonly database: Database;

  // Timing-based priority system
  private readonly timingPriorities: TimingPriority[] = [
    {
      timeWindow: 'golden',
      priority: 1,
      multiplier: 3.0,
      description: 'First 24 hours - maximum alpha potential'
    },
    {
      timeWindow: 'silver', 
      priority: 2,
      multiplier: 2.0,
      description: '24-72 hours - high alpha potential'
    },
    {
      timeWindow: 'bronze',
      priority: 3,
      multiplier: 1.5,
      description: '3-7 days - moderate alpha potential'
    },
    {
      timeWindow: 'expired',
      priority: 5,
      multiplier: 0.5,
      description: 'Over 1 week - low alpha potential'
    }
  ];

  constructor() {
    this.freshContractPipeline = new FreshContractPipeline();
    this.emptyMerkleDetector = new EmptyMerkleDetector();
    this.preFrontendAnalyzer = new PreFrontendAnalyzer();
    this.earlyClaimEngine = new EarlyClaimEngine();
    this.protocolTracker = new ProtocolTracker();
    this.database = new Database();
  }

  /**
   * Main airdrop hunting function - discovers and prioritizes all opportunities
   */
  async huntAirdrops(executeImmediateActions: boolean = false): Promise<AirdropHuntResult> {
    logger.extract('🏺 INITIATING COMPREHENSIVE AIRDROP HUNT...');
    
    const opportunities: AirdropOpportunity[] = [];
    const claimResults: EarlyClaimResult[] = [];
    let totalClaimedValue = '0';
    let gasUsed = 0;

    // Step 1: Discover fresh contracts with airdrop potential
    logger.info('🔍 Phase 1: Discovering fresh contracts...');
    const freshCandidates = await this.freshContractPipeline.discoverFreshAirdropCandidates();
    
    // Step 2: Analyze for empty merkle roots (pre-proof opportunities)
    logger.info('🔍 Phase 2: Analyzing for empty merkle roots...');
    const contracts = freshCandidates.map(c => c.contract);
    const preProofContracts = await this.emptyMerkleDetector.findPreProofContracts(contracts);
    
    // Step 3: Analyze for pre-frontend opportunities
    logger.info('🔍 Phase 3: Analyzing for pre-frontend opportunities...');
    const preFrontendContracts = await this.preFrontendAnalyzer.findPreFrontendContracts(contracts);
    
    // Step 4: Check protocol deployment alerts
    logger.info('🔍 Phase 4: Checking protocol deployment alerts...');
    const protocolAlerts = await this.protocolTracker.checkAllProtocolsForDeployments();

    // Step 5: Convert all findings to opportunities and prioritize
    logger.info('🎯 Phase 5: Converting findings to prioritized opportunities...');
    
    // Add fresh contract opportunities
    for (const candidate of freshCandidates) {
      const opportunity = this.createOpportunityFromFreshContract(candidate);
      opportunities.push(opportunity);
    }

    // Add pre-proof opportunities (higher priority)
    for (const preProof of preProofContracts) {
      const opportunity = this.createOpportunityFromPreProof(preProof);
      opportunities.push(opportunity);
    }

    // Add pre-frontend opportunities
    for (const preFrontend of preFrontendContracts) {
      const opportunity = this.createOpportunityFromPreFrontend(preFrontend);
      opportunities.push(opportunity);
    }

    // Add protocol alert opportunities
    for (const alert of protocolAlerts) {
      for (const contract of alert.newContracts) {
        const opportunity = this.createOpportunityFromProtocolAlert(contract, alert);
        opportunities.push(opportunity);
      }
    }

    // Step 6: Apply timing-based prioritization
    const prioritizedOpportunities = this.applyTimingBasedPrioritization(opportunities);

    // Step 7: Execute immediate actions if requested
    if (executeImmediateActions) {
      logger.extract('⚡ Phase 6: Executing immediate actions...');
      const immediateOpportunities = prioritizedOpportunities.filter(o => o.actionRequired);
      
      for (const opportunity of immediateOpportunities) {
        try {
          const result = await this.earlyClaimEngine.attemptEarlyClaims(opportunity.contract, false);
          claimResults.push(result);
          
          if (result.successfulClaims.length > 0) {
            totalClaimedValue = this.addEthValues(totalClaimedValue, result.totalClaimedValue);
            gasUsed += result.gasUsed;
            
            logger.extract(`🏆 SUCCESSFUL AIRDROP CLAIM: ${result.totalClaimedValue} ETH from ${opportunity.contract.address}`);
          }
        } catch (error) {
          logger.error(`Error executing claims on ${opportunity.contract.address}:`, error);
        }
      }
    }

    // Step 8: Calculate results
    const goldenWindowOpportunities = prioritizedOpportunities.filter(o => o.timeWindow === 'golden').length;
    const immediateActionRequired = prioritizedOpportunities.filter(o => o.actionRequired).length;
    const successfulClaims = claimResults.filter(r => r.successfulClaims.length > 0).length;
    const profitability = this.calculateOverallProfitability(totalClaimedValue, gasUsed);

    const result: AirdropHuntResult = {
      totalOpportunities: prioritizedOpportunities.length,
      goldenWindowOpportunities,
      immediateActionRequired,
      successfulClaims,
      totalClaimedValue,
      gasUsed,
      profitability,
      opportunities: prioritizedOpportunities,
      claimResults
    };

    // Log summary
    this.logHuntSummary(result);

    return result;
  }

  /**
   * Create opportunity from fresh contract candidate
   */
  private createOpportunityFromFreshContract(candidate: FreshContractCandidate): AirdropOpportunity {
    return {
      id: `fresh_${candidate.contract.address}_${Date.now()}`,
      contract: candidate.contract,
      opportunityType: 'fresh_deployment',
      priority: candidate.priority,
      confidence: candidate.airdropPotential,
      timeWindow: this.getTimeWindowFromFreshness(candidate.freshness),
      timeRemaining: this.calculateTimeRemaining(candidate.deploymentTime),
      actionRequired: candidate.priority <= 2 && candidate.freshness >= 80,
      estimatedValue: this.estimateValue(candidate.airdropPotential),
      riskLevel: this.assessRiskLevel(candidate.priority, candidate.airdropPotential),
      discoveredAt: new Date(),
      lastUpdated: new Date()
    };
  }

  /**
   * Create opportunity from pre-proof contract
   */
  private createOpportunityFromPreProof(preProof: PreProofContract): AirdropOpportunity {
    return {
      id: `preproof_${preProof.contract.address}_${Date.now()}`,
      contract: preProof.contract,
      opportunityType: 'pre_proof',
      priority: preProof.analysis.priority,
      confidence: preProof.analysis.confidence,
      timeWindow: this.getTimeWindowFromHours(preProof.timeWindow),
      timeRemaining: preProof.estimatedTimeLeft,
      actionRequired: preProof.actionRequired,
      estimatedValue: this.estimateValueFromMerkle(preProof.analysis),
      riskLevel: 'low', // Pre-proof opportunities are generally low risk
      discoveredAt: new Date(),
      lastUpdated: new Date()
    };
  }

  /**
   * Create opportunity from pre-frontend contract
   */
  private createOpportunityFromPreFrontend(preFrontend: PreFrontendContract): AirdropOpportunity {
    return {
      id: `prefrontend_${preFrontend.contract.address}_${Date.now()}`,
      contract: preFrontend.contract,
      opportunityType: 'pre_frontend',
      priority: preFrontend.analysis.actionPriority,
      confidence: preFrontend.analysis.confidence,
      timeWindow: preFrontend.analysis.timeWindow,
      timeRemaining: preFrontend.timeRemaining,
      actionRequired: preFrontend.actionRequired,
      estimatedValue: this.estimateValueFromFrontend(preFrontend.analysis),
      riskLevel: this.assessFrontendRisk(preFrontend.analysis),
      discoveredAt: new Date(),
      lastUpdated: new Date()
    };
  }

  /**
   * Create opportunity from protocol alert
   */
  private createOpportunityFromProtocolAlert(
    contract: ContractInfo, 
    alert: ProtocolDeploymentAlert
  ): AirdropOpportunity {
    return {
      id: `protocol_${contract.address}_${Date.now()}`,
      contract,
      opportunityType: 'protocol_alert',
      priority: alert.priority,
      confidence: 80, // High confidence for known protocols
      timeWindow: 'golden', // Protocol alerts are usually fresh
      timeRemaining: 48, // Assume 48 hour window for protocol deployments
      actionRequired: alert.actionRequired,
      estimatedValue: this.estimateValueFromProtocol(alert.protocol.name),
      riskLevel: 'low', // Known protocols are low risk
      discoveredAt: new Date(),
      lastUpdated: new Date()
    };
  }

  /**
   * Apply timing-based prioritization to opportunities
   */
  private applyTimingBasedPrioritization(opportunities: AirdropOpportunity[]): AirdropOpportunity[] {
    // Apply timing multipliers to priority scores
    for (const opportunity of opportunities) {
      const timingPriority = this.timingPriorities.find(t => t.timeWindow === opportunity.timeWindow);
      if (timingPriority) {
        // Adjust priority based on timing (lower is better)
        opportunity.priority = Math.max(1, Math.floor(opportunity.priority / timingPriority.multiplier));
        
        // Boost confidence for golden window opportunities
        if (opportunity.timeWindow === 'golden') {
          opportunity.confidence = Math.min(100, opportunity.confidence + 15);
        }
      }
    }

    // Sort by priority, then confidence, then time remaining
    return opportunities.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority; // Lower priority number = higher priority
      }
      if (a.confidence !== b.confidence) {
        return b.confidence - a.confidence; // Higher confidence = higher priority
      }
      return a.timeRemaining - b.timeRemaining; // Less time remaining = higher priority
    });
  }

  /**
   * Get time window from freshness score
   */
  private getTimeWindowFromFreshness(freshness: number): 'golden' | 'silver' | 'bronze' | 'expired' {
    if (freshness >= 90) return 'golden';
    if (freshness >= 70) return 'silver';
    if (freshness >= 40) return 'bronze';
    return 'expired';
  }

  /**
   * Get time window from hours
   */
  private getTimeWindowFromHours(hours: number): 'golden' | 'silver' | 'bronze' | 'expired' {
    if (hours <= 24) return 'golden';
    if (hours <= 72) return 'silver';
    if (hours <= 168) return 'bronze';
    return 'expired';
  }

  /**
   * Calculate time remaining until opportunity expires
   */
  private calculateTimeRemaining(deploymentTime: Date): number {
    const ageHours = (Date.now() - deploymentTime.getTime()) / (1000 * 60 * 60);
    return Math.max(0, 168 - ageHours); // Assume 1 week window
  }

  /**
   * Estimate value based on airdrop potential
   */
  private estimateValue(airdropPotential: number): string {
    if (airdropPotential >= 80) return '1.0'; // 1 ETH
    if (airdropPotential >= 60) return '0.5'; // 0.5 ETH
    if (airdropPotential >= 40) return '0.1'; // 0.1 ETH
    return '0.01'; // 0.01 ETH
  }

  /**
   * Estimate value from merkle analysis
   */
  private estimateValueFromMerkle(analysis: any): string {
    if (analysis.claimAvailable && analysis.confidence >= 80) return '2.0';
    if (analysis.isEmpty && analysis.confidence >= 60) return '1.0';
    return '0.5';
  }

  /**
   * Estimate value from frontend analysis
   */
  private estimateValueFromFrontend(analysis: any): string {
    if (analysis.frontendStatus === 'none' && analysis.claimFunctionStatus === 'available') return '1.5';
    if (analysis.frontendStatus === 'development') return '1.0';
    return '0.5';
  }

  /**
   * Estimate value from protocol
   */
  private estimateValueFromProtocol(protocolName: string): string {
    const highValueProtocols = ['uniswap', 'aave', 'compound', 'eigenlayer'];
    if (highValueProtocols.some(p => protocolName.toLowerCase().includes(p))) {
      return '5.0'; // 5 ETH for major protocols
    }
    return '1.0';
  }

  /**
   * Assess risk level
   */
  private assessRiskLevel(priority: number, confidence: number): 'low' | 'medium' | 'high' {
    if (priority <= 2 && confidence >= 70) return 'low';
    if (priority <= 3 && confidence >= 50) return 'medium';
    return 'high';
  }

  /**
   * Assess frontend risk
   */
  private assessFrontendRisk(analysis: any): 'low' | 'medium' | 'high' {
    if (analysis.frontendStatus === 'none' && analysis.claimFunctionStatus === 'available') return 'low';
    if (analysis.frontendStatus === 'development') return 'medium';
    return 'high';
  }

  /**
   * Add ETH values
   */
  private addEthValues(value1: string, value2: string): string {
    const sum = parseFloat(value1) + parseFloat(value2);
    return sum.toFixed(6);
  }

  /**
   * Calculate overall profitability
   */
  private calculateOverallProfitability(claimedValue: string, gasUsed: number): number {
    if (gasUsed === 0) return 0;
    const gasCostEth = (gasUsed * 20) / 1e9; // 20 gwei gas price
    return parseFloat(claimedValue) / gasCostEth;
  }

  /**
   * Log hunt summary
   */
  private logHuntSummary(result: AirdropHuntResult): void {
    logger.extract('🏺 AIRDROP HUNT COMPLETE - SUMMARY:');
    logger.extract(`📊 Total Opportunities: ${result.totalOpportunities}`);
    logger.extract(`🥇 Golden Window: ${result.goldenWindowOpportunities}`);
    logger.extract(`⚡ Immediate Action: ${result.immediateActionRequired}`);
    logger.extract(`✅ Successful Claims: ${result.successfulClaims}`);
    logger.extract(`💰 Total Claimed: ${result.totalClaimedValue} ETH`);
    logger.extract(`⛽ Gas Used: ${result.gasUsed}`);
    logger.extract(`📈 Profitability: ${result.profitability.toFixed(2)}x`);
    
    if (result.goldenWindowOpportunities > 0) {
      logger.extract(`🎯 ${result.goldenWindowOpportunities} GOLDEN WINDOW OPPORTUNITIES DETECTED - IMMEDIATE ACTION RECOMMENDED`);
    }
  }

  /**
   * Start continuous airdrop hunting
   */
  async startContinuousHunting(intervalMinutes: number = 15): Promise<void> {
    logger.extract(`🔄 Starting continuous airdrop hunting (${intervalMinutes} minute intervals)`);
    
    const hunt = async () => {
      try {
        const result = await this.huntAirdrops(true); // Execute immediate actions
        
        if (result.goldenWindowOpportunities > 0) {
          logger.extract(`🚨 GOLDEN WINDOW ALERT: ${result.goldenWindowOpportunities} fresh opportunities detected!`);
        }
      } catch (error) {
        logger.error('Error in continuous airdrop hunting:', error);
      }
    };

    // Initial hunt
    await hunt();
    
    // Set up interval
    setInterval(hunt, intervalMinutes * 60 * 1000);
  }
}
