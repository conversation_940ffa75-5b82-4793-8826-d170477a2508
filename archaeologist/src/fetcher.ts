import axios from 'axios';
import { ContractInfo, ChainConfig } from './types';
import { logger } from './logger';
import { bytecodeDecompiler, DecompilationResult } from './decompiler';

export class ContractFetcher {
  private requestTimes: number[] = [];
  private readonly maxRequestsPerSecond = 5; // Free tier limit

  private async rateLimit(): Promise<void> {
    const now = Date.now();

    // Remove requests older than 1 second
    this.requestTimes = this.requestTimes.filter(time => now - time < 1000);

    // If we're at the limit, wait until we can make another request
    if (this.requestTimes.length >= this.maxRequestsPerSecond) {
      const oldestRequest = this.requestTimes[0];
      const waitTime = 1000 - (now - oldestRequest) + 10; // Add 10ms buffer
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
        return this.rateLimit(); // Recursive call to recheck
      }
    }

    // Record this request
    this.requestTimes.push(now);
  }

  // Process multiple addresses in parallel while respecting rate limits
  private async processAddressesBatch<T>(
    addresses: string[],
    processor: (address: string) => Promise<T | null>,
    batchSize: number = 3
  ): Promise<T[]> {
    const results: T[] = [];

    for (let i = 0; i < addresses.length; i += batchSize) {
      const batch = addresses.slice(i, i + batchSize);
      const batchPromises = batch.map(async (address) => {
        await this.rateLimit();
        return processor(address);
      });

      const batchResults = await Promise.all(batchPromises);
      const validResults = batchResults.filter(result => result !== null) as T[];
      results.push(...validResults);
    }

    return results;
  }

  async fetchRecentVerifiedContracts(
    chain: ChainConfig,
    page: number = 1,
    offset: number = 1000  // ARCHAEOLOGIST MODE: Increased from 100 to 1000
  ): Promise<ContractInfo[]> {
    logger.info(`🏺 ARCHAEOLOGIST MODE: Hunting for forgotten money on ${chain.name}...`);

    // Check if we have an API key
    if (!chain.etherscanApiKey) {
      logger.error(`No Etherscan API key configured for ${chain.name}. Please set ETHERSCAN_API_KEY environment variable.`);
      throw new Error(`Missing Etherscan API key for ${chain.name}`);
    }

    // ARCHAEOLOGIST STRATEGY: Enhanced discovery with balance checking
    try {
      logger.info(`🔍 Enhanced protocol discovery for ${chain.name}...`);
      const protocolContracts = await Promise.race([
        this.fetchProtocolContracts(chain, Math.min(offset, 200)), // Increased from 20 to 200
        new Promise<ContractInfo[]>((_, reject) =>
          setTimeout(() => reject(new Error('Protocol fetch timeout')), 20000) // Increased timeout
        )
      ]);

      if (protocolContracts.length > 0) {
        // ARCHAEOLOGIST ENHANCEMENT: Check balances and prioritize high-value contracts
        const contractsWithValue = await this.prioritizeContractsByValue(chain, protocolContracts);
        logger.info(`💰 Found ${contractsWithValue.length} contracts (${contractsWithValue.filter(c => c.name.includes('💰')).length} with value) from ${chain.name}`);
        return contractsWithValue;
      }
    } catch (error) {
      logger.debug(`Enhanced protocol discovery failed for ${chain.name}:`, error instanceof Error ? error.message : 'Unknown error');
    }

    // Fallback: Try unverified contracts (ARCHAEOLOGIST BONUS)
    try {
      logger.info(`🔓 Scanning unverified contracts for hidden value on ${chain.name}...`);
      const unverifiedContracts = await this.fetchUnverifiedContracts(chain, 50);
      if (unverifiedContracts.length > 0) {
        logger.info(`🎯 Found ${unverifiedContracts.length} unverified contracts on ${chain.name}`);
        return unverifiedContracts;
      }
    } catch (error) {
      logger.debug(`Unverified contract scan failed for ${chain.name}:`, error instanceof Error ? error.message : 'Unknown error');
    }

    logger.info(`No contracts found via archaeologist methods for ${chain.name}`);
    return [];
  }

  private async getRecentContractAddresses(
    chain: ChainConfig, 
    page: number, 
    offset: number
  ): Promise<string> {
    await this.rateLimit();
    
    try {
      // Get recent verified contracts
      const response = await axios.get(chain.etherscanApiUrl, {
        params: {
          module: 'contract',
          action: 'listcontracts',
          page: page,
          offset: offset,
          apikey: chain.etherscanApiKey
        },
        timeout: 10000
      });

      if (response.data.status !== '1') {
        logger.warn(`Failed to get recent contracts for ${chain.name}:`, response.data.message);
        return '';
      }

      const contracts = response.data.result || [];
      return contracts.map((c: any) => c.ContractAddress).join(',');
    } catch (error) {
      logger.error(`Failed to get recent contract addresses from ${chain.name}:`, error);
      return '';
    }
  }

  async fetchContractDetails(chain: ChainConfig, address: string): Promise<ContractInfo | null> {
    await this.rateLimit();
    
    try {
      const response = await axios.get(chain.etherscanApiUrl, {
        params: {
          module: 'contract',
          action: 'getsourcecode',
          address: address,
          apikey: chain.etherscanApiKey
        },
        timeout: 10000
      });

      if (response.data.status !== '1') {
        logger.debug(`Failed to get source code for ${address}:`, response.data.message);
        return null;
      }

      const result = response.data.result[0];
      
      // Skip if not verified
      if (!result.ABI || result.ABI === 'Contract source code not verified') {
        logger.debug(`Contract ${address} not verified - skipping`);
        return null;
      }

      // Parse ABI
      let abi: any[];
      try {
        abi = JSON.parse(result.ABI);
      } catch (error) {
        logger.debug(`Failed to parse ABI for ${address}:`, error);
        return null;
      }

      // Get creation transaction details
      const creationInfo = await this.getContractCreationInfo(chain, address);

      return {
        address: address,
        name: result.ContractName || 'Unknown',
        abi: abi,
        sourceCode: result.SourceCode || '',
        compiler: result.CompilerVersion || '',
        txHash: creationInfo?.txHash || '',
        blockNumber: creationInfo?.blockNumber || 0,
        timestamp: creationInfo?.timestamp || 0,
        chainId: chain.chainId
      };
    } catch (error) {
      logger.debug(`Failed to fetch contract details for ${address}:`, error);
      return null;
    }
  }

  private async getContractCreationInfo(
    chain: ChainConfig, 
    address: string
  ): Promise<{ txHash: string; blockNumber: number; timestamp: number } | null> {
    await this.rateLimit();
    
    try {
      const response = await axios.get(chain.etherscanApiUrl, {
        params: {
          module: 'contract',
          action: 'getcontractcreation',
          contractaddresses: address,
          apikey: chain.etherscanApiKey
        },
        timeout: 10000
      });

      if (response.data.status !== '1') {
        return null;
      }

      const result = response.data.result[0];
      
      // Get transaction details
      const txResponse = await axios.get(chain.etherscanApiUrl, {
        params: {
          module: 'proxy',
          action: 'eth_getTransactionByHash',
          txhash: result.txHash,
          apikey: chain.etherscanApiKey
        },
        timeout: 10000
      });

      if (txResponse.data.result) {
        const tx = txResponse.data.result;
        return {
          txHash: result.txHash,
          blockNumber: parseInt(tx.blockNumber, 16),
          timestamp: Date.now() // We'll get the actual timestamp from the block if needed
        };
      }

      return {
        txHash: result.txHash,
        blockNumber: 0,
        timestamp: Date.now()
      };
    } catch (error) {
      logger.debug(`Failed to get creation info for ${address}:`, error);
      return null;
    }
  }

  async fetchContractsByBlockRange(
    chain: ChainConfig, 
    fromBlock: number, 
    toBlock: number
  ): Promise<ContractInfo[]> {
    const contracts: ContractInfo[] = [];
    
    try {
      // Get contract creation events in block range
      await this.rateLimit();
      
      const response = await axios.get(chain.etherscanApiUrl, {
        params: {
          module: 'logs',
          action: 'getLogs',
          fromBlock: fromBlock,
          toBlock: toBlock,
          topic0: '0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0', // OwnershipTransferred (common in contracts)
          apikey: chain.etherscanApiKey
        },
        timeout: 15000
      });

      if (response.data.status !== '1') {
        logger.debug(`No logs found for block range ${fromBlock}-${toBlock} on ${chain.name}`);
        return contracts;
      }

      const logs = response.data.result || [];
      const uniqueAddresses = new Set<string>();

      // Extract unique contract addresses from logs
      for (const log of logs) {
        if (log.address) {
          uniqueAddresses.add(log.address);
        }
      }

      // Fetch details for each unique contract
      for (const address of uniqueAddresses) {
        try {
          const contractInfo = await this.fetchContractDetails(chain, address);
          if (contractInfo) {
            contracts.push(contractInfo);
          }
        } catch (error) {
          logger.debug(`Failed to fetch details for contract ${address}:`, error);
        }
      }

      logger.info(`Fetched ${contracts.length} contracts from blocks ${fromBlock}-${toBlock} on ${chain.name}`);
      return contracts;
    } catch (error) {
      logger.error(`Failed to fetch contracts by block range on ${chain.name}:`, error);
      return contracts;
    }
  }

  async fetchTopContracts(chain: ChainConfig, limit: number = 100): Promise<ContractInfo[]> {
    await this.rateLimit();
    
    try {
      // Get top contracts by transaction count
      const response = await axios.get(chain.etherscanApiUrl, {
        params: {
          module: 'stats',
          action: 'topaccounts',
          apikey: chain.etherscanApiKey
        },
        timeout: 10000
      });

      if (response.data.status !== '1') {
        logger.warn(`Failed to get top contracts for ${chain.name}:`, response.data.message);
        return [];
      }

      const accounts = response.data.result || [];
      const contracts: ContractInfo[] = [];

      for (const account of accounts.slice(0, limit)) {
        try {
          const contractInfo = await this.fetchContractDetails(chain, account.address);
          if (contractInfo) {
            contracts.push(contractInfo);
          }
        } catch (error) {
          logger.debug(`Failed to fetch details for top contract ${account.address}:`, error);
        }
      }

      logger.info(`Fetched ${contracts.length} top contracts from ${chain.name}`);
      return contracts;
    } catch (error) {
      logger.error(`Failed to fetch top contracts from ${chain.name}:`, error);
      return [];
    }
  }


  // Method to fetch contracts from protocol deployments - ARCHAEOLOGIST MODE
  private async fetchProtocolContracts(chain: ChainConfig, limit: number): Promise<ContractInfo[]> {
    logger.info(`🏺 ARCHAEOLOGIST MODE: Scanning ${chain.name} for forgotten money...`);

    const contracts: ContractInfo[] = [];

    try {
      // Expanded protocol-specific event topics for better coverage
      const protocolTopics = [
        '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', // Transfer
        '0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', // Approval
        '0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', // Deposit
        '0x7fcf532c15f0a6db0bd6d0e038bea71d30d808c7d98cb3bf7268a95bf5081b65', // Withdrawal
        '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb', // Claim
        '0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0', // OwnershipTransferred
        '0x17307eab39ab6107e8899845ad3d59bd9653f200f220920489ca2b5937696c31', // ApprovalForAll
        '0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d', // RoleGranted
        '0xf6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b', // RoleRevoked
        '0x4c209b5fc8ad50758f13e2e1088ba56a560dff690a1c6fef26394f4c03821c4f', // Mint
        '0xcc16f5dbb4873280815c1ee09dbd06736cffcc184412cf7a71a0fdb75d397ca5', // Burn
        '0x90890809c654f11d6e72a28fa60149770a0d11ec6c92319d6ceb2bb0a4ea1a15', // Stake
        '0x85082129d87b2fe11527cb1b3b7a520aeb5aa6913f96cd83f8456a74b4f46f01'  // Unstake
      ];

      // Get latest block for recent range using V2 API
      const latestBlockResponse = await axios.get(chain.etherscanApiUrl, {
        params: {
          chainid: chain.chainId,
          module: 'proxy',
          action: 'eth_blockNumber',
          apikey: chain.etherscanApiKey
        },
        timeout: 10000
      });

      // Handle different response formats from different chains
      if (latestBlockResponse.data.status !== '1' && !latestBlockResponse.data.result) {
        logger.warn(`Failed to get latest block for ${chain.name}: ${latestBlockResponse.data.message || latestBlockResponse.data.status || 'API Error'}`);
        return contracts;
      }

      // Handle different response formats
      const blockResult = latestBlockResponse.data.result;
      const latestBlock = typeof blockResult === 'string' ? parseInt(blockResult, 16) : blockResult;
      const fromBlock = latestBlock - 10000; // Look back 10,000 blocks for more coverage

      const uniqueAddresses = new Set<string>();

      // Process topics in parallel for speed but get more results
      const topicPromises = protocolTopics.map(async (topic) => {
        try {
          await this.rateLimit();

          const response = await axios.get(chain.etherscanApiUrl, {
            params: {
              chainid: chain.chainId,
              module: 'logs',
              action: 'getLogs',
              fromBlock: fromBlock,
              toBlock: latestBlock,
              topic0: topic,
              apikey: chain.etherscanApiKey
            },
            timeout: 12000 // Increased timeout for more data
          });

          if (response.data.status === '1') {
            const logs = response.data.result || [];
            // Get up to 500 results per topic instead of 20 - ARCHAEOLOGIST SCALE
            return logs.slice(0, 500).map((log: any) => log.address).filter(Boolean);
          }
          return [];
        } catch (error) {
          logger.debug(`Failed to fetch logs for topic ${topic}:`, error);
          return [];
        }
      });

      const allResults = await Promise.all(topicPromises);
      allResults.flat().forEach(address => uniqueAddresses.add(address));

      logger.info(`Found ${uniqueAddresses.size} unique contract addresses from events on ${chain.name}`);

      // Fetch details for unique addresses (limit to first 10 for better chance of finding verified contracts)
      const addressArray = Array.from(uniqueAddresses).slice(0, Math.min(limit, 10));

      // Process in parallel for speed
      const contractPromises = addressArray.map(async (address) => {
        try {
          return await this.fetchContractDetails(chain, address);
        } catch (error) {
          logger.debug(`Failed to fetch protocol contract ${address}:`, error);
          return null;
        }
      });

      const contractResults = await Promise.all(contractPromises);
      contracts.push(...contractResults.filter((c): c is ContractInfo => c !== null));
      
      logger.info(`Fetched ${contracts.length} protocol contracts from ${chain.name}`);
      return contracts;
    } catch (error) {
      logger.error(`Failed to fetch protocol contracts from ${chain.name}:`, error);
      return contracts;
    }
  }
  
  // Method to fetch contracts from specific block ranges
  private async fetchContractsFromBlockRanges(chain: ChainConfig, limit: number): Promise<ContractInfo[]> {
    logger.info(`Fetching contracts from block ranges on ${chain.name}...`);
    
    const contracts: ContractInfo[] = [];
    
    try {
      // Get latest block number using V2 API
      const latestBlockResponse = await axios.get(chain.etherscanApiUrl, {
        params: {
          chainid: chain.chainId,
          module: 'proxy',
          action: 'eth_blockNumber',
          apikey: chain.etherscanApiKey
        },
        timeout: 10000
      });
      
      if (latestBlockResponse.data.status !== '1' && !latestBlockResponse.data.result) {
        logger.warn(`Failed to get latest block for ${chain.name}: ${latestBlockResponse.data.message || 'API Error'}`);
        return contracts;
      }

      const blockResult = latestBlockResponse.data.result;
      const latestBlock = typeof blockResult === 'string' ? parseInt(blockResult, 16) : blockResult;
      
      // Define multiple block ranges to scan
      const blockRanges = [
        { from: latestBlock - 1000, to: latestBlock },       // Very recent
        { from: latestBlock - 5000, to: latestBlock - 1000 }, // Recent
        { from: latestBlock - 15000, to: latestBlock - 5000 }, // Older
      ];
      
      let totalFetched = 0;
      
      for (const range of blockRanges) {
        if (totalFetched >= limit) break;
        
        try {
          const rangeContracts = await this.fetchContractsByBlockRange(chain, range.from, range.to);
          const remainingLimit = limit - totalFetched;
          const contractsToAdd = rangeContracts.slice(0, remainingLimit);
          
          contracts.push(...contractsToAdd);
          totalFetched += contractsToAdd.length;
          
          logger.info(`Fetched ${contractsToAdd.length} contracts from range ${range.from}-${range.to}`);
        } catch (error) {
          logger.debug(`Failed to fetch contracts from range ${range.from}-${range.to}:`, error);
        }
      }
      
      logger.info(`Total fetched ${contracts.length} contracts from block ranges on ${chain.name}`);
      return contracts;
    } catch (error) {
      logger.error(`Failed to fetch contracts from block ranges on ${chain.name}:`, error);
      return contracts;
    }
  }

  // Fallback method to get contracts from recent blocks
  private async fetchContractsFromRecentBlocks(chain: ChainConfig, limit: number): Promise<ContractInfo[]> {
    logger.info(`Trying fallback method for ${chain.name} - getting contracts from recent blocks`);
    
    try {
      // Get latest block number using V2 API
      const latestBlockResponse = await axios.get(chain.etherscanApiUrl, {
        params: {
          chainid: chain.chainId,
          module: 'proxy',
          action: 'eth_blockNumber',
          apikey: chain.etherscanApiKey
        },
        timeout: 10000
      });

      if (latestBlockResponse.data.status !== '1' && !latestBlockResponse.data.result) {
        logger.warn(`Failed to get latest block for ${chain.name}: ${latestBlockResponse.data.message || 'API Error'}`);
        return [];
      }

      const blockResult = latestBlockResponse.data.result;
      const latestBlock = typeof blockResult === 'string' ? parseInt(blockResult, 16) : blockResult;
      const fromBlock = latestBlock - 10000; // Look back 10,000 blocks
      
      logger.info(`Scanning blocks ${fromBlock} to ${latestBlock} on ${chain.name}`);
      
      return await this.fetchContractsByBlockRange(chain, fromBlock, latestBlock);
    } catch (error) {
      logger.error(`Fallback method failed for ${chain.name}:`, error);
      return [];
    }
  }

  // New method to fetch and decompile unverified contracts
  async fetchUnverifiedContracts(
    chain: ChainConfig,
    limit: number = 5
  ): Promise<ContractInfo[]> {
    logger.info(`Fetching unverified contracts from ${chain.name}...`);

    // Skip if no API key available
    if (!chain.etherscanApiKey) {
      logger.debug(`No Etherscan API key for ${chain.name}, skipping unverified contract fetching`);
      return [];
    }

    try {
      // Reuse the active contract addresses we already found from protocol events
      const activeAddresses = await this.getActiveContractAddressesFromEvents(chain, limit * 2);

      if (activeAddresses.length === 0) {
        logger.debug(`No active contracts found on ${chain.name}`);
        return [];
      }

      // Filter out verified contracts (keep only unverified ones)
      const unverifiedAddresses = await this.filterUnverifiedContracts(chain, activeAddresses);

      if (unverifiedAddresses.length === 0) {
        logger.debug(`No unverified contracts found on ${chain.name}`);
        return [];
      }

      logger.info(`Found ${unverifiedAddresses.length} unverified contracts on ${chain.name}`);

      // Take only the first few for decompilation (expensive operation)
      const addressesToDecompile = unverifiedAddresses.slice(0, limit);

      // Decompile contracts to extract function signatures
      const decompilationResults = await bytecodeDecompiler.batchDecompile(addressesToDecompile, chain.chainId);

      // Filter for contracts with target functions
      const contractsWithTargetFunctions = decompilationResults.filter(result => result.hasTargetFunctions);

      // Convert decompilation results to ContractInfo
      const contracts: ContractInfo[] = [];
      for (const result of contractsWithTargetFunctions) {
        const contractInfo = await this.createContractInfoFromDecompilation(result, chain);
        if (contractInfo) {
          contracts.push(contractInfo);
        }
      }

      logger.info(`Found ${contracts.length} unverified contracts with target functions on ${chain.name}`);
      return contracts;
    } catch (error) {
      logger.error(`Failed to fetch unverified contracts from ${chain.name}:`, error);
      return [];
    }
  }

  // Helper method to get active contract addresses from recent events
  private async getActiveContractAddressesFromEvents(chain: ChainConfig, limit: number): Promise<string[]> {
    try {
      // Use the same logic as protocol contracts but just return addresses
      const protocolTopics = [
        '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', // Transfer (most common)
        '0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0'  // OwnershipTransferred
      ];

      // Get latest block for recent range
      await this.rateLimit();
      const latestBlockResponse = await axios.get(chain.etherscanApiUrl, {
        params: {
          chainid: chain.chainId,
          module: 'proxy',
          action: 'eth_blockNumber',
          apikey: chain.etherscanApiKey
        },
        timeout: 5000
      });

      if (latestBlockResponse.data.status !== '1' && !latestBlockResponse.data.result) {
        return [];
      }

      const blockResult = latestBlockResponse.data.result;
      const latestBlock = typeof blockResult === 'string' ? parseInt(blockResult, 16) : blockResult;
      const fromBlock = latestBlock - 1000; // Look back 1,000 blocks

      const uniqueAddresses = new Set<string>();

      // Process topics in parallel for speed
      const topicPromises = protocolTopics.map(async (topic) => {
        try {
          await this.rateLimit();

          const response = await axios.get(chain.etherscanApiUrl, {
            params: {
              chainid: chain.chainId,
              module: 'logs',
              action: 'getLogs',
              fromBlock: fromBlock,
              toBlock: latestBlock,
              topic0: topic,
              apikey: chain.etherscanApiKey
            },
            timeout: 8000
          });

          if (response.data.status === '1') {
            const logs = response.data.result || [];
            return logs.slice(0, 50).map((log: any) => log.address).filter(Boolean);
          }
          return [];
        } catch (error) {
          logger.debug(`Failed to fetch logs for topic ${topic}:`, error);
          return [];
        }
      });

      const allResults = await Promise.all(topicPromises);
      allResults.flat().forEach(address => uniqueAddresses.add(address));

      return Array.from(uniqueAddresses).slice(0, limit);
    } catch (error) {
      logger.debug(`Failed to get active contract addresses from ${chain.name}:`, error);
      return [];
    }
  }

  private async getRecentContractCreations(chain: ChainConfig, limit: number): Promise<string[]> {
    const addresses: string[] = [];

    try {
      // Get recent blocks and scan for contract creations using V2 API
      await this.rateLimit();
      const latestBlockResponse = await axios.get(chain.etherscanApiUrl, {
        params: {
          chainid: chain.chainId,
          module: 'proxy',
          action: 'eth_blockNumber',
          apikey: chain.etherscanApiKey
        }
      });

      const blockResult = latestBlockResponse.data.result;
      const latestBlock = typeof blockResult === 'string' ? parseInt(blockResult, 16) : blockResult;
      const fromBlock = latestBlock - 1000; // Look back 1000 blocks for recent creations

      // Get transactions from recent blocks
      for (let block = latestBlock; block > fromBlock && addresses.length < limit; block--) {
        await this.rateLimit();

        const blockResponse = await axios.get(chain.etherscanApiUrl, {
          params: {
            module: 'proxy',
            action: 'eth_getBlockByNumber',
            tag: `0x${block.toString(16)}`,
            boolean: true,
            apikey: chain.etherscanApiKey
          }
        });

        const blockData = blockResponse.data.result;
        if (blockData && blockData.transactions) {
          for (const tx of blockData.transactions) {
            // Contract creation transactions have null 'to' field
            if (!tx.to && tx.creates) {
              addresses.push(tx.creates);
              if (addresses.length >= limit) break;
            }
          }
        }
      }
    } catch (error) {
      logger.error(`Failed to get recent contract creations:`, error);
    }

    return addresses;
  }

  private async filterUnverifiedContracts(chain: ChainConfig, addresses: string[]): Promise<string[]> {
    const unverified: string[] = [];

    for (const address of addresses) {
      try {
        await this.rateLimit();

        const response = await axios.get(chain.etherscanApiUrl, {
          params: {
            module: 'contract',
            action: 'getsourcecode',
            address: address,
            apikey: chain.etherscanApiKey
          }
        });

        const result = response.data.result[0];

        // If source code is empty, contract is unverified
        if (!result.SourceCode || result.SourceCode === '') {
          unverified.push(address);
        }
      } catch (error) {
        logger.debug(`Failed to check verification status for ${address}:`, error);
        // Assume unverified if we can't check
        unverified.push(address);
      }
    }

    return unverified;
  }

  private async createContractInfoFromDecompilation(
    result: DecompilationResult,
    chain: ChainConfig
  ): Promise<ContractInfo | null> {
    try {
      // Create a synthetic ABI from decompiled functions
      const abi = result.functions.map(func => ({
        type: 'function',
        name: func.name || 'unknown',
        inputs: (func.inputs || []).map((type, index) => ({
          name: `param${index}`,
          type: type
        })),
        outputs: [],
        stateMutability: 'nonpayable'
      }));

      const contractInfo: ContractInfo = {
        address: result.address,
        name: `Unverified_${result.address.slice(0, 8)}`,
        abi: abi,
        sourceCode: `// Decompiled contract - ${result.functions.length} functions found`,
        compiler: 'decompiled',
        txHash: '0x0', // Unknown for unverified contracts
        blockNumber: 0, // Unknown for unverified contracts
        timestamp: Date.now(),
        chainId: result.chainId
      };

      return contractInfo;
    } catch (error) {
      logger.error(`Failed to create ContractInfo from decompilation:`, error);
      return null;
    }
  }

  // ARCHAEOLOGIST ENHANCEMENT: Check if contract has ETH or token value
  private async checkContractValue(chain: ChainConfig, address: string): Promise<boolean> {
    try {
      await this.rateLimit();

      // Check ETH balance
      const response = await axios.get(chain.etherscanApiUrl, {
        params: {
          module: 'account',
          action: 'balance',
          address: address,
          tag: 'latest',
          apikey: chain.etherscanApiKey
        },
        timeout: 5000
      });

      if (response.data.status === '1') {
        const balance = BigInt(response.data.result || '0');
        // Consider contracts with > 0.001 ETH as valuable
        const threshold = BigInt('****************'); // 0.001 ETH in wei
        return balance > threshold;
      }

      return false;
    } catch (error) {
      logger.debug(`Failed to check balance for ${address}:`, error);
      return false; // Assume no value if we can't check
    }
  }

  // ARCHAEOLOGIST ENHANCEMENT: Prioritize contracts by value
  private async prioritizeContractsByValue(chain: ChainConfig, contracts: ContractInfo[]): Promise<ContractInfo[]> {
    const contractsWithValue: ContractInfo[] = [];

    // Check balances in batches to avoid rate limits
    const batchSize = 5;
    for (let i = 0; i < contracts.length; i += batchSize) {
      const batch = contracts.slice(i, i + batchSize);

      const batchPromises = batch.map(async (contract) => {
        const hasValue = await this.checkContractValue(chain, contract.address);
        if (hasValue) {
          // Mark high-value contracts
          contract.name = `💰 ${contract.name}`;
          logger.info(`💰 High-value contract found: ${contract.address}`);
        }
        return contract;
      });

      const batchResults = await Promise.all(batchPromises);
      contractsWithValue.push(...batchResults);

      // Small delay between batches
      if (i + batchSize < contracts.length) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    // Sort by value (high-value contracts first)
    return contractsWithValue.sort((a, b) => {
      const aHasValue = a.name.includes('💰');
      const bHasValue = b.name.includes('💰');
      if (aHasValue && !bHasValue) return -1;
      if (!aHasValue && bHasValue) return 1;
      return 0;
    });
  }


}

export const contractFetcher = new ContractFetcher();
