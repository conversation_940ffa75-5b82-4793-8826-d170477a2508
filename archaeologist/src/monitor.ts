import { ethers } from 'ethers';
import { ChainConfig } from './types';
import { CONFIG } from './config';
import { contractFetcher } from './fetcher';
import { contractFilter } from './filter';
import { contractSimulator } from './simulator';
import { evaluator } from './evaluator';
import { executor } from './executor';
import { db } from './database';
import { logger } from './logger';
import ora from 'ora';

export class BlockMonitor {
  private providers: Map<number, ethers.JsonRpcProvider> = new Map();
  private listeners: Map<number, () => void> = new Map();
  private isMonitoring: boolean = false;

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders(): void {
    for (const chain of CONFIG.chains) {
      if (chain.rpcUrl) {
        try {
          const provider = new ethers.JsonRpcProvider(chain.rpcUrl);
          this.providers.set(chain.chainId, provider);
          logger.debug(`Block monitor initialized for ${chain.name} (${chain.chainId})`);
        } catch (error) {
          logger.error(`Failed to initialize block monitor for ${chain.name}:`, error);
        }
      }
    }
  }

  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      logger.warn('Block monitoring is already running');
      return;
    }

    this.isMonitoring = true;
    logger.info('Starting real-time block monitoring...');

    for (const chain of CONFIG.chains) {
      if (chain.rpcUrl && chain.etherscanApiKey) {
        try {
          await this.startChainMonitoring(chain);
        } catch (error) {
          logger.error(`Failed to start monitoring for ${chain.name}:`, error);
        }
      }
    }
  }

  private async startChainMonitoring(chain: ChainConfig): Promise<void> {
    const provider = this.providers.get(chain.chainId);
    if (!provider) {
      logger.error(`No provider configured for ${chain.name}`);
      return;
    }

    logger.info(`Starting block monitoring for ${chain.name}...`);

    // Get the last processed block
    const scanStatus = await db.getScanStatus(chain.chainId);
    let lastProcessedBlock = scanStatus?.lastProcessedBlock || 0;

    // If no previous scan, start from current block minus some buffer
    if (lastProcessedBlock === 0) {
      try {
        const currentBlock = await provider.getBlockNumber();
        lastProcessedBlock = currentBlock - 10; // Start 10 blocks back
      } catch (error) {
        logger.error(`Failed to get current block for ${chain.name}:`, error);
        return;
      }
    }

    // Set up block listener
    const blockListener = async (blockNumber: number) => {
      try {
        await this.processNewBlock(chain, blockNumber, lastProcessedBlock);
        lastProcessedBlock = blockNumber;
      } catch (error) {
        logger.error(`Error processing block ${blockNumber} on ${chain.name}:`, error);
      }
    };

    provider.on('block', blockListener);
    this.listeners.set(chain.chainId, () => provider.off('block', blockListener));

    logger.info(`Block monitoring started for ${chain.name} from block ${lastProcessedBlock}`);
  }

  private async processNewBlock(
    chain: ChainConfig,
    blockNumber: number,
    lastProcessedBlock: number
  ): Promise<void> {
    if (blockNumber <= lastProcessedBlock) {
      return; // Already processed
    }

    logger.debug(`Processing new block ${blockNumber} on ${chain.name}`);

    try {
      // Get contracts created in this block
      const contracts = await contractFetcher.fetchContractsByBlockRange(
        chain,
        blockNumber,
        blockNumber
      );

      if (contracts.length === 0) {
        // Update scan status even if no contracts found
        await db.updateScanStatus({
          chainId: chain.chainId,
          lastProcessedBlock: blockNumber,
          lastScanTime: new Date(),
          contractsScanned: 0,
          exploitsFound: 0,
          totalValue: '0'
        });
        return;
      }

      logger.info(`Found ${contracts.length} new contracts in block ${blockNumber} on ${chain.name}`);

      // Save contracts to database
      for (const contract of contracts) {
        await db.saveContract(contract);
      }

      // Filter contracts with target functions
      const filteredResults = contractFilter.filterContracts(contracts);

      if (filteredResults.length === 0) {
        await db.updateScanStatus({
          chainId: chain.chainId,
          lastProcessedBlock: blockNumber,
          lastScanTime: new Date(),
          contractsScanned: contracts.length,
          exploitsFound: 0,
          totalValue: '0'
        });
        return;
      }

      logger.info(`Found ${filteredResults.length} contracts with target functions in block ${blockNumber}`);

      // Simulate target functions
      const simulationResults = await contractSimulator.batchSimulate(filteredResults);

      // Evaluate simulation results
      const exploitableContracts = evaluator.evaluateSimulations(simulationResults);

      if (exploitableContracts.length > 0) {
        logger.info(`🎯 Found ${exploitableContracts.length} exploitable contracts in block ${blockNumber}!`);

        // Execute exploits immediately for time-sensitive opportunities
        const executionResults = await executor.batchExecuteExploits(exploitableContracts);
        const successfulExecutions = executionResults.filter(r => r.success);

        if (successfulExecutions.length > 0) {
          logger.info(`✅ Successfully executed ${successfulExecutions.length} exploits from block ${blockNumber}`);
        }
      }

      // Update scan status
      await db.updateScanStatus({
        chainId: chain.chainId,
        lastProcessedBlock: blockNumber,
        lastScanTime: new Date(),
        contractsScanned: contracts.length,
        exploitsFound: exploitableContracts.length,
        totalValue: exploitableContracts.reduce((sum, e) => sum + parseFloat(e.estimatedValue || '0'), 0).toString()
      });

    } catch (error) {
      logger.error(`Error processing block ${blockNumber} on ${chain.name}:`, error);
    }
  }

  async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      logger.warn('Block monitoring is not running');
      return;
    }

    logger.info('Stopping block monitoring...');

    // Remove all listeners
    for (const [chainId, removeListener] of this.listeners) {
      try {
        removeListener();
        logger.debug(`Stopped monitoring for chain ${chainId}`);
      } catch (error) {
        logger.error(`Error stopping monitoring for chain ${chainId}:`, error);
      }
    }

    this.listeners.clear();
    this.isMonitoring = false;
    logger.info('Block monitoring stopped');
  }

  async catchUpMissedBlocks(): Promise<void> {
    if (this.isMonitoring) {
      logger.warn('Cannot catch up while monitoring is active');
      return;
    }

    logger.info('Catching up missed blocks...');

    for (const chain of CONFIG.chains) {
      if (chain.rpcUrl && chain.etherscanApiKey) {
        await this.catchUpChainBlocks(chain);
      }
    }
  }

  private async catchUpChainBlocks(chain: ChainConfig): Promise<void> {
    const provider = this.providers.get(chain.chainId);
    if (!provider) {
      logger.error(`No provider configured for ${chain.name}`);
      return;
    }

    const spinner = ora(`Catching up blocks for ${chain.name}...`).start();

    try {
      const scanStatus = await db.getScanStatus(chain.chainId);
      const currentBlock = await provider.getBlockNumber();
      const lastProcessedBlock = scanStatus?.lastProcessedBlock || currentBlock - 1000;

      if (currentBlock <= lastProcessedBlock) {
        spinner.info(`${chain.name} is up to date`);
        return;
      }

      const blockGap = currentBlock - lastProcessedBlock;
      spinner.text = `Catching up ${blockGap} blocks for ${chain.name}...`;

      // Process in chunks to avoid overwhelming the system
      const chunkSize = 100;
      for (let start = lastProcessedBlock + 1; start <= currentBlock; start += chunkSize) {
        const end = Math.min(start + chunkSize - 1, currentBlock);
        
        spinner.text = `Processing blocks ${start}-${end} on ${chain.name}...`;
        
        const contracts = await contractFetcher.fetchContractsByBlockRange(chain, start, end);
        
        if (contracts.length > 0) {
          // Save contracts
          for (const contract of contracts) {
            await db.saveContract(contract);
          }

          // Process as usual
          const filteredResults = contractFilter.filterContracts(contracts);
          
          if (filteredResults.length > 0) {
            const simulationResults = await contractSimulator.batchSimulate(filteredResults);
            const exploitableContracts = evaluator.evaluateSimulations(simulationResults);
            
            if (exploitableContracts.length > 0) {
              spinner.info(`Found ${exploitableContracts.length} exploitable contracts in blocks ${start}-${end}`);
              await executor.batchExecuteExploits(exploitableContracts);
            }
          }
        }

        // Update progress
        await db.updateScanStatus({
          chainId: chain.chainId,
          lastProcessedBlock: end,
          lastScanTime: new Date(),
          contractsScanned: contracts.length,
          exploitsFound: 0,
          totalValue: '0'
        });
      }

      spinner.succeed(`Caught up ${blockGap} blocks for ${chain.name}`);
    } catch (error) {
      spinner.fail(`Failed to catch up blocks for ${chain.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      logger.error(`Catch up error for ${chain.name}:`, error);
    }
  }

  isRunning(): boolean {
    return this.isMonitoring;
  }

  getMonitoringStatus(): { [chainId: number]: boolean } {
    const status: { [chainId: number]: boolean } = {};
    for (const chain of CONFIG.chains) {
      status[chain.chainId] = this.listeners.has(chain.chainId);
    }
    return status;
  }
}

export const blockMonitor = new BlockMonitor();
