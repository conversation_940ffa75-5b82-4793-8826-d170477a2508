import { ChainConfig, TargetFunction, Config } from './types';
import dotenv from 'dotenv';

dotenv.config();

export const TARGET_FUNCTIONS: TargetFunction[] = [
  {
    name: 'claim',
    signature: 'claim()',
    fourByteSignature: '0x4e71d92d',
    description: 'Airdrops, loyalty drops',
    priority: 1
  },
  {
    name: 'withdraw',
    signature: 'withdraw()',
    fourByteSignature: '0x2e1a7d4d',
    description: 'Legacy vaults',
    priority: 1
  },
  {
    name: 'exit',
    signature: 'exit()',
    fourByteSignature: '0x3f4ba83a',
    description: 'DAOs, LP exits',
    priority: 1
  },
  {
    name: 'rageQuit',
    signature: 'rageQuit(uint256)',
    fourByteSignature: '0x7c7f4f8e',
    description: 'DAO exits',
    priority: 2
  },
  {
    name: 'collect',
    signature: 'collect()',
    fourByteSignature: '0x9a1fc3a7',
    description: 'DEX fees',
    priority: 2
  },
  {
    name: 'finalize',
    signature: 'finalize()',
    fourByteSignature: '0x4bb278f3',
    description: 'Sometimes unlocks value',
    priority: 2
  },
  {
    name: 'redeem',
    signature: 'redeem()',
    fourByteSignature: '0xbe040fb0',
    description: 'Tokenized positions',
    priority: 2
  },
  {
    name: 'close',
    signature: 'close()',
    fourByteSignature: '0x43d726d6',
    description: 'Closing out positions/contracts',
    priority: 3
  },
  {
    name: 'selfDestruct',
    signature: 'selfDestruct()',
    fourByteSignature: '0x9cb8a26a',
    description: 'Legacy contracts with payout',
    priority: 3
  },
  // Additional function variants
  {
    name: 'withdraw',
    signature: 'withdraw(uint256)',
    fourByteSignature: '0x2e1a7d4d',
    description: 'Legacy vaults with amount',
    priority: 1
  },
  {
    name: 'claimTokens',
    signature: 'claimTokens()',
    fourByteSignature: '0xdf8de3e7',
    description: 'Token claiming',
    priority: 1
  },
  {
    name: 'emergencyWithdraw',
    signature: 'emergencyWithdraw()',
    fourByteSignature: '0x5312ea8e',
    description: 'Emergency withdrawals',
    priority: 1
  }
];

export const CHAIN_CONFIGS: ChainConfig[] = [
  {
    chainId: 1,
    name: 'Ethereum',
    rpcUrl: process.env.ETHEREUM_RPC_URL || '',
    etherscanApiUrl: 'https://api.etherscan.io/v2/api',
    etherscanApiKey: process.env.ETHERSCAN_API_KEY || ''
  },
  {
    chainId: 42161,
    name: 'Arbitrum',
    rpcUrl: process.env.ARBITRUM_RPC_URL || '',
    etherscanApiUrl: 'https://api.etherscan.io/v2/api',
    etherscanApiKey: process.env.ETHERSCAN_API_KEY || ''
  },
  {
    chainId: 8453,
    name: 'Base',
    rpcUrl: process.env.BASE_RPC_URL || '',
    etherscanApiUrl: 'https://api.etherscan.io/v2/api',
    etherscanApiKey: process.env.ETHERSCAN_API_KEY || ''
  },
  {
    chainId: 10,
    name: 'Optimism',
    rpcUrl: process.env.OPTIMISM_RPC_URL || '',
    etherscanApiUrl: 'https://api.etherscan.io/v2/api',
    etherscanApiKey: process.env.ETHERSCAN_API_KEY || ''
  },
  {
    chainId: 137,
    name: 'Polygon',
    rpcUrl: process.env.POLYGON_RPC_URL || '',
    etherscanApiUrl: 'https://api.etherscan.io/v2/api',
    etherscanApiKey: process.env.ETHERSCAN_API_KEY || ''
  }
];

export const CONFIG: Config = {
  chains: CHAIN_CONFIGS,
  targetFunctions: TARGET_FUNCTIONS,
  scanBatchSize: parseInt(process.env.SCAN_BATCH_SIZE || '100'),
  simulationTimeout: parseInt(process.env.SIMULATION_TIMEOUT || '5000'),
  maxGasPrice: BigInt(process.env.MAX_GAS_PRICE || '20000000000'),
  privateKey: process.env.PRIVATE_KEY || '',
  dbPath: process.env.DB_PATH || './data/archaeologist.db',
  logLevel: process.env.LOG_LEVEL || 'info',
  flashbotsRpcUrl: process.env.FLASHBOTS_RPC_URL
};

export const HIGH_PRIORITY_KEYWORDS = [
  'airdrop',
  'vault',
  'ragequit',
  'merkle',
  'claim',
  'withdraw',
  'exit',
  'dao',
  'yield',
  'farming',
  'staking',
  'lp',
  'liquidity'
];

// Etherscan V2 API Rate Limits (per second):
// Free: 5 calls/second, Standard: 10 calls/second, Advanced: 20 calls/second
// Professional: 30 calls/second, Pro Plus: 30 calls/second
export const ETHERSCAN_RATE_LIMIT = 200; // milliseconds between requests (5 calls/second for free tier)
export const ETHERSCAN_BURST_SIZE = 3; // Allow small bursts for efficiency
