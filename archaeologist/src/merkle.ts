import { ethers } from 'ethers';
import { logger } from './logger';

export interface MerkleProof {
  leaf: string;
  proof: string[];
  root: string;
  index: number;
}

export interface AirdropClaim {
  address: string;
  amount: string;
  proof: string[];
}

export class MerkleProofGenerator {
  
  // Generate common merkle proof patterns for testing
  generateTestProofs(userAddress: string): MerkleProof[] {
    const proofs: MerkleProof[] = [];
    
    // Generate various proof patterns
    const amounts = ['1000000000000000000', '5000000000000000000', '10000000000000000000']; // 1, 5, 10 ETH
    
    for (const amount of amounts) {
      // Create leaf hash (common pattern: keccak256(abi.encodePacked(address, amount)))
      const leaf = ethers.keccak256(
        ethers.solidityPacked(['address', 'uint256'], [userAddress, amount])
      );
      
      // Generate synthetic proof (for testing purposes)
      const proof = this.generateSyntheticProof(leaf);
      
      proofs.push({
        leaf,
        proof,
        root: this.calculateRoot(leaf, proof),
        index: Math.floor(Math.random() * 1000)
      });
    }
    
    return proofs;
  }

  // Generate synthetic merkle proofs for testing
  private generateSyntheticProof(leaf: string): string[] {
    const proof: string[] = [];
    let currentHash = leaf;
    
    // Generate a proof path of depth 3-8 (common for airdrops)
    const depth = 3 + Math.floor(Math.random() * 6);
    
    for (let i = 0; i < depth; i++) {
      // Generate a sibling hash
      const sibling = ethers.keccak256(
        ethers.toUtf8Bytes(`sibling_${i}_${currentHash.slice(2, 10)}`)
      );
      
      proof.push(sibling);
      
      // Calculate next level hash
      currentHash = ethers.keccak256(
        ethers.solidityPacked(['bytes32', 'bytes32'], [currentHash, sibling])
      );
    }
    
    return proof;
  }

  private calculateRoot(leaf: string, proof: string[]): string {
    let currentHash = leaf;
    
    for (const sibling of proof) {
      currentHash = ethers.keccak256(
        ethers.solidityPacked(['bytes32', 'bytes32'], [currentHash, sibling])
      );
    }
    
    return currentHash;
  }

  // Verify a merkle proof
  verifyProof(proof: string[], leaf: string, root: string): boolean {
    const calculatedRoot = this.calculateRoot(leaf, proof);
    return calculatedRoot === root;
  }

  // Generate common airdrop claim parameters
  generateAirdropClaimParams(userAddress: string): any[][] {
    const paramSets: any[][] = [];
    const proofs = this.generateTestProofs(userAddress);
    
    for (const proof of proofs) {
      // Common airdrop claim patterns
      paramSets.push([
        userAddress,
        proof.leaf.slice(2), // Remove 0x prefix
        proof.proof
      ]);
      
      paramSets.push([
        userAddress,
        '1000000000000000000', // 1 ETH
        proof.proof
      ]);
      
      paramSets.push([
        proof.index,
        userAddress,
        '1000000000000000000',
        proof.proof
      ]);
      
      // With merkle root
      paramSets.push([
        userAddress,
        '1000000000000000000',
        proof.proof,
        proof.root
      ]);
    }
    
    return paramSets;
  }

  // Generate parameters for different claim function signatures
  generateClaimParameters(functionSignature: string, userAddress: string): any[][] {
    const paramSets: any[][] = [];
    
    switch (functionSignature) {
      case 'claim()':
        paramSets.push([]);
        break;
        
      case 'claim(address)':
        paramSets.push([userAddress]);
        paramSets.push([ethers.ZeroAddress]);
        break;
        
      case 'claim(uint256,address,uint256,bytes32[])':
        return this.generateAirdropClaimParams(userAddress);
        
      case 'claim(address,uint256,bytes32[])':
        const proofs = this.generateTestProofs(userAddress);
        for (const proof of proofs) {
          paramSets.push([userAddress, '1000000000000000000', proof.proof]);
        }
        break;
        
      case 'claimTokens()':
        paramSets.push([]);
        break;
        
      case 'claimTokens(address)':
        paramSets.push([userAddress]);
        break;
        
      default:
        // Fallback: try common patterns
        paramSets.push([]);
        paramSets.push([userAddress]);
        paramSets.push([userAddress, '1000000000000000000']);
        break;
    }
    
    return paramSets;
  }

  // Generate sophisticated test data for various scenarios
  generateAdvancedTestData(userAddress: string): {
    merkleProofs: MerkleProof[];
    commonAmounts: string[];
    testAddresses: string[];
    timeBasedParams: number[];
  } {
    return {
      merkleProofs: this.generateTestProofs(userAddress),
      commonAmounts: [
        '0', // Zero amount
        '1', // Minimal amount
        '1000000000000000000', // 1 ETH
        '5000000000000000000', // 5 ETH
        '10000000000000000000', // 10 ETH
        '100000000000000000000', // 100 ETH
        ethers.MaxUint256.toString() // Max uint256
      ],
      testAddresses: [
        userAddress,
        ethers.ZeroAddress,
        '******************************************',
        '******************************************',
        '******************************************'
      ],
      timeBasedParams: [
        0, // Genesis
        Math.floor(Date.now() / 1000), // Current timestamp
        Math.floor(Date.now() / 1000) + 86400, // Tomorrow
        Math.floor(Date.now() / 1000) - 86400, // Yesterday
        1640995200, // Jan 1, 2022
        2147483647 // Max int32
      ]
    };
  }

  // Detect if a function likely uses merkle proofs
  detectMerkleProofFunction(functionSignature: string): boolean {
    const merkleIndicators = [
      'bytes32[]', // Proof array
      'proof',
      'merkle',
      'claim',
      'airdrop'
    ];
    
    return merkleIndicators.some(indicator => 
      functionSignature.toLowerCase().includes(indicator)
    );
  }

  // Generate parameters based on function ABI analysis
  generateParametersFromABI(abiFunction: any, userAddress: string): any[][] {
    const paramSets: any[][] = [];
    const inputs = abiFunction.inputs || [];
    
    if (inputs.length === 0) {
      return [[]];
    }
    
    // Check if this looks like a merkle proof function
    const hasMerkleProof = inputs.some((input: any) => 
      input.type === 'bytes32[]' || input.name?.toLowerCase().includes('proof')
    );
    
    if (hasMerkleProof) {
      return this.generateMerkleProofParameters(inputs, userAddress);
    }
    
    // Generate parameters based on input types
    const testData = this.generateAdvancedTestData(userAddress);
    
    // Generate combinations of parameters
    const paramCombinations = this.generateParameterCombinations(inputs, testData);
    
    return paramCombinations.slice(0, 20); // Limit to 20 combinations
  }

  private generateMerkleProofParameters(inputs: any[], userAddress: string): any[][] {
    const paramSets: any[][] = [];
    const proofs = this.generateTestProofs(userAddress);
    
    for (const proof of proofs) {
      const params: any[] = [];
      
      for (const input of inputs) {
        switch (input.type) {
          case 'address':
            params.push(userAddress);
            break;
          case 'uint256':
            params.push('1000000000000000000'); // 1 ETH
            break;
          case 'bytes32[]':
            params.push(proof.proof);
            break;
          case 'bytes32':
            params.push(proof.root);
            break;
          default:
            params.push(this.getDefaultValueForType(input.type));
        }
      }
      
      paramSets.push(params);
    }
    
    return paramSets;
  }

  private generateParameterCombinations(inputs: any[], testData: any): any[][] {
    const combinations: any[][] = [];
    
    // Generate a few key combinations
    for (let i = 0; i < 10; i++) {
      const params: any[] = [];
      
      for (const input of inputs) {
        params.push(this.getTestValueForType(input.type, testData, i));
      }
      
      combinations.push(params);
    }
    
    return combinations;
  }

  private getTestValueForType(type: string, testData: any, index: number): any {
    switch (type) {
      case 'address':
        return testData.testAddresses[index % testData.testAddresses.length];
      case 'uint256':
      case 'uint':
        return testData.commonAmounts[index % testData.commonAmounts.length];
      case 'bytes32':
        return testData.merkleProofs[0]?.root || ethers.ZeroHash;
      case 'bytes32[]':
        return testData.merkleProofs[0]?.proof || [];
      case 'bool':
        return index % 2 === 0;
      case 'string':
        return `test_string_${index}`;
      default:
        return this.getDefaultValueForType(type);
    }
  }

  private getDefaultValueForType(type: string): any {
    if (type.includes('uint')) return '0';
    if (type === 'address') return ethers.ZeroAddress;
    if (type === 'bool') return false;
    if (type === 'string') return '';
    if (type === 'bytes32') return ethers.ZeroHash;
    if (type.includes('[]')) return [];
    return '0';
  }
}

export const merkleProofGenerator = new MerkleProofGenerator();
