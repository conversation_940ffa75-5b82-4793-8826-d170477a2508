import express from 'express';
import cors from 'cors';
import path from 'path';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import { db } from '../database';
import { scanner } from '../scanner';
import { blockMonitor } from '../monitor';
import { contractFuzzer } from '../fuzzer';
import { logger } from '../logger';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// WebSocket connections for real-time updates
const clients = new Set<any>();

wss.on('connection', (ws) => {
  clients.add(ws);
  logger.info('Dashboard client connected');
  
  ws.on('close', () => {
    clients.delete(ws);
    logger.info('Dashboard client disconnected');
  });
});

// Broadcast to all connected clients
function broadcast(data: any) {
  clients.forEach((client) => {
    if (client.readyState === 1) { // WebSocket.OPEN
      client.send(JSON.stringify(data));
    }
  });
}

// API Routes

// Get system statistics
app.get('/api/stats', async (req, res) => {
  try {
    const stats = await db.getStats();
    const pendingExploits = await db.getUnexecutedExploits();
    
    res.json({
      ...stats,
      pendingExploits: pendingExploits.length,
      isScanning: scanner.getIsRunning(),
      isMonitoring: blockMonitor.isRunning(),
      monitoringStatus: blockMonitor.getMonitoringStatus()
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch stats' });
  }
});

// Get recent exploits
app.get('/api/exploits', async (req, res) => {
  try {
    const exploits = await db.getUnexecutedExploits();
    res.json(exploits);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch exploits' });
  }
});

// Get contracts by chain
app.get('/api/contracts/:chainId', async (req, res) => {
  try {
    const chainId = parseInt(req.params.chainId);
    const limit = parseInt(req.query.limit as string) || 100;
    const contracts = await db.getContractsByChain(chainId, limit);
    res.json(contracts);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch contracts' });
  }
});

// Get scan status for all chains
app.get('/api/scan-status', async (req, res) => {
  try {
    const chains = [1, 42161, 8453, 10, 137]; // Ethereum, Arbitrum, Base, Optimism, Polygon
    const statuses = await Promise.all(
      chains.map(async (chainId) => {
        const status = await db.getScanStatus(chainId);
        return { chainId, ...status };
      })
    );
    res.json(statuses);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch scan status' });
  }
});

// Get fuzzing results
app.get('/api/fuzzing-results', async (req, res) => {
  try {
    const contractAddress = req.query.contract as string;
    const limit = parseInt(req.query.limit as string) || 100;
    const results = await db.getFuzzingResults(contractAddress, limit);
    res.json(results);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch fuzzing results' });
  }
});

// Control endpoints

// Start/stop scanning
app.post('/api/scan/:action', async (req, res) => {
  try {
    const action = req.params.action;
    
    if (action === 'start') {
      scanner.scanAllChains();
      broadcast({ type: 'scan_started' });
      res.json({ message: 'Scan started' });
    } else if (action === 'stop') {
      scanner.stop();
      broadcast({ type: 'scan_stopped' });
      res.json({ message: 'Scan stopped' });
    } else if (action === 'incremental') {
      scanner.scanAllChainsIncremental();
      broadcast({ type: 'incremental_scan_started' });
      res.json({ message: 'Incremental scan started' });
    } else {
      res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    res.status(500).json({ error: 'Failed to control scanner' });
  }
});

// Start/stop monitoring
app.post('/api/monitor/:action', async (req, res) => {
  try {
    const action = req.params.action;
    
    if (action === 'start') {
      await blockMonitor.startMonitoring();
      broadcast({ type: 'monitoring_started' });
      res.json({ message: 'Monitoring started' });
    } else if (action === 'stop') {
      await blockMonitor.stopMonitoring();
      broadcast({ type: 'monitoring_stopped' });
      res.json({ message: 'Monitoring stopped' });
    } else {
      res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    res.status(500).json({ error: 'Failed to control monitor' });
  }
});

// Execute pending exploits
app.post('/api/execute', async (req, res) => {
  try {
    await scanner.executePendingExploits();
    broadcast({ type: 'exploits_executed' });
    res.json({ message: 'Exploits executed' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to execute exploits' });
  }
});

// Scan specific contract
app.post('/api/scan-contract', async (req, res) => {
  try {
    const { address, chainId } = req.body;
    
    if (!address || !chainId) {
      return res.status(400).json({ error: 'Address and chainId are required' });
    }
    
    await scanner.scanSpecificContract(address, chainId);
    broadcast({ type: 'contract_scanned', address, chainId });
    res.json({ message: 'Contract scanned' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to scan contract' });
  }
});

// Fuzz specific contract
app.post('/api/fuzz-contract', async (req, res) => {
  try {
    const { address, chainId, maxIterations } = req.body;
    
    if (!address || !chainId) {
      return res.status(400).json({ error: 'Address and chainId are required' });
    }
    
    // This would need to be implemented with proper contract fetching
    // const contract = await contractFetcher.fetchContractDetails(chain, address);
    // const results = await contractFuzzer.fuzzContract(contract, targetFunctions, maxIterations);
    
    broadcast({ type: 'contract_fuzzed', address, chainId });
    res.json({ message: 'Contract fuzzing started' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fuzz contract' });
  }
});

// Get system logs
app.get('/api/logs', async (req, res) => {
  try {
    // This would need to be implemented with proper log storage
    const logs = [
      { timestamp: new Date().toISOString(), level: 'info', message: 'System started' },
      { timestamp: new Date().toISOString(), level: 'warn', message: 'No contracts found on chain' },
    ];
    res.json(logs);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch logs' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Serve the dashboard
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

const PORT = process.env.DASHBOARD_PORT || 3000;

server.listen(PORT, () => {
  logger.info(`Dashboard server running on port ${PORT}`);
  logger.info(`Dashboard URL: http://localhost:${PORT}`);
});

export { app, server, broadcast };
