<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Archaeologist Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .pulse-green {
            animation: pulse-green 2s infinite;
        }
        
        @keyframes pulse-green {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .scan-animation {
            animation: scan 2s linear infinite;
        }
        
        @keyframes scan {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .exploit-glow {
            animation: exploit-glow 1s ease-in-out infinite alternate;
        }
        
        @keyframes exploit-glow {
            from { box-shadow: 0 0 5px #f59e0b; }
            to { box-shadow: 0 0 20px #f59e0b, 0 0 30px #f59e0b; }
        }
        
        .terminal {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            overflow-y: auto;
            max-height: 300px;
        }
        
        .chain-card {
            transition: all 0.3s ease;
        }
        
        .chain-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .exploit-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .contract-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .monitoring-card {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <!-- Header -->
    <header class="bg-gray-800 shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-search text-3xl text-blue-400"></i>
                    <h1 class="text-2xl font-bold">Contract Archaeologist</h1>
                    <div class="flex items-center space-x-2">
                        <div id="status-indicator" class="w-3 h-3 rounded-full bg-gray-500"></div>
                        <span id="status-text" class="text-sm">Offline</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="theme-toggle" class="p-2 rounded-lg bg-gray-700 hover:bg-gray-600">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="text-sm">
                        <div>Uptime: <span id="uptime">--</span></div>
                        <div>Last Update: <span id="last-update">--</span></div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card rounded-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Contracts Scanned</h3>
                        <p class="text-3xl font-bold" id="total-contracts">0</p>
                    </div>
                    <i class="fas fa-file-contract text-4xl opacity-50"></i>
                </div>
            </div>
            
            <div class="exploit-card rounded-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Exploits Found</h3>
                        <p class="text-3xl font-bold" id="total-exploits">0</p>
                    </div>
                    <i class="fas fa-bug text-4xl opacity-50"></i>
                </div>
            </div>
            
            <div class="contract-card rounded-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Value Extracted</h3>
                        <p class="text-3xl font-bold" id="total-value">0 ETH</p>
                    </div>
                    <i class="fas fa-coins text-4xl opacity-50"></i>
                </div>
            </div>
            
            <div class="monitoring-card rounded-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Pending Exploits</h3>
                        <p class="text-3xl font-bold" id="pending-exploits">0</p>
                    </div>
                    <i class="fas fa-clock text-4xl opacity-50"></i>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Control Panel</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="space-y-2">
                    <h3 class="font-semibold">Scanning</h3>
                    <button id="start-scan" class="w-full bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                        <i class="fas fa-play mr-2"></i>Start Scan
                    </button>
                    <button id="incremental-scan" class="w-full bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                        <i class="fas fa-forward mr-2"></i>Incremental Scan
                    </button>
                    <button id="stop-scan" class="w-full bg-red-600 hover:bg-red-700 px-4 py-2 rounded">
                        <i class="fas fa-stop mr-2"></i>Stop Scan
                    </button>
                </div>
                
                <div class="space-y-2">
                    <h3 class="font-semibold">Monitoring</h3>
                    <button id="start-monitor" class="w-full bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                        <i class="fas fa-eye mr-2"></i>Start Monitoring
                    </button>
                    <button id="stop-monitor" class="w-full bg-red-600 hover:bg-red-700 px-4 py-2 rounded">
                        <i class="fas fa-eye-slash mr-2"></i>Stop Monitoring
                    </button>
                </div>
                
                <div class="space-y-2">
                    <h3 class="font-semibold">Execution</h3>
                    <button id="execute-exploits" class="w-full bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded">
                        <i class="fas fa-play-circle mr-2"></i>Execute Exploits
                    </button>
                    <button id="refresh-data" class="w-full bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded">
                        <i class="fas fa-sync mr-2"></i>Refresh Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Chain Status -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Chain Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4" id="chain-status">
                <!-- Chain cards will be populated here -->
            </div>
        </div>

        <!-- Recent Exploits -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Recent Exploits</h2>
            <div class="overflow-x-auto">
                <table class="w-full table-auto">
                    <thead>
                        <tr class="text-left border-b border-gray-700">
                            <th class="pb-2">Contract</th>
                            <th class="pb-2">Function</th>
                            <th class="pb-2">Estimated Value</th>
                            <th class="pb-2">Priority</th>
                            <th class="pb-2">Chain</th>
                            <th class="pb-2">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="exploits-table">
                        <!-- Exploits will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Manual Contract Scan -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Manual Contract Scan</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <input type="text" id="contract-address" placeholder="Contract Address" 
                       class="bg-gray-700 border border-gray-600 rounded px-3 py-2 w-full">
                <select id="chain-select" class="bg-gray-700 border border-gray-600 rounded px-3 py-2 w-full">
                    <option value="1">Ethereum</option>
                    <option value="42161">Arbitrum</option>
                    <option value="8453">Base</option>
                    <option value="10">Optimism</option>
                    <option value="137">Polygon</option>
                </select>
                <button id="scan-contract" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded w-full">
                    <i class="fas fa-search mr-2"></i>Scan Contract
                </button>
            </div>
        </div>

        <!-- System Logs -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-bold mb-4">System Logs</h2>
            <div class="terminal p-4 rounded" id="logs-container">
                <div class="text-green-400">[INFO] Contract Archaeologist Dashboard initialized</div>
                <div class="text-yellow-400">[WARN] No active scans running</div>
                <div class="text-blue-400">[INFO] Monitoring status: Offline</div>
            </div>
        </div>
    </main>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- Toast notifications will appear here -->
    </div>

    <script>
        // WebSocket connection for real-time updates
        let ws;
        let isConnected = false;
        
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const logsContainer = document.getElementById('logs-container');
        
        // Initialize WebSocket connection
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}`);
            
            ws.onopen = function() {
                isConnected = true;
                statusIndicator.className = 'w-3 h-3 rounded-full bg-green-500 pulse-green';
                statusText.textContent = 'Connected';
                addLog('Connected to dashboard server', 'success');
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = function() {
                isConnected = false;
                statusIndicator.className = 'w-3 h-3 rounded-full bg-red-500';
                statusText.textContent = 'Disconnected';
                addLog('Disconnected from dashboard server', 'error');
                
                // Reconnect after 5 seconds
                setTimeout(initWebSocket, 5000);
            };
            
            ws.onerror = function(error) {
                addLog('WebSocket error: ' + error, 'error');
            };
        }
        
        // Handle WebSocket messages
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'scan_started':
                    addLog('Scan started', 'info');
                    showToast('Scan started', 'success');
                    break;
                case 'scan_stopped':
                    addLog('Scan stopped', 'info');
                    showToast('Scan stopped', 'info');
                    break;
                case 'monitoring_started':
                    addLog('Real-time monitoring started', 'info');
                    showToast('Monitoring started', 'success');
                    break;
                case 'monitoring_stopped':
                    addLog('Real-time monitoring stopped', 'info');
                    showToast('Monitoring stopped', 'info');
                    break;
                case 'exploits_executed':
                    addLog('Pending exploits executed', 'success');
                    showToast('Exploits executed', 'success');
                    refreshData();
                    break;
            }
        }
        
        // Add log entry
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-blue-400',
                success: 'text-green-400',
                warning: 'text-yellow-400',
                error: 'text-red-400'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type];
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        // Show toast notification
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `p-4 rounded-lg shadow-lg text-white ${
                type === 'success' ? 'bg-green-600' : 
                type === 'error' ? 'bg-red-600' : 
                type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
            }`;
            toast.textContent = message;
            
            document.getElementById('toast-container').appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
        
        // API functions
        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(`/api${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`API call failed: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                addLog(`API Error: ${error.message}`, 'error');
                showToast(`Error: ${error.message}`, 'error');
                throw error;
            }
        }
        
        // Refresh all data
        async function refreshData() {
            try {
                // Update stats
                const stats = await apiCall('/stats');
                document.getElementById('total-contracts').textContent = stats.totalContracts;
                document.getElementById('total-exploits').textContent = stats.exploitableContracts;
                document.getElementById('total-value').textContent = `${stats.totalValueExtracted} ETH`;
                document.getElementById('pending-exploits').textContent = stats.pendingExploits;
                
                // Update chain status
                const chainStatus = await apiCall('/scan-status');
                updateChainStatus(chainStatus);
                
                // Update exploits table
                const exploits = await apiCall('/exploits');
                updateExploitsTable(exploits);
                
                document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
            } catch (error) {
                // Error already logged in apiCall
            }
        }
        
        // Update chain status cards
        function updateChainStatus(chainStatus) {
            const container = document.getElementById('chain-status');
            container.innerHTML = '';
            
            const chainNames = {
                1: 'Ethereum',
                42161: 'Arbitrum',
                8453: 'Base',
                10: 'Optimism',
                137: 'Polygon'
            };
            
            chainStatus.forEach(status => {
                const card = document.createElement('div');
                card.className = 'chain-card bg-gray-700 rounded-lg p-4 text-center';
                
                const isActive = status.lastScanTime && new Date(status.lastScanTime) > new Date(Date.now() - 24*60*60*1000);
                
                card.innerHTML = `
                    <h3 class="font-semibold">${chainNames[status.chainId]}</h3>
                    <div class="text-2xl font-bold ${isActive ? 'text-green-400' : 'text-gray-400'}">${status.contractsScanned || 0}</div>
                    <div class="text-sm text-gray-400">Block: ${status.lastProcessedBlock || 'N/A'}</div>
                    <div class="text-xs text-gray-500">${status.lastScanTime ? new Date(status.lastScanTime).toLocaleString() : 'Never'}</div>
                `;
                
                container.appendChild(card);
            });
        }
        
        // Update exploits table
        function updateExploitsTable(exploits) {
            const tbody = document.getElementById('exploits-table');
            tbody.innerHTML = '';
            
            exploits.slice(0, 10).forEach(exploit => {
                const row = document.createElement('tr');
                row.className = 'border-b border-gray-700 hover:bg-gray-700';
                
                const chainNames = {
                    1: 'Ethereum',
                    42161: 'Arbitrum',
                    8453: 'Base',
                    10: 'Optimism',
                    137: 'Polygon'
                };
                
                row.innerHTML = `
                    <td class="py-2 font-mono text-sm">${exploit.address.slice(0, 10)}...</td>
                    <td class="py-2">${exploit.functionName}</td>
                    <td class="py-2 text-yellow-400">${exploit.estimatedValue} ETH</td>
                    <td class="py-2">
                        <span class="px-2 py-1 rounded text-xs ${
                            exploit.priority === 1 ? 'bg-red-600' :
                            exploit.priority === 2 ? 'bg-yellow-600' : 'bg-green-600'
                        }">${exploit.priority}</span>
                    </td>
                    <td class="py-2">${chainNames[exploit.chainId]}</td>
                    <td class="py-2">
                        <button class="text-blue-400 hover:text-blue-300 mr-2" onclick="viewContract('${exploit.address}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="text-green-400 hover:text-green-300" onclick="executeExploit('${exploit.address}', '${exploit.functionName}')">
                            <i class="fas fa-play"></i>
                        </button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }
        
        // View contract details
        function viewContract(address) {
            window.open(`https://etherscan.io/address/${address}`, '_blank');
        }
        
        // Execute specific exploit
        async function executeExploit(address, functionName) {
            if (confirm(`Execute exploit for ${functionName} on ${address}?`)) {
                try {
                    await apiCall('/execute', { method: 'POST' });
                    showToast('Exploit executed', 'success');
                } catch (error) {
                    // Error already handled in apiCall
                }
            }
        }
        
        // Event listeners
        document.getElementById('start-scan').addEventListener('click', async () => {
            try {
                await apiCall('/scan/start', { method: 'POST' });
            } catch (error) {
                // Error already handled in apiCall
            }
        });
        
        document.getElementById('incremental-scan').addEventListener('click', async () => {
            try {
                await apiCall('/scan/incremental', { method: 'POST' });
            } catch (error) {
                // Error already handled in apiCall
            }
        });
        
        document.getElementById('stop-scan').addEventListener('click', async () => {
            try {
                await apiCall('/scan/stop', { method: 'POST' });
            } catch (error) {
                // Error already handled in apiCall
            }
        });
        
        document.getElementById('start-monitor').addEventListener('click', async () => {
            try {
                await apiCall('/monitor/start', { method: 'POST' });
            } catch (error) {
                // Error already handled in apiCall
            }
        });
        
        document.getElementById('stop-monitor').addEventListener('click', async () => {
            try {
                await apiCall('/monitor/stop', { method: 'POST' });
            } catch (error) {
                // Error already handled in apiCall
            }
        });
        
        document.getElementById('execute-exploits').addEventListener('click', async () => {
            if (confirm('Execute all pending exploits?')) {
                try {
                    await apiCall('/execute', { method: 'POST' });
                } catch (error) {
                    // Error already handled in apiCall
                }
            }
        });
        
        document.getElementById('refresh-data').addEventListener('click', refreshData);
        
        document.getElementById('scan-contract').addEventListener('click', async () => {
            const address = document.getElementById('contract-address').value;
            const chainId = parseInt(document.getElementById('chain-select').value);
            
            if (!address) {
                showToast('Please enter a contract address', 'warning');
                return;
            }
            
            try {
                await apiCall('/scan-contract', {
                    method: 'POST',
                    body: JSON.stringify({ address, chainId })
                });
                document.getElementById('contract-address').value = '';
                showToast('Contract scan started', 'success');
            } catch (error) {
                // Error already handled in apiCall
            }
        });
        
        // Initialize dashboard
        function init() {
            initWebSocket();
            refreshData();
            
            // Auto-refresh every 30 seconds
            setInterval(refreshData, 30000);
            
            // Update uptime every second
            setInterval(() => {
                const uptime = Math.floor(performance.now() / 1000);
                const hours = Math.floor(uptime / 3600);
                const minutes = Math.floor((uptime % 3600) / 60);
                const seconds = uptime % 60;
                document.getElementById('uptime').textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }
        
        // Start the dashboard
        init();
    </script>
</body>
</html>
