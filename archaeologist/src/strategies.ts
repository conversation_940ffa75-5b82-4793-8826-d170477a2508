import { ethers } from 'ethers';
import { FuzzingStrategy } from './fuzzer';

// Base strategy class
abstract class BaseFuzzingStrategy implements FuzzingStrategy {
  abstract name: string;
  abstract description: string;
  abstract priority: number;
  
  abstract generateParameters(abiFunction: any): any[][];
  
  protected getParameterTypes(abiFunction: any): string[] {
    return (abiFunction.inputs || []).map((input: any) => input.type);
  }
  
  protected generateAddress(type: 'zero' | 'random' | 'max' | 'common'): string {
    switch (type) {
      case 'zero':
        return ethers.ZeroAddress;
      case 'random':
        return ethers.Wallet.createRandom().address;
      case 'max':
        return '******************************************';
      case 'common':
        // Common addresses that might have special behavior
        const commonAddresses = [
          '******************************************',
          '******************************************',
          '******************************************',
          '******************************************'
        ];
        return commonAddresses[Math.floor(Math.random() * commonAddresses.length)];
      default:
        return ethers.ZeroAddress;
    }
  }
  
  protected generateUint(bits: number, type: 'zero' | 'one' | 'max' | 'boundary' | 'random'): bigint {
    const maxValue = (2n ** BigInt(bits)) - 1n;
    
    switch (type) {
      case 'zero':
        return 0n;
      case 'one':
        return 1n;
      case 'max':
        return maxValue;
      case 'boundary':
        // Common boundary values
        const boundaries = [0n, 1n, maxValue, maxValue - 1n, 2n ** 255n - 1n];
        return boundaries[Math.floor(Math.random() * boundaries.length)];
      case 'random':
        return BigInt(Math.floor(Math.random() * Number(maxValue > 2n ** 32n ? 2n ** 32n : maxValue)));
      default:
        return 0n;
    }
  }
}

// Boundary value testing strategy
export class BoundaryValueStrategy extends BaseFuzzingStrategy {
  name = 'BoundaryValueStrategy';
  description = 'Tests boundary values like 0, 1, max values';
  priority = 1;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    const parameterSets: any[][] = [];
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    // Generate boundary value combinations
    const boundaryTypes = ['zero', 'one', 'max', 'boundary'];
    
    for (const boundaryType of boundaryTypes) {
      const params = paramTypes.map(type => {
        if (type === 'address') {
          return this.generateAddress(boundaryType as any);
        } else if (type.startsWith('uint')) {
          const bits = type === 'uint' ? 256 : parseInt(type.slice(4));
          return this.generateUint(bits, boundaryType as any);
        } else if (type === 'bool') {
          return boundaryType === 'zero' ? false : true;
        } else if (type === 'string') {
          return boundaryType === 'zero' ? '' : 'test';
        } else if (type.startsWith('bytes')) {
          return boundaryType === 'zero' ? '0x' : '0x1234';
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// Overflow/Underflow testing strategy
export class OverflowUnderflowStrategy extends BaseFuzzingStrategy {
  name = 'OverflowUnderflowStrategy';
  description = 'Tests for integer overflow and underflow vulnerabilities';
  priority = 1;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    const parameterSets: any[][] = [];
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    // Generate overflow/underflow test cases
    const overflowValues = [
      { type: 'max', description: 'Maximum value' },
      { type: 'max_minus_one', description: 'Maximum value - 1' },
      { type: 'near_max', description: 'Near maximum value' },
      { type: 'overflow', description: 'Overflow attempt' }
    ];
    
    for (const valueType of overflowValues) {
      const params = paramTypes.map(type => {
        if (type.startsWith('uint')) {
          const bits = type === 'uint' ? 256 : parseInt(type.slice(4));
          const maxValue = (2n ** BigInt(bits)) - 1n;
          
          switch (valueType.type) {
            case 'max':
              return maxValue;
            case 'max_minus_one':
              return maxValue - 1n;
            case 'near_max':
              return maxValue - BigInt(Math.floor(Math.random() * 100));
            case 'overflow':
              return maxValue + 1n; // This should fail, but might reveal issues
            default:
              return 0n;
          }
        } else if (type === 'address') {
          return this.generateAddress('zero');
        } else if (type === 'bool') {
          return false;
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// Reentrancy testing strategy
export class ReentrancyStrategy extends BaseFuzzingStrategy {
  name = 'ReentrancyStrategy';
  description = 'Tests for reentrancy vulnerabilities';
  priority = 1;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    // Generate parameters that might trigger reentrancy
    const parameterSets: any[][] = [];
    
    // Test with attacker-controlled addresses
    const attackerAddresses = [
      '******************************************',
      '******************************************',
      '******************************************'
    ];
    
    for (const attackerAddress of attackerAddresses) {
      const params = paramTypes.map(type => {
        if (type === 'address') {
          return attackerAddress;
        } else if (type.startsWith('uint')) {
          // Small amounts for reentrancy testing
          return BigInt(Math.floor(Math.random() * 1000) + 1);
        } else if (type === 'bool') {
          return true;
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// Access control testing strategy
export class AccessControlStrategy extends BaseFuzzingStrategy {
  name = 'AccessControlStrategy';
  description = 'Tests for access control vulnerabilities';
  priority = 1;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    // Generate parameters to test access control
    const parameterSets: any[][] = [];
    
    // Test with various addresses that might bypass access control
    const testAddresses = [
      ethers.ZeroAddress,
      '******************************************',
      '******************************************',
      '******************************************',
      '******************************************',
      '******************************************'
    ];
    
    for (const testAddress of testAddresses) {
      const params = paramTypes.map(type => {
        if (type === 'address') {
          return testAddress;
        } else if (type.startsWith('uint')) {
          return 1n; // Small amounts for access control testing
        } else if (type === 'bool') {
          return true;
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// State manipulation strategy
export class StateManipulationStrategy extends BaseFuzzingStrategy {
  name = 'StateManipulationStrategy';
  description = 'Tests state manipulation vulnerabilities';
  priority = 2;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Generate parameters that might manipulate state unexpectedly
    const stateValues = [0, 1, 2, 10, 100, 1000];
    
    for (const value of stateValues) {
      const params = paramTypes.map(type => {
        if (type.startsWith('uint')) {
          return BigInt(value);
        } else if (type === 'address') {
          return this.generateAddress('random');
        } else if (type === 'bool') {
          return value % 2 === 0;
        }
        return value;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// Token amount strategy
export class TokenAmountStrategy extends BaseFuzzingStrategy {
  name = 'TokenAmountStrategy';
  description = 'Tests various token amounts including dust and large amounts';
  priority = 2;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Common token amounts (considering 18 decimals)
    const tokenAmounts = [
      1n, // 1 wei
      1000n, // 1000 wei
      ethers.parseEther('0.001'), // 0.001 ETH
      ethers.parseEther('1'), // 1 ETH
      ethers.parseEther('100'), // 100 ETH
      ethers.parseEther('10000'), // 10,000 ETH
      ethers.parseEther('1000000'), // 1M ETH
    ];
    
    for (const amount of tokenAmounts) {
      const params = paramTypes.map(type => {
        if (type.startsWith('uint')) {
          return amount;
        } else if (type === 'address') {
          return this.generateAddress('random');
        } else if (type === 'bool') {
          return true;
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// Address strategy for testing different address types
export class AddressStrategy extends BaseFuzzingStrategy {
  name = 'AddressStrategy';
  description = 'Tests various address types and patterns';
  priority = 2;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Various address types to test
    const addressTypes: Array<'zero' | 'random' | 'max' | 'common'> = ['zero', 'random', 'max', 'common'];
    
    for (const addressType of addressTypes) {
      const params = paramTypes.map(type => {
        if (type === 'address') {
          return this.generateAddress(addressType);
        } else if (type.startsWith('uint')) {
          return 1n;
        } else if (type === 'bool') {
          return true;
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// Timestamp strategy
export class TimestampStrategy extends BaseFuzzingStrategy {
  name = 'TimestampStrategy';
  description = 'Tests timestamp-related vulnerabilities';
  priority = 3;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Timestamp values to test
    const now = BigInt(Math.floor(Date.now() / 1000));
    const timestampValues = [
      0n, // Unix epoch
      1n, // Near epoch
      now - 86400n, // 1 day ago
      now, // Current time
      now + 86400n, // 1 day from now
      now + 31536000n, // 1 year from now
      2n ** 32n - 1n, // Max 32-bit timestamp
    ];
    
    for (const timestamp of timestampValues) {
      const params = paramTypes.map(type => {
        if (type.startsWith('uint')) {
          return timestamp;
        } else if (type === 'address') {
          return this.generateAddress('random');
        } else if (type === 'bool') {
          return true;
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// Array strategy
export class ArrayStrategy extends BaseFuzzingStrategy {
  name = 'ArrayStrategy';
  description = 'Tests array-related vulnerabilities';
  priority = 3;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Array sizes to test
    const arraySizes = [0, 1, 2, 10, 100, 1000];
    
    for (const size of arraySizes) {
      const params = paramTypes.map(type => {
        if (type.endsWith('[]')) {
          const baseType = type.slice(0, -2);
          const array = [];
          
          for (let i = 0; i < size; i++) {
            if (baseType === 'address') {
              array.push(this.generateAddress('random'));
            } else if (baseType.startsWith('uint')) {
              array.push(BigInt(i));
            } else {
              array.push(i);
            }
          }
          
          return array;
        } else if (type.startsWith('uint')) {
          return BigInt(size);
        } else if (type === 'address') {
          return this.generateAddress('random');
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

// Additional specialized strategies
export class SlippageStrategy extends BaseFuzzingStrategy {
  name = 'SlippageStrategy';
  description = 'Tests slippage-related vulnerabilities in DEX functions';
  priority = 3;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Slippage percentages (basis points)
    const slippageValues = [0n, 1n, 50n, 100n, 500n, 1000n, 5000n, 10000n];
    
    for (const slippage of slippageValues) {
      const params = paramTypes.map(type => {
        if (type.startsWith('uint')) {
          return slippage;
        } else if (type === 'address') {
          return this.generateAddress('random');
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

export class GasLimitStrategy extends BaseFuzzingStrategy {
  name = 'GasLimitStrategy';
  description = 'Tests gas limit related issues';
  priority = 3;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Gas amounts to test
    const gasValues = [1n, 21000n, 100000n, 1000000n, 8000000n];
    
    for (const gasValue of gasValues) {
      const params = paramTypes.map(type => {
        if (type.startsWith('uint')) {
          return gasValue;
        } else if (type === 'address') {
          return this.generateAddress('random');
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

export class MerkleProofStrategy extends BaseFuzzingStrategy {
  name = 'MerkleProofStrategy';
  description = 'Tests Merkle proof vulnerabilities';
  priority = 3;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Mock Merkle proof data
    const mockProofs = [
      [],
      ['0x1234567890123456789012345678901234567890123456789012345678901234'],
      ['******************************************111111111111111111111111', '******************************************222222222222222222222222']
    ];
    
    for (const proof of mockProofs) {
      const params = paramTypes.map(type => {
        if (type === 'bytes32[]') {
          return proof;
        } else if (type === 'bytes32') {
          return proof[0] || '0x0000000000000000000000000000000000000000000000000000000000000000';
        } else if (type.startsWith('uint')) {
          return 1n;
        } else if (type === 'address') {
          return this.generateAddress('random');
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

export class SignatureStrategy extends BaseFuzzingStrategy {
  name = 'SignatureStrategy';
  description = 'Tests signature-related vulnerabilities';
  priority = 3;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Mock signature components
    const mockSignatures = [
      { v: 27, r: '******************************************111111111111111111111111', s: '******************************************222222222222222222222222' },
      { v: 28, r: '0x3333333333333333333333333333333333333333333333333333333333333333', s: '0x4444444444444444444444444444444444444444444444444444444444444444' }
    ];
    
    for (const sig of mockSignatures) {
      const params = paramTypes.map(type => {
        if (type === 'uint8' || type === 'uint256') {
          return BigInt(sig.v);
        } else if (type === 'bytes32') {
          return sig.r;
        } else if (type === 'bytes') {
          return sig.r + sig.s.slice(2);
        } else if (type === 'address') {
          return this.generateAddress('random');
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

export class PricingStrategy extends BaseFuzzingStrategy {
  name = 'PricingStrategy';
  description = 'Tests pricing oracle vulnerabilities';
  priority = 3;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Price values to test (considering different decimal places)
    const priceValues = [
      0n, // Zero price
      1n, // Minimum price
      ethers.parseEther('0.01'), // Low price
      ethers.parseEther('1'), // Normal price
      ethers.parseEther('1000'), // High price
      ethers.parseEther('1000000'), // Extreme price
    ];
    
    for (const price of priceValues) {
      const params = paramTypes.map(type => {
        if (type.startsWith('uint')) {
          return price;
        } else if (type === 'address') {
          return this.generateAddress('random');
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}

export class RoundingStrategy extends BaseFuzzingStrategy {
  name = 'RoundingStrategy';
  description = 'Tests rounding vulnerabilities';
  priority = 3;
  
  generateParameters(abiFunction: any): any[][] {
    const paramTypes = this.getParameterTypes(abiFunction);
    
    if (paramTypes.length === 0) {
      return [[]];
    }
    
    const parameterSets: any[][] = [];
    
    // Values that might cause rounding issues
    const roundingValues = [
      1n, // Minimum value
      2n, // Even number
      3n, // Odd number
      9n, // Single digit
      10n, // Round number
      99n, // Near round number
      100n, // Round number
      999n, // Near round number
      1000n, // Round number
    ];
    
    for (const value of roundingValues) {
      const params = paramTypes.map(type => {
        if (type.startsWith('uint')) {
          return value;
        } else if (type === 'address') {
          return this.generateAddress('random');
        }
        return 0;
      });
      
      parameterSets.push(params);
    }
    
    return parameterSets;
  }
}
