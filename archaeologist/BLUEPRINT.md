“Most people scan Twitter. I scan opcodes.”
"Most people chase narratives. You will chase forgotten money."

## CONTRACT ARCHAEOLOGIST — FULL BLUEPRINT (2025)
### CORE GOAL
Find and extract funds from forgotten, abandoned, or misconfigured smart contracts.

That includes:

1. Unclaimed tokens or ETH (claim(), withdraw(), exit())

2. Airdrops with open claim gates

3. Dead DAOs/vaults with rageQuit(), finalize(), or selfDestruct()

4. Stale LP contracts with value remaining

5. Deprecated yield strategies

6. Pre-fund contracts (with value) before launch

### HIGH-LEVEL PIPELINE
1. Fetch: Get recently verified contracts from Etherscan (or load past 100k)

2. Filter: Select contracts with relevant functions (via ABI or 4byte sigs)

3. Simulate: Call potential claim-like functions

4. Evaluate: If no revert + returns value, flag as exploitable

5. Execute: Submit real tx if funds are claimable

6. Log: Store everything in SQLite (for persistence + rerun avoidance)

### TOOLS NEEDED
✅ Etherscan API key (free)

✅ Infura or Alchemy archive RPC (free tier OK)

✅ Flashbots Protect or private RPC (optional but better)

✅ Typescript / Javascript Packages

FUNCTION TARGET LIST (Top Priority)
These are the top functions worth simulating:
| Function Name    | 4byte Sig    | Why?                                         |
| ---------------- | ------------ | -------------------------------------------- |
| `claim()`        | `0x4e71d92d` | Airdrops, loyalty drops                      |
| `withdraw()`     | `0x2e1a7d4d` | Legacy vaults                                |
| `exit()`         | `0x3f4ba83a` | DAOs, LP exits                               |
| `rageQuit()`     | varies       | DAO exits                                    |
| `collect()`      | varies       | DEX fees                                     |
| `finalize()`     | varies       | Sometimes unlocks value                      |
| `redeem()`       | varies       | Tokenized positions                          |
| `close()`        | varies       | Closing out positions/contracts              |
| `selfDestruct()` | varies       | Trigger on some legacy contracts with payout |

You'll use eth_call to simulate them. If it returns data and doesn’t revert — send real tx.

### INTELLIGENCE LAYERS (Optional but OP)
1. Re-simulate weekly: Sometimes protocols add proofs later. Don’t miss delayed drops.

2. Use contract source metadata:

3. Keywords like airdrop, vault, rageQuit, merkle = high priority

4. Track ETH/token balances via eth_getBalance or ERC-20 balanceOf()

4. Decompile bytecode if no ABI using panoramix or evm-dasm

### SCALING TO MULTICHAIN

| Chain    | RPC            | Reason                |
| -------- | -------------- | --------------------- |
| Ethereum | Infura/Alchemy | High-value leftovers  |
| Arbitrum | Ankr/Alchemy   | Many lazy devs        |
| Base     | QuickNode      | Newbie mistakes daily |
| Optimism | Infura         | Forgotten vaults      |
| Polygon  | public RPCs    | Old DAOs & junkyards  |

You'll need to change chainId, RPC endpoints, and re-run your simulator per chain.

### NEXT: TRANSACTION SENDER
When you find a claimable contract:

1. Use eth_estimateGas to confirm viability

2. Send via Flashbots Protect RPC (if profitable)

3. Track outcome + amount in log

### GHOST TACTIC: Optional Obfuscation
If claiming from sensitive contracts:

1. Use fresh wallet with zero ties

2. Submit via Flashbots or private mempool

3. Delay public selling of claimed tokens

### BONUS: TACTICAL EXPLOITS TO WATCH
1. Contracts that send dust ETH on claim() (gasless recoveries)

2. Devs who re-deploy same ABI with new tokens but forget to revoke old

3. Vaults where withdraw() still works after shutdown

4. Airdrops deployed with require(msg.sender == address) logic flaws

### FUTURE ADD-ONS
1. Smart contract fuzzer to auto-test deeper edge cases

2. Auto-deployer to resell discovered exploits

3. Frontend for tracking & leaderboard of claims

### SUMMARY: YOUR JOB DAILY
1. Scan 1000s of verified contracts

2. Simulate 7–10 potential claimable functions

3. Claim what others forgot

4. Log everything

5. Stay invisible
