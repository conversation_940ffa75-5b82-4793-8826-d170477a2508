require('dotenv').config();
const axios = require('axios');

async function testEtherscanAPI() {
  const apiKey = process.env.ETHERSCAN_API_KEY;
  
  if (!apiKey) {
    console.error('❌ ETHERSCAN_API_KEY not found in .env file');
    return;
  }
  
  console.log('🔑 API Key found:', apiKey.substring(0, 10) + '...');
  
  try {
    // Test basic API call
    const response = await axios.get('https://api.etherscan.io/api', {
      params: {
        module: 'stats',
        action: 'ethsupply',
        apikey: apiKey
      },
      timeout: 10000
    });
    
    console.log('📡 API Response Status:', response.data.status);
    console.log('📡 API Response Message:', response.data.message);
    
    if (response.data.status === '1') {
      console.log('✅ Etherscan API is working correctly');
      console.log('📊 ETH Supply:', response.data.result);
    } else {
      console.error('❌ API Error:', response.data.message);
      console.error('💡 Possible solutions:');
      console.error('   1. Check if your API key is valid');
      console.error('   2. Make sure you have not exceeded rate limits');
      console.error('   3. Verify your API key has the correct permissions');
    }
  } catch (error) {
    console.error('❌ Network Error:', error.message);
    console.error('💡 Check your internet connection and API key');
  }
}

// Test contract listing (the problematic endpoint)
async function testContractListing() {
  const apiKey = process.env.ETHERSCAN_API_KEY;
  
  try {
    console.log('\n🔍 Testing contract listing endpoint...');
    const response = await axios.get('https://api.etherscan.io/api', {
      params: {
        module: 'contract',
        action: 'listcontracts',
        page: 1,
        offset: 5,
        apikey: apiKey
      },
      timeout: 10000
    });
    
    console.log('📡 Contract List Status:', response.data.status);
    console.log('📡 Contract List Message:', response.data.message);
    
    if (response.data.status === '1') {
      console.log('✅ Contract listing is working');
      console.log('📄 Found contracts:', response.data.result?.length || 0);
    } else {
      console.error('❌ Contract listing failed:', response.data.message);
      console.error('💡 This endpoint might not be available for free accounts');
      console.error('💡 The application will use fallback methods');
    }
  } catch (error) {
    console.error('❌ Contract listing error:', error.message);
  }
}

async function main() {
  console.log('🧪 Testing Etherscan API Configuration...\n');
  await testEtherscanAPI();
  await testContractListing();
  
  console.log('\n📋 Next steps:');
  console.log('1. If API is working, try running: npm run dev scan');
  console.log('2. If API fails, check your .env file configuration');
  console.log('3. Make sure your API key is from: https://etherscan.io/apis');
}

main().catch(console.error);
