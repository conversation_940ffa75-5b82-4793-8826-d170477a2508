#!/usr/bin/env node

/**
 * 🏺 DISCOVERY MODE CONFIGURATION
 * 
 * This script helps configure the archaeologist for optimal target discovery.
 * Use this to find contracts with target functions and analyze why they're
 * not meeting profitability thresholds.
 */

const fs = require('fs');
const path = require('path');

console.log('🏺 ARCHAEOLOGIST DISCOVERY MODE CONFIGURATION');
console.log('━'.repeat(60));
console.log('');

function createDiscoveryEnv() {
  const envPath = path.join(__dirname, '.env.discovery');
  
  const discoveryConfig = `# 🏺 ARCHAEOLOGIST DISCOVERY MODE
# This configuration lowers thresholds to find more targets for analysis

# Lower profit threshold for discovery (1.2x instead of 2x)
PROFIT_THRESHOLD=1.2

# Enable discovery mode logging
DISCOVERY_MODE=true

# Your existing API keys (copy from your main .env file)
# ETHERSCAN_API_KEY=your_api_key_here
# ETHEREUM_RPC_URL=your_rpc_url_here
# PRIVATE_KEY=your_private_key_here

# Optional: Focus on specific chains for faster discovery
# SCAN_CHAINS=1,42161  # Ethereum and Arbitrum only
`;

  fs.writeFileSync(envPath, discoveryConfig);
  console.log('✅ Created .env.discovery file');
  console.log('');
  console.log('📝 Next steps:');
  console.log('');
  console.log('1. Copy your API keys from .env to .env.discovery:');
  console.log('   cp .env .env.discovery');
  console.log('   # Then edit .env.discovery to add the discovery settings above');
  console.log('');
  console.log('2. Run discovery scan:');
  console.log('   cp .env.discovery .env');
  console.log('   yarn dev scan');
  console.log('');
  console.log('3. Restore normal settings:');
  console.log('   git checkout .env  # or restore your original .env');
  console.log('');
}

function showDiscoveryTips() {
  console.log('🎯 DISCOVERY MODE BENEFITS:');
  console.log('');
  console.log('✅ Lower Profit Threshold (1.2x vs 2x):');
  console.log('   • Finds more potential targets');
  console.log('   • Shows contracts with target functions');
  console.log('   • Helps understand market conditions');
  console.log('');
  console.log('✅ Enhanced Logging:');
  console.log('   • Shows rejected contracts and why');
  console.log('   • Displays potential values found');
  console.log('   • Reveals gas cost analysis');
  console.log('');
  console.log('✅ Market Analysis:');
  console.log('   • Understand current gas prices');
  console.log('   • See what values are available');
  console.log('   • Identify optimal scanning times');
  console.log('');
  console.log('🏺 ARCHAEOLOGIST STRATEGY:');
  console.log('');
  console.log('The archaeologist mindset is about patience and precision:');
  console.log('');
  console.log('❄️  COLD: No rush - wait for optimal conditions');
  console.log('🤫 QUIET: Silent monitoring until opportunities arise');
  console.log('⚔️  DANGEROUS: Strike with surgical precision when ready');
  console.log('');
  console.log('💡 OPTIMAL CONDITIONS FOR EXPLOITATION:');
  console.log('');
  console.log('1. 🕐 TIME: During high gas price periods, forgotten contracts');
  console.log('   accumulate value while becoming less likely to be claimed');
  console.log('');
  console.log('2. 📈 MARKET: Bull markets create more abandoned positions');
  console.log('   as people forget small amounts in protocols');
  console.log('');
  console.log('3. 🔄 PROTOCOL CHANGES: When protocols upgrade or migrate,');
  console.log('   old contracts often retain claimable value');
  console.log('');
  console.log('4. 💸 GAS EFFICIENCY: Target contracts during low gas periods');
  console.log('   for maximum profitability');
  console.log('');
  console.log('🎯 CURRENT MARKET ANALYSIS:');
  console.log('');
  console.log('If you\'re seeing "No exploitable contracts found", it likely means:');
  console.log('');
  console.log('• ✅ System is working correctly (high standards)');
  console.log('• ⏰ Market timing - may need to wait for better conditions');
  console.log('• 💰 Values are too small relative to current gas prices');
  console.log('• 🔍 Need to scan more chains or different time periods');
  console.log('');
  console.log('🏺 RECOMMENDATION:');
  console.log('');
  console.log('1. Run discovery mode to see what\'s being found');
  console.log('2. Monitor gas prices and scan during low-gas periods');
  console.log('3. Set up continuous monitoring for when conditions improve');
  console.log('4. Focus on chains with lower gas costs (Arbitrum, Base, Polygon)');
  console.log('');
  console.log('Remember: The best archaeologists are patient hunters.');
  console.log('The system is designed for precision, not volume.');
  console.log('');
  console.log('Dust. Silence. Extraction. 💀');
}

// Main execution
console.log('🔧 CONFIGURATION OPTIONS:');
console.log('');
console.log('1. Create discovery configuration');
console.log('2. Show discovery tips and strategy');
console.log('');

const args = process.argv.slice(2);

if (args.includes('--create') || args.includes('-c')) {
  createDiscoveryEnv();
} else if (args.includes('--tips') || args.includes('-t')) {
  showDiscoveryTips();
} else {
  console.log('Usage:');
  console.log('  node discovery-mode.js --create    # Create discovery configuration');
  console.log('  node discovery-mode.js --tips      # Show discovery tips');
  console.log('');
  console.log('Or run both:');
  console.log('  node discovery-mode.js --create --tips');
  console.log('');
  
  if (args.length === 0) {
    createDiscoveryEnv();
    console.log('');
    showDiscoveryTips();
  }
}
