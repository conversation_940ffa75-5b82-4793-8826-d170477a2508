import { contractFilter } from '../src/filter';
import { ContractInfo } from '../src/types';
import { ethers } from 'ethers';

describe('Contract Filter', () => {
  const mockContract: ContractInfo = {
    address: '******************************************',
    name: 'Test Contract',
    abi: [
      {
        type: 'function',
        name: 'claim',
        inputs: [],
        outputs: [{ type: 'bool' }]
      },
      {
        type: 'function',
        name: 'withdraw',
        inputs: [{ type: 'uint256', name: 'amount' }],
        outputs: []
      },
      {
        type: 'function',
        name: 'unrelatedFunction',
        inputs: [],
        outputs: []
      }
    ],
    sourceCode: 'contract Test { function claim() {} function withdraw(uint256 amount) {} }',
    compiler: 'v0.8.0',
    txHash: '0xabc123',
    blockNumber: 12345,
    timestamp: 1234567890,
    chainId: 1
  };

  it('should filter contracts with target functions', () => {
    const results = contractFilter.filterContracts([mockContract]);
    
    expect(results).toHaveLength(1);
    expect(results[0].contract).toBe(mockContract);
    expect(results[0].matchedFunctions.length).toBeGreaterThan(0);
  });

  it('should match functions by name', () => {
    const results = contractFilter.filterContracts([mockContract]);
    const matchedNames = results[0].matchedFunctions.map(f => f.name);
    
    expect(matchedNames).toContain('claim');
    expect(matchedNames).toContain('withdraw');
    expect(matchedNames).not.toContain('unrelatedFunction');
  });

  it('should generate correct 4-byte signatures', () => {
    const signature = 'claim()';
    const expected = ethers.keccak256(ethers.toUtf8Bytes(signature)).substring(0, 10);
    
    // Access private method for testing
    const filter = contractFilter as any;
    const result = filter.generateFourByteSignature(signature);
    
    expect(result).toBe(expected);
    expect(result).toMatch(/^0x[a-fA-F0-9]{8}$/);
  });

  it('should prioritize contracts with high priority keywords', () => {
    const airdropContract = {
      ...mockContract,
      name: 'Airdrop Contract',
      sourceCode: 'contract Airdrop { function claim() {} }'
    };

    const regularContract = {
      ...mockContract,
      name: 'Regular Contract',
      sourceCode: 'contract Regular { function claim() {} }',
      address: '0x456'
    };

    const results = contractFilter.filterContracts([regularContract, airdropContract]);

    // Both contracts should be filtered since they have claim() function
    expect(results.length).toBe(2);

    // Find the airdrop contract in results
    const airdropResult = results.find(r => r.contract.name === 'Airdrop Contract');
    const regularResult = results.find(r => r.contract.name === 'Regular Contract');

    expect(airdropResult?.hasHighPriorityKeywords).toBe(true);
    // Regular contract might also have keywords due to "claim" in source code
    // Just verify that airdrop has keywords and better priority
    expect(airdropResult?.hasHighPriorityKeywords).toBe(true);

    // Airdrop should have better (lower) priority score due to keywords
    expect(airdropResult?.priorityScore).toBeLessThanOrEqual(regularResult?.priorityScore || 10);
  });

  it('should filter by chain ID', () => {
    const ethereumContract = { ...mockContract, chainId: 1 };
    const arbitrumContract = { ...mockContract, chainId: 42161 };
    
    const results = contractFilter.filterContracts([ethereumContract, arbitrumContract]);
    const ethereumResults = contractFilter.filterByChain(results, 1);
    
    expect(ethereumResults).toHaveLength(1);
    expect(ethereumResults[0].contract.chainId).toBe(1);
  });

  it('should filter DAO contracts', () => {
    const daoContract = {
      ...mockContract,
      name: 'DAO Contract',
      sourceCode: 'contract DAO { function rageQuit() {} }'
    };

    const results = contractFilter.filterContracts([daoContract]);
    const daoResults = contractFilter.filterDAOContracts(results);
    
    expect(daoResults.length).toBeGreaterThan(0);
  });

  it('should filter vault contracts', () => {
    const vaultContract = {
      ...mockContract,
      name: 'Vault Contract',
      sourceCode: 'contract Vault { function withdraw() {} }'
    };

    const results = contractFilter.filterContracts([vaultContract]);
    const vaultResults = contractFilter.filterVaultContracts(results);
    
    expect(vaultResults.length).toBeGreaterThan(0);
  });

  it('should filter airdrop contracts', () => {
    const airdropContract = {
      ...mockContract,
      name: 'Airdrop Contract',
      sourceCode: 'contract Airdrop { function claim() {} }'
    };

    const results = contractFilter.filterContracts([airdropContract]);
    const airdropResults = contractFilter.filterAirdropContracts(results);
    
    expect(airdropResults.length).toBeGreaterThan(0);
  });

  it('should generate filter statistics', () => {
    const results = contractFilter.filterContracts([mockContract]);
    const stats = contractFilter.getFilterStats(results);
    
    expect(stats).toHaveProperty('total');
    expect(stats).toHaveProperty('byPriority');
    expect(stats).toHaveProperty('byChain');
    expect(stats).toHaveProperty('byFunction');
    expect(stats).toHaveProperty('withHighPriorityKeywords');
    
    expect(stats.total).toBe(1);
    expect(stats.byChain[1]).toBe(1);
  });
});
