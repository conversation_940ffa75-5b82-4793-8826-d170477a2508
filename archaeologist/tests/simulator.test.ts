import { contractSimulator } from '../src/simulator';
import { ContractInfo, TargetFunction } from '../src/types';
import { FilterResult } from '../src/filter';
import { jest } from '@jest/globals';

describe('Contract Simulator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockContract: ContractInfo = {
    address: '0x123',
    name: 'Test Contract',
    abi: [{ name: 'testFunction', type: 'function', inputs: [] }],
    sourceCode: 'contract Test {}',
    compiler: 'v0.8.0',
    txHash: '0xabc',
    blockNumber: 12345,
    timestamp: Date.now(),
    chainId: 1
  };

  const mockTargetFunction: TargetFunction = {
    name: 'testFunction',
    signature: 'testFunction()',
    fourByteSignature: '0x12345678',
    description: 'Test function',
    priority: 1
  };

  const mockFilterResult: FilterResult = {
    contract: mockContract,
    matchedFunctions: [mockTargetFunction],
    priorityScore: 1,
    hasHighPriorityKeywords: false
  };

  it('should have provider for Ethereum', () => {
    const provider = contractSimulator.getProvider(1);
    expect(provider).toBeDefined();
  });

  it('should simulate contract functions', async () => {
    const results = await contractSimulator.simulateContract(mockFilterResult);
    expect(Array.isArray(results)).toBe(true);
  });

  it('should handle simulation errors gracefully', async () => {
    const invalidFilterResult: FilterResult = {
      ...mockFilterResult,
      contract: { ...mockContract, chainId: 999 } // Invalid chain
    };

    const results = await contractSimulator.simulateContract(invalidFilterResult);
    expect(Array.isArray(results)).toBe(true);
  });
});

