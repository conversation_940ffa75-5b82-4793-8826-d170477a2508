import { CONFIG, TARGET_FUNCTIONS, HIGH_PRIORITY_KEYWORDS } from '../src/config';
import { ChainConfig, TargetFunction } from '../src/types';

describe('Configuration', () => {
  describe('CONFIG', () => {
    it('should have all required configuration properties', () => {
      expect(CONFIG).toHaveProperty('chains');
      expect(CONFIG).toHaveProperty('targetFunctions');
      expect(CONFIG).toHaveProperty('scanBatchSize');
      expect(CONFIG).toHaveProperty('simulationTimeout');
      expect(CONFIG).toHaveProperty('maxGasPrice');
      expect(CONFIG).toHaveProperty('privateKey');
      expect(CONFIG).toHaveProperty('dbPath');
      expect(CONFIG).toHaveProperty('logLevel');
    });

    it('should have valid chain configurations', () => {
      expect(CONFIG.chains).toBeInstanceOf(Array);
      expect(CONFIG.chains.length).toBeGreaterThan(0);
      
      CONFIG.chains.forEach((chain: ChainConfig) => {
        expect(chain).toHaveProperty('chainId');
        expect(chain).toHaveProperty('name');
        expect(chain).toHaveProperty('rpcUrl');
        expect(chain).toHaveProperty('etherscanApiUrl');
        expect(chain).toHaveProperty('etherscanApiKey');
        
        expect(typeof chain.chainId).toBe('number');
        expect(typeof chain.name).toBe('string');
        expect(typeof chain.rpcUrl).toBe('string');
        expect(typeof chain.etherscanApiUrl).toBe('string');
        expect(typeof chain.etherscanApiKey).toBe('string');
        
        expect(chain.chainId).toBeGreaterThan(0);
        expect(chain.name).toBeTruthy();
        expect(chain.etherscanApiUrl).toMatch(/^https?:\/\//);
      });
    });

    it('should have expected chain IDs', () => {
      const chainIds = CONFIG.chains.map(c => c.chainId);
      expect(chainIds).toContain(1);     // Ethereum
      expect(chainIds).toContain(42161); // Arbitrum
      expect(chainIds).toContain(8453);  // Base
      expect(chainIds).toContain(10);    // Optimism
      expect(chainIds).toContain(137);   // Polygon
    });

    it('should have valid numeric configurations', () => {
      expect(CONFIG.scanBatchSize).toBeGreaterThan(0);
      expect(CONFIG.simulationTimeout).toBeGreaterThan(0);
      expect(typeof CONFIG.maxGasPrice).toBe('bigint');
      expect(CONFIG.maxGasPrice).toBeGreaterThan(0n);
    });
  });

  describe('TARGET_FUNCTIONS', () => {
    it('should have target functions defined', () => {
      expect(TARGET_FUNCTIONS).toBeInstanceOf(Array);
      expect(TARGET_FUNCTIONS.length).toBeGreaterThan(0);
    });

    it('should have valid target function structure', () => {
      TARGET_FUNCTIONS.forEach((func: TargetFunction) => {
        expect(func).toHaveProperty('name');
        expect(func).toHaveProperty('signature');
        expect(func).toHaveProperty('fourByteSignature');
        expect(func).toHaveProperty('description');
        expect(func).toHaveProperty('priority');
        
        expect(typeof func.name).toBe('string');
        expect(typeof func.signature).toBe('string');
        expect(typeof func.fourByteSignature).toBe('string');
        expect(typeof func.description).toBe('string');
        expect(typeof func.priority).toBe('number');
        
        expect(func.name).toBeTruthy();
        expect(func.signature).toMatch(/^[a-zA-Z_][a-zA-Z0-9_]*\(/);
        expect(func.fourByteSignature).toMatch(/^0x[a-fA-F0-9]{8}$/);
        expect(func.priority).toBeGreaterThan(0);
      });
    });

    it('should have priority 1 functions', () => {
      const priority1Functions = TARGET_FUNCTIONS.filter(f => f.priority === 1);
      expect(priority1Functions.length).toBeGreaterThan(0);
      
      const priority1Names = priority1Functions.map(f => f.name);
      expect(priority1Names).toContain('claim');
      expect(priority1Names).toContain('withdraw');
      expect(priority1Names).toContain('exit');
    });

    it('should have unique function signatures', () => {
      const signatures = TARGET_FUNCTIONS.map(f => f.signature);
      const uniqueSignatures = [...new Set(signatures)];
      expect(signatures.length).toBe(uniqueSignatures.length);
    });
  });

  describe('HIGH_PRIORITY_KEYWORDS', () => {
    it('should have high priority keywords defined', () => {
      expect(HIGH_PRIORITY_KEYWORDS).toBeInstanceOf(Array);
      expect(HIGH_PRIORITY_KEYWORDS.length).toBeGreaterThan(0);
    });

    it('should contain expected keywords', () => {
      expect(HIGH_PRIORITY_KEYWORDS).toContain('airdrop');
      expect(HIGH_PRIORITY_KEYWORDS).toContain('vault');
      expect(HIGH_PRIORITY_KEYWORDS).toContain('claim');
      expect(HIGH_PRIORITY_KEYWORDS).toContain('withdraw');
      expect(HIGH_PRIORITY_KEYWORDS).toContain('dao');
    });

    it('should have all lowercase keywords', () => {
      HIGH_PRIORITY_KEYWORDS.forEach(keyword => {
        expect(keyword).toBe(keyword.toLowerCase());
      });
    });
  });
});
