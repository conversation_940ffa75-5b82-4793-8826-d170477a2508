import { scanner } from '../src/scanner';
import { contractFetcher } from '../src/fetcher';
import { ContractInfo } from '../src/types';
import { jest } from '@jest/globals';

jest.mock('../src/fetcher');

describe('Scanner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockContract: ContractInfo = {
    address: '0x123',
    name: 'Test Contract',
    abi: [],
    sourceCode: 'contract Test {}',
    compiler: 'v0.8.0',
    txHash: '0xabc',
    blockNumber: 12345,
    timestamp: Date.now(),
    chainId: 1
  };

  it('should scan all chains', async () => {
    jest.spyOn(contractFetcher, 'fetchRecentVerifiedContracts').mockResolvedValue([mockContract]);

    await scanner.scanAllChains();

    expect(contractFetcher.fetchRecentVerifiedContracts).toHaveBeenCalled();
  });

  it('should handle empty contracts', async () => {
    jest.spyOn(contractFetcher, 'fetchRecentVerifiedContracts').mockResolvedValue([]);

    await scanner.scanAllChains();

    expect(contractFetcher.fetchRecentVerifiedContracts).toHaveBeenCalled();
  });

  it('should handle exceptions during scanning', async () => {
    jest.spyOn(contractFetcher, 'fetchRecentVerifiedContracts').mockRejectedValue(new Error('Fetch failed'));

    await expect(scanner.scanAllChains()).resolves.not.toThrow();
    expect(contractFetcher.fetchRecentVerifiedContracts).toHaveBeenCalled();
  });
});
