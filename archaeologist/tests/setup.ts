import * as dotenv from 'dotenv';
import { join } from 'path';
import { jest } from '@jest/globals';

// Load test environment variables
dotenv.config({ path: join(__dirname, '../.env.test') });

// Mock database operations to avoid real database calls during tests
jest.mock('../src/database', () => ({
  db: {
    saveContract: jest.fn(),
    saveSimulation: jest.fn(),
    saveExploitableContract: jest.fn(),
    hasBeenSimulated: jest.fn(),
    updateScanStatus: jest.fn(),
    getScanStatus: jest.fn(),
    getStats: jest.fn(),
    getUnexecutedExploits: jest.fn(),
    close: jest.fn()
  }
}));

// Mock external API calls
jest.mock('axios');

// Mock global console methods for cleaner test output
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DB_PATH = ':memory:';
process.env.LOG_LEVEL = 'error';
process.env.PRIVATE_KEY = '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
process.env.ETHERSCAN_API_KEY = 'test_api_key';
process.env.ETHEREUM_RPC_URL = 'https://eth-mainnet.alchemyapi.io/v2/test';
process.env.ARBITRUM_RPC_URL = 'https://arb-mainnet.g.alchemy.com/v2/test';
process.env.BASE_RPC_URL = 'https://mainnet.base.org';
process.env.OPTIMISM_RPC_URL = 'https://mainnet.optimism.io';
process.env.POLYGON_RPC_URL = 'https://polygon-mainnet.g.alchemy.com/v2/test';

// Global test timeout
jest.setTimeout(30000);
