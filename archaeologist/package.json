{"name": "contract-archaeologist", "version": "1.0.0", "description": "Find and extract funds from forgotten smart contracts", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "scan": "ts-node src/scanner.ts", "scan:incremental": "ts-node src/scanner.ts incremental", "scan:continuous": "ts-node src/scanner.ts continuous", "scan:metrics": "ts-node src/scanner.ts metrics", "monitor": "ts-node src/scanner.ts monitor", "dashboard": "ts-node src/scanner.ts dashboard", "stats": "ts-node src/scanner.ts stats", "execute": "ts-node src/scanner.ts execute", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["ethereum", "smart-contracts", "defi", "archaeology"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "chalk": "^4.1.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "ethers": "^6.8.0", "express": "^5.1.0", "ora": "^5.4.1", "sqlite3": "^5.1.6", "ws": "^8.18.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^29.5.0", "@types/node": "^20.8.0", "@types/sqlite3": "^3.1.8", "@types/ws": "^8.18.1", "jest": "^29.7.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.0", "typescript": "^5.2.0"}}