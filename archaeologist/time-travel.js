#!/usr/bin/env node

/**
 * 🏺 ARCHA<PERSON>OL<PERSON>GICAL TIME-TRAVEL COMMAND
 * 
 * This script allows the archaeologist to travel back in time and scan
 * specific historical periods for forgotten money.
 * 
 * Usage:
 *   node time-travel.js --year 2020
 *   node time-travel.js --block 9193266 --end-block 9293266
 *   node time-travel.js --year 2017 --chain 1
 */

const { timeTravelScanner, ARCHAEOLOGICAL_PERIODS } = require('./dist/time-travel-scanner.js');

console.log('🏺 ARCHAEOLOGICAL TIME-TRAVEL SCANNER');
console.log('━'.repeat(60));
console.log('');

function showHelp() {
  console.log('🕰️  USAGE:');
  console.log('');
  console.log('Scan by year (default: 2020):');
  console.log('  node time-travel.js --year 2020');
  console.log('  node time-travel.js --year 2017 --chain 1');
  console.log('');
  console.log('Scan by block range:');
  console.log('  node time-travel.js --block 9193266 --end-block 9293266');
  console.log('  node time-travel.js --block 9193266 --end-block 9293266 --chain 1');
  console.log('');
  console.log('Options:');
  console.log('  --year <year>        Scan specific year (2015-2024)');
  console.log('  --block <number>     Start block number');
  console.log('  --end-block <number> End block number');
  console.log('  --chain <id>         Chain ID (default: 1 = Ethereum)');
  console.log('  --batch <size>       Batch size for scanning (default: 100)');
  console.log('  --max-blocks <num>   Maximum blocks to scan (default: 10000)');
  console.log('  --help               Show this help');
  console.log('');
  console.log('🏺 ARCHAEOLOGICAL PERIODS:');
  console.log('');
  
  Object.entries(ARCHAEOLOGICAL_PERIODS).forEach(([year, info]) => {
    console.log(`  ${year}: ${info.name} - ${info.description}`);
  });
  
  console.log('');
  console.log('⛓️  SUPPORTED CHAINS:');
  console.log('  1     = Ethereum (default)');
  console.log('  42161 = Arbitrum');
  console.log('  8453  = Base');
  console.log('  10    = Optimism');
  console.log('  137   = Polygon');
  console.log('');
  console.log('💡 EXAMPLES:');
  console.log('');
  console.log('Hunt DeFi Summer 2020:');
  console.log('  node time-travel.js --year 2020');
  console.log('');
  console.log('Explore ICO Boom 2017:');
  console.log('  node time-travel.js --year 2017');
  console.log('');
  console.log('Scan specific block range:');
  console.log('  node time-travel.js --block 9000000 --end-block 9100000');
  console.log('');
  console.log('Hunt on Arbitrum:');
  console.log('  node time-travel.js --year 2021 --chain 42161');
  console.log('');
}

function parseArgs() {
  const args = process.argv.slice(2);
  const config = {
    chainId: 1, // Default to Ethereum
    batchSize: 100,
    maxBlocks: 10000
  };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
      case '--year':
      case '-y':
        config.year = parseInt(args[++i]);
        break;
      case '--block':
      case '-b':
        config.startBlock = parseInt(args[++i]);
        break;
      case '--end-block':
      case '-e':
        config.endBlock = parseInt(args[++i]);
        break;
      case '--chain':
      case '-c':
        config.chainId = parseInt(args[++i]);
        break;
      case '--batch':
        config.batchSize = parseInt(args[++i]);
        break;
      case '--max-blocks':
        config.maxBlocks = parseInt(args[++i]);
        break;
      default:
        if (args[i].startsWith('-')) {
          console.error(`❌ Unknown option: ${args[i]}`);
          console.log('Use --help for usage information');
          process.exit(1);
        }
        break;
    }
  }

  return config;
}

function validateConfig(config) {
  // Validate year
  if (config.year && (config.year < 2015 || config.year > 2024)) {
    console.error('❌ Year must be between 2015 and 2024');
    process.exit(1);
  }

  // Validate blocks
  if (config.startBlock && config.endBlock && config.startBlock >= config.endBlock) {
    console.error('❌ Start block must be less than end block');
    process.exit(1);
  }

  // Validate chain
  const supportedChains = [1, 42161, 8453, 10, 137];
  if (!supportedChains.includes(config.chainId)) {
    console.error(`❌ Unsupported chain ID: ${config.chainId}`);
    console.error(`Supported chains: ${supportedChains.join(', ')}`);
    process.exit(1);
  }

  return config;
}

async function main() {
  try {
    const config = parseArgs();
    const validatedConfig = validateConfig(config);

    console.log('🚀 INITIATING TIME TRAVEL...');
    console.log('');

    // Show configuration
    console.log('📋 CONFIGURATION:');
    if (validatedConfig.year) {
      const period = ARCHAEOLOGICAL_PERIODS[validatedConfig.year];
      console.log(`   Year: ${validatedConfig.year} (${period ? period.name : 'Unknown Period'})`);
    }
    if (validatedConfig.startBlock) {
      console.log(`   Start Block: ${validatedConfig.startBlock.toLocaleString()}`);
    }
    if (validatedConfig.endBlock) {
      console.log(`   End Block: ${validatedConfig.endBlock.toLocaleString()}`);
    }
    console.log(`   Chain: ${validatedConfig.chainId}`);
    console.log(`   Batch Size: ${validatedConfig.batchSize}`);
    console.log(`   Max Blocks: ${validatedConfig.maxBlocks.toLocaleString()}`);
    console.log('');

    // Start time travel scan
    const result = await timeTravelScanner.scanHistoricalPeriod(validatedConfig);

    // Show final summary
    console.log('🎯 MISSION SUMMARY:');
    console.log(`   Contracts Discovered: ${result.contractsFound.toLocaleString()}`);
    console.log(`   Target Functions Found: ${result.targetsFound.toLocaleString()}`);
    console.log(`   Exploitable Contracts: ${result.exploitableContracts.toLocaleString()}`);
    console.log(`   Total Potential Value: ${result.totalValue} ETH`);
    console.log(`   Scan Duration: ${(result.scanDuration / 1000).toFixed(1)} seconds`);
    console.log('');

    if (result.exploitableContracts > 0) {
      console.log('🎉 ANCIENT TREASURES DISCOVERED!');
      console.log('💀 Check the database for exploitable contracts');
      console.log('⚔️  Ready for extraction operations');
    } else {
      console.log('👻 No exploitable contracts found in this period');
      console.log('💡 Try different time periods or adjust profit thresholds');
    }

    console.log('');
    console.log('🏺 Time travel complete. The ruins have been explored.');
    console.log('');

  } catch (error) {
    console.error('❌ Time travel failed:', error.message);
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('   • Make sure your .env file is configured');
    console.log('   • Check your RPC endpoints are working');
    console.log('   • Verify the block range is valid');
    console.log('   • Try a smaller block range or batch size');
    console.log('');
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}
